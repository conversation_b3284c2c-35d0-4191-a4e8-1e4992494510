# Development files
/.git/
/.github/
/tests/
/bin/
/vendor/
/node_modules/
/coverage/

# Configuration files
/.gitignore
/.distignore
/composer.json
/composer.lock
/package.json
/package-lock.json
/phpunit.xml
/phpcs.xml
/phpstan.neon
/phpstan-bootstrap.php

# Documentation (keep README.md)
/docs/

# Build files
*.log
*.tmp
*.cache

# IDE files
/.vscode/
/.idea/
*.sublime-*

# OS files
.DS_Store
Thumbs.db

# Backup files
*.bak
*.backup
*~

# Test files
*test*
*Test*
*TEST*

# Development assets
/src/
/scss/
/less/
/webpack.config.js
/gulpfile.js
/Gruntfile.js

# Temporary files
/tmp/
/temp/
