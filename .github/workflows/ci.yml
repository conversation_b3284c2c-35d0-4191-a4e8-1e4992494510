name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        php-version: ['8.1', '8.2', '8.3']
        wordpress-version: ['6.4', '6.5', 'latest']
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: wordpress_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-version }}
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, mysql, mysqli, pdo_mysql, bcmath, soap, intl, gd, exif, iconv
        coverage: xdebug

    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-php-${{ matrix.php-version }}-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-${{ matrix.php-version }}-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Install WordPress Test Suite
      run: |
        bash bin/install-wp-tests.sh wordpress_test root root localhost ${{ matrix.wordpress-version }}

    - name: Run PHPUnit tests
      run: composer test

    - name: Run PHPCS
      run: composer cs:check

    - name: Run PHPStan
      run: composer analyze

    - name: Upload coverage reports to Codecov
      if: matrix.php-version == '8.2' && matrix.wordpress-version == 'latest'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        
    - name: Install dependencies
      run: composer install --prefer-dist --no-progress
      
    - name: Security check
      run: |
        composer require --dev roave/security-advisories:dev-latest || true
        
  compatibility:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        
    - name: Install dependencies
      run: composer install --prefer-dist --no-progress
      
    - name: PHP Compatibility check
      run: |
        vendor/bin/phpcs --standard=PHPCompatibilityWP --runtime-set testVersion 8.1- --extensions=php --ignore=vendor/,tests/,node_modules/ .

  build:
    runs-on: ubuntu-latest
    needs: [test, security, compatibility]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Build assets
      run: |
        # If we had build process for assets
        echo "No build process needed for this plugin"
        
    - name: Create release archive
      run: |
        mkdir -p dist
        rsync -av --exclude-from='.distignore' . dist/ordinace/
        cd dist
        zip -r ordinace-plugin.zip ordinace/
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: plugin-build
        path: dist/ordinace-plugin.zip
