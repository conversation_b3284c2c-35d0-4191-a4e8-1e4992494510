# Changelog

Všechny významné změny v tomto projektu budou dokumentovány v tomto souboru.

Formát je založen na [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
a tento projekt dodržuje [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-08-18

### Přidáno
- **Základní funkcionalita pluginu**
  - Custom Post Type `ordinace` pro správu ordinací
  - Taxonomie `ordinace_typ` s typy: praktik-dospeli, pediatr
  - Meta boxy pro detaily ordinací (lékaři, organizace, adresa, kontakty)
  
- **Import a správa dat**
  - Import z Google Sheets (CSV export)
  - Mapování sloupců s flexibilní konfigurací
  - Deduplikace záznamů podle source_row_id
  - WP-CLI příkazy pro import a správu dat
  
- **Geokódování**
  - Podpora pro Nominatim, Google Maps a Mapbox
  - Cache geokódování pro optimalizaci výkonu
  - Ruční geokódování v administraci
  
- **Frontend zobrazení**
  - Úvodní stránka s 3 dlaždicemi (template page-ordinace-landing.php)
  - Archive stránky s profesionálním designem
  - Detail ordinací s kompletními informacemi
  - Responzivní design s Bootstrap kompatibilitou
  
- **Mapy a vizualizace**
  - Interaktivní mapy s Leaflet.js
  - Barevně odlišené značky podle typu ordinace
  - GeoJSON endpoint pro mapová data
  - Clustering pro lepší přehlednost
  
- **Shortcodes**
  - `[ordinace_list]` - seznam ordinací s filtry
  - `[ordinace_map]` - interaktivní mapa
  - Konfigurovatelné parametry (typ, čtvrť, styl zobrazení)
  
- **Administrace**
  - Přehledné admin sloupce s filtry
  - Nastavení pluginu s kartami pro různé sekce
  - CSV import rozhraní s drag & drop
  - Bulk operace a správa záznamů
  
- **Technické funkce**
  - Kompletní i18n podpora (text-domain: spd10-ordinace)
  - Cron úlohy pro automatické aktualizace
  - Cache systém pro optimalizaci výkonu
  - Bezpečnostní opatření (nonce, capability checks)
  - PHPUnit testy pro klíčové funkce

### Technické specifikace
- **Prefix:** `spd10_ordinace_` pro všechny funkce a hooky
- **Text-domain:** `spd10-ordinace`
- **CSS třídy:** `spd10-ordinace-*`
- **Kompatibilita:** PHP 8.1+, WordPress 6.4+
- **Kompatibilní** s pluginem "detske-skupiny" (žádné konflikty)

### Autor
Pavel Roušar - 18. srpna 2025

---

## Plánované verze

### [1.1.0] - Plánováno
- Rozšířené filtry v administraci
- Export dat do CSV/Excel
- Pokročilé vyhledávání

### [1.2.0] - Plánováno  
- REST API endpoints
- Gutenberg bloky
- Pokročilé mapové funkce

### [2.0.0] - Plánováno
- Kompletní refaktoring architektury
- Pokročilé integrace
- Rozšířené možnosti customizace
