# Contributing to SPD10 Ordinace Plugin

Děkujeme za váš zájem o přispívání do SPD10 Ordinace Plugin! Tento dokument obsahuje pokyny pro vývojáře.

## 🚀 R<PERSON>l<PERSON> start

### Požadavky
- PHP 8.1+
- WordPress 6.4+
- Composer
- Node.js 18+ (volitelné)
- MySQL/MariaDB

### Instalace vývojového prostředí

1. **Klonování repozitáře**
```bash
git clone https://github.com/rousarp/ordinace-plugin.git
cd ordinace-plugin
```

2. **Instalace závislostí**
```bash
composer install
```

3. **Nastavení WordPress test suite**
```bash
bin/install-wp-tests.sh wordpress_test root '' localhost latest
```

4. **Spuštění testů**
```bash
composer test
```

## 📋 Vývojové workflow

### 1. Kódovací standardy

Plugin používá WordPress Coding Standards:

```bash
# Kontrola kódovacích standardů
composer cs:check

# Automatická oprava
composer cs:fix
```

### 2. Statická analýza

```bash
# PHPStan analýza
composer analyze
```

### 3. Testování

```bash
# Spuštění všech testů
composer test

# Testy s coverage
composer test:coverage
```

### 4. Kompletní kontrola kvality

```bash
# Spuštění všech kontrol
composer quality
```

## 🏗️ Architektura pluginu

### Struktura souborů

```
ordinace-plugin/
├── ordinace.php              # Hlavní soubor pluginu
├── includes/                 # PHP třídy a funkce
│   ├── class-*.php          # Hlavní třídy
│   ├── interface-*.php      # Rozhraní
│   ├── geocoders/           # Geocoding providery
│   └── *.php               # Ostatní funkce
├── templates/               # Frontend šablony
├── assets/                  # CSS/JS soubory
├── languages/              # Lokalizace
├── tests/                  # Unit a integrační testy
└── bin/                    # Utility skripty
```

### Klíčové třídy

- `SPD10_Ordinace_Plugin` - Hlavní třída pluginu
- `SPD10_Ordinace_Importer` - Import z Google Sheets
- `SPD10_Ordinace_CSV_Importer` - CSV a paste import
- `SPD10_Ordinace_Geocoder` - Geokódování adres
- `SPD10_Ordinace_Template_Loader` - Frontend šablony

### Prefixy a konvence

- **PHP funkce**: `spd10_ordinace_`
- **PHP třídy**: `SPD10_Ordinace_`
- **CSS třídy**: `spd10-ordinace-`
- **JavaScript**: `spd10OrdinaceMap`
- **Options**: `spd10_ordinace_`
- **Text domain**: `spd10-ordinace`

## 🧪 Testování

### Unit testy

```bash
# Spuštění konkrétního testu
vendor/bin/phpunit tests/test-importer.php

# Test s verbose výstupem
vendor/bin/phpunit --verbose
```

### Testovací utility

Použijte `SPD10_Ordinace_Test_Utils` pro:
- Vytváření testovacích dat
- Mock geocoding responses
- Cleanup po testech

### Příklad testu

```php
public function test_import_functionality() {
    $test_data = SPD10_Ordinace_Test_Utils::get_test_csv_data();
    $importer = new SPD10_Ordinace_Importer();
    
    $result = $importer->import_data($test_data, 'test-sheet', true);
    
    $this->assertFalse(is_wp_error($result));
    $this->assertEquals(2, $result['stats']['processed']);
}
```

## 🔧 Hooks a filtry

### Akce (Actions)

```php
// Po importu dat
do_action('spd10_ordinace_after_import', $import_result, $source);

// Po geokódování
do_action('spd10_ordinace_after_geocoding', $post_id, $coordinates);
```

### Filtry (Filters)

```php
// Úprava geocoding výsledku
$result = apply_filters('spd10_ordinace_geocode_result', $result, $address);

// Úprava mapování sloupců
$mapping = apply_filters('spd10_ordinace_column_mapping', $mapping, $headers);
```

## 📝 Commit konvence

Používáme konvenční commit zprávy:

```
feat: přidání nové funkcionalitě
fix: oprava chyby
docs: aktualizace dokumentace
style: formátování kódu
refactor: refaktoring bez změny funkcionalitě
test: přidání testů
chore: údržba
```

Příklady:
```
feat: přidání paste import funkcionalitě
fix: oprava geocoding cache invalidation
docs: aktualizace README s novými funkcemi
```

## 🚦 Pre-commit hooks

Plugin automaticky spouští kontroly před commitem:
- PHPCS (coding standards)
- PHPStan (static analysis)
- PHPUnit (testy)
- Kontrola debug statements

## 🔒 Bezpečnost

### Povinné kontroly

1. **Nonce verification** pro všechny AJAX požadavky
2. **Capability checks** (`manage_options`)
3. **Input sanitization** (`sanitize_text_field`, `sanitize_textarea_field`)
4. **Output escaping** (`esc_html`, `esc_attr`)

### Příklad bezpečného AJAX handleru

```php
public function handle_ajax_request() {
    // Nonce verification
    if (!wp_verify_nonce($_POST['nonce'], 'spd10_ordinace_action')) {
        wp_send_json_error('Security check failed');
    }
    
    // Capability check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
    }
    
    // Input sanitization
    $data = sanitize_text_field($_POST['data']);
    
    // Process and respond
    wp_send_json_success($result);
}
```

## 🌐 Lokalizace

### Přidání nových textů

```php
// Použití
__('Text k překladu', 'spd10-ordinace')
_e('Text k překladu', 'spd10-ordinace')

// Plurály
_n('singular', 'plural', $count, 'spd10-ordinace')
```

### Generování .pot souboru

```bash
wp i18n make-pot . languages/spd10-ordinace.pot
```

## 📊 Performance

### Cachování

- Používejte WordPress transients pro dočasné cache
- GeoJSON data jsou cachována pro rychlé načítání map
- Import výsledky jsou logovány pro debugging

### Optimalizace

- Lazy loading pro mapy
- Batch processing pro bulk operace
- Rate limiting pro API calls

## 🐛 Debugging

### Logging

```php
// Error logging
error_log('SPD10 Ordinace: ' . $message);

// WordPress debug log
if (WP_DEBUG_LOG) {
    error_log('Debug info: ' . print_r($data, true));
}
```

### Debug konstanty

```php
// wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('SCRIPT_DEBUG', true);
```

## 📞 Podpora

- **Issues**: GitHub Issues
- **Dokumentace**: README.md, PRD.md
- **Kód**: Inline komentáře a PHPDoc

## 🎯 Roadmap

Viz `tasks.md` pro aktuální stav a plánované funkce.

---

**Děkujeme za vaše příspěvky! 🙏**
