## PRD – WordPress plugin „Ordinace“ (Strategie pro Desítku)

### 1) Kontext a cíl
Cílem je vytvořit profesionální WordPress plugin „Ordinace“, který publikuje a udržuje databázi ordinací praktických lékařů a pediatrů na Praze 10. Plugin je odvozen z existujícího pluginu „detske-skupiny“ a musí s ním umět běžet paralelně (bez kolizí názvů, hook<PERSON>, stylů a konfiguračních klíčů). Výstupem je plnohodnotný katalog s přehledovým seznamem, mapou a administrací včetně importu z Google Sheets a geokódování.

- Web: Strategie pro Desítku
- Zdroj dat: Google Sheets (2 záložky – „Praktičtí lékaři“, „Pediatři“)
- Odkaz: https://docs.google.com/spreadsheets/d/1SnwP4sFp1OrWYlZxrzVJs9kZVdBFLgic4FDzlguts7k/edit?gid=0#gid=0

### 2) Uživatelé a potřeby
- Návštěvník webu: rychle najít praktického lékaře/pediatra v konkrétní čtvrti; zobrazit kontakt, adresu, e‑mail; použít mapu; stránkování po 10 záznamech.
- Redaktor/administrátor: snadno importovat/aktualizovat data z Google Sheets, ručně upravovat záznamy, vynutit geokódování, filtrovat záznamy v administraci.

### 3) Rozsah (Scope) – In / Out
In:
- Nový plugin „ordinace“ s vlastním jmenným prostorem/prefixy; souběh s „detske-skupiny“.
- Vytvoření CPT „ordinace“ a taxonomie „ordinace_typ“ (minimálně typy: „praktik-dospeli“, „pediatr“).
- Frontend: úvodní stránka se 3 dlaždicemi, seznamy (dospělí/pediatři) se stránkováním (výchozí 10/str.), detail ordinace, mapa s barevně odlišenými značkami dle typu.
- Backend: editace a vkládání záznamů; import ze Sheets; geokódování; nastavení pluginu.
- Import: mapování sloupců, deduplikace, validace, logování, „dry-run“ režim, WP‑CLI příkaz.
- Geokódování: konfigurovatelný provider, cache výsledků, náhled polohy v administraci.
- Konfigurace: žádné hodnoty napevno – vše přes stránku Nastavení (ID sheetu, názvy listů, API klíče, poskytovatel map, stránkování, barvy značek, šablony/shortcody/bloky).
- Bezpečnost, i18n, výkon (cache/transients), testování (unit/integration), dokumentace.



### 4) Datový model (CPT + taxonomie + metadatová pole)
CPT: „ordinace“ (slug konfigurovatelný, výchozí „ordinace“)
Taxonomie: „ordinace_typ“ (hierarchie: ne)
- Termíny: „praktik-dospeli“, „pediatr“ (editovatelné v administraci)

Doporučená metadatová pole u CPT (s typovými nápovědami a validací):
- doctors_names: seznam jmen lékařů (1..N, řádkově)
- organization: název organizace
- quarter: čtvrť (text/enum dle potřeby)
- address: adresa (string)
- email: kontaktní e‑mail (validace formátu)
- phone: telefon (volitelné)
- website: web (volitelné, validace URL)
- lat, lng: souřadnice (float)
- geocode_status: stav geokódování (OK/PENDING/FAILED + message)
- source_row_id: identifikátor řádku ve Sheets pro idempotentní sync
- updated_from_source_at: datetime posledního importu

### 5) Zdroj dat a mapování
**Primární zdroj:** Google Sheets se 2 listy: „Praktičtí lékaři“, „Pediatři“.
- Přístup: přes veřejný CSV export nebo API (konfigurovatelně). ID sheetu a názvy listů jsou v nastavení pluginu.
- Mapování sloupců: nastavitelné v administraci (minimálně: Jméno lékaře, Organizace, Čtvrť, Adresa, E‑mail, Telefon, Web). Pro více lékařů v jedné ordinaci se jména spojí do pole doctors_names (oddělovač konfigurovatelný).
- Deduplikace: podle source_row_id; sekundárně heuristika (adresy + organizace + jména).

**Alternativní zdroj:** CSV soubor upload
- Možnost nahrání CSV souboru přes WordPress admin jako alternativa k Google Sheets
- Jeden CSV soubor obsahuje všechny ordinace s novým sloupcem `kategorie_ordinace`
- Hodnoty v sloupci `kategorie_ordinace`: "pediatr" nebo "praktik-dospeli"
- Zachování zpětné kompatibility s existujícím Google Sheets importem
- Stejné mapování sloupců a deduplikace jako u Google Sheets
- Validace CSV struktury před importem s user-friendly chybovými zprávami

### 6) Importní workflow
**Google Sheets import:**
- Admin UI: stránka Import v rámci Nastavení pluginu: náhled mapování, volba listu (dospělí/pediatři), dry‑run (zobrazí, co se vytvoří/aktualizuje/smaže), ostrý import.
- WP‑CLI: příkaz typu `wp ordinace import --sheet=<id> --tab=<name> --dry-run`.

**CSV import (nová funkcionalita):**
- Admin UI: nová karta "CSV Import" v nastavení pluginu
- Upload CSV souboru přes WordPress media library nebo custom upload
- Automatická detekce sloupců s možností ručního mapování
- Validace CSV struktury a povinného sloupce `kategorie_ordinace`
- Stejný dry-run režim jako u Google Sheets importu
- WP‑CLI: rozšíření příkazu `wp ordinace import --csv=<file-path> --dry-run`

**Společné funkce:**
- Validace: povinná pole (adresa, alespoň jedno jméno nebo organizace), validní e‑mail/URL.
- Logování: přehled změn (create/update/skip/fail), export do CSV/JSON.
- Plánování: volitelný cron (vypnuto ve výchozím stavu; plán a frekvence konfigurovatelné).
- Deduplikace: jednotný systém pro oba typy importu.

### 6a) CSV formát specifikace
**Povinné sloupce:**
- `kategorie_ordinace` - hodnoty: "pediatr" nebo "praktik-dospeli" (nahrazuje rozdělení na 2 záložky)
- `jmena_lekaru` - jména lékařů (každé na nový řádek nebo oddělené čárkou)
- `organizace` - název organizace/ordinace
- `ctvrt` - čtvrť na Praze 10
- `adresa` - úplná adresa pro geokódování

**Volitelné sloupce:**
- `email` - kontaktní e-mail
- `telefon` - kontaktní telefon
- `web` - webové stránky
- `poznamky` - dodatečné informace

**Technické sloupce (volitelné):**
- `source_row_id` - unikátní identifikátor řádku pro deduplikaci
- `updated_at` - datum poslední aktualizace

**Formát souboru:**
- Kódování: UTF-8
- Oddělovač: čárka (,) nebo středník (;)
- Uvozovky: dvojité (") pro hodnoty obsahující oddělovač
- První řádek: názvy sloupců (header)

### 7) Geokódování
- Provider konfigurovatelný (např. Nominatim/OSM, Google Geocoding API, Mapbox). Klíče se neukládají v kódu; pouze v nastavení.
- Caching: výsledky (lat,lng) ukládat k záznamu + transients pro opětovné dotazy.
- Retry a limity: backoff při 429/5xx, denní limit, ruční „Znovu geokódovat“ akce v admin.
- Náhled: v editaci CPT malá mapa se zobrazením pin‑u a možností ruční korekce lat/lng.

### 8) Frontend (UX/UI a navigace)
- Úvodní přehled: 3 dlaždice – „Praktičtí lékaři pro děti a dorost“, „Praktičtí lékaři pro dospělé“, „Mapa praktických lékařů na Praze 10“ (https://strategieprodesitku.cz/lekari).
- Seznamy: výpis po 10 záznamech/stránka (konfigurovatelné), filtr dle čtvrti a typu, vyhledávání (volitelně).
- Karta/teaser záznamu: první řádek jméno lékaře (více lékařů pod sebou), dále čtvrť; akce „Více informací“.
- Detail: zobrazit Organizace, Kontakt na lékaře (jména), Čtvrť, Adresa, E‑mail (+ telefon, web, pokud dostupné), mini‑mapa.
- Mapa: značky odlišeny barvou dle typu („pediatr“ vs „praktik‑dospeli“), legenda, možnost filtrování. Poskytovatel map konfigurovatelný (výchozí Leaflet + OSM). Export GeoJSON cache pro rychlé načítání.
- Vkládání do obsahu: shortcody a/nebo Gutenberg bloky (např. [ordinace_list typ="pediatr" per_page="10"], [ordinace_map typ="*" dataset="cache"]).
- Šablony: možnost přepsat přes theme (template‑parts); CSS třídy s prefixem pluginu.

### 9) Backend (administrace)
- CPT „Ordinace“: sloupce v přehledu (Jména, Organizace, Čtvrť, Typ, Adresa, Geocode), filtry dle typu/čtvrti.
- Editace: validace e‑mail/URL; tlačítko „Geokódovat“; náhled mapy; term „ordinace_typ“ volitelně předvyplnit podle importovaného listu/CSV.
- Nastavení: karta „Zdroj dat“, „Geokódování“, „Mapa a frontend“, „Cron & výkon“, „Diagnostika & logy“.

### 10) Architektonické požadavky
- Souběh s pluginem „detske‑skupiny“: odlišný slug, text‑domain, PHP namespace, prefixed function names, option keys, skripty a styly (např. prefix „spd10_ordinace_“).
- Kódová organizace: malé, jednopurpose třídy (Service, Repository, Importer, Geocoder, AdminUI, FrontendUI). Žádné hard‑kódy – vše konfigurovatelné přes Options API.
- Bezpečnost: capability checks (např. manage_options / custom capability), nonce, sanitizace/escapování, omezení přístupu k importu a geokódování.
- Výkon: transients pro seznamy a mapová data, invalidace po importu/editaci; lazy‑load scriptů jen na relevantních stránkách.
- Lokalizace: text‑domain „spd10-ordinace“, .pot soubor.
- Kompatibilita: PHP 8.1+, WP 6.4+, běžná témata; žádné přímé změny DB mimo WP API.

### 11) Konfigurace (bez hard‑kodů)
Konfigurovat přes stránku Nastavení:
- Google Sheet ID + názvy listů, způsob načítání (CSV/API), mapování sloupců.
- Geocoder provider + API klíč(e), limity, přesnost.
- Map provider (Leaflet/OSM, Google Maps, Mapbox), barvy značek pro typy, počáteční extent mapy.
- Stránkování, výchozí filtry, šablony/shortcody/bloky.
- Cron harmonogram a notifikace (volitelné).
Vše ukládat do wp_options s prefixem „spd10_ordinace_…“; klíče nikdy nelogovat.

### 12) Testování a kvalita
- Automatické testy: PHPUnit (WordPress test suite) – import (dry‑run, dedup, mapping), geokódování (mock provider rozhraní), generování GeoJSON, zobrazení shortcode.
- Integrační testy: WP‑CLI příkaz, admin obrazovky (smoke), i18n načítání.
- Manuální QA: checklist pro seznam, detail, mapa, import, geokódování, stránkování, filtry, souběh s „detske‑skupiny“.
- Linting: PHPCS (WordPress Coding Standards), statická analýza (Psalm/phpstan – úroveň rozumná pro WP kód).

### 13) Akceptační kritéria
- [ ] Aktivní plugin „ordinace“ koexistuje s „detske‑skupiny“ bez konfliktů (namespace, hooky, asset handle, options, šablony).
- [ ] CPT „ordinace“ + taxonomie „ordinace_typ“ vytvořeny a viditelné v admin.
- [ ] Import ze Sheets: dry‑run, ostrý běh, deduplikace, mapping, logování; WP‑CLI příkaz funkční.
- [ ] Geokódování funkční (ruční i při importu), cache, malé admin náhledy mapy.
- [ ] Frontend: úvodní 3 dlaždice, seznamy (10/str. – konfigurovatelné), detail, mapa s barevnými značkami a legendou.
- [ ] Shortcody/bloky fungují a lze je vložit do stránek.
- [ ] Bezpečnost (nonce, capability), výkon (cache/transients), i18n, dokumentace.

### 14) Rizika a mitigace
- Limity API/kvóty geokódování → cache, backoff, možnost ruční korekce lat/lng.
- Změna struktury Sheetů → konfigurovatelné mapování, validační kroky s chybami před importem.
- Výkon mapy při větším množství bodů → GeoJSON cache, případně clustering (volitelné).

### 15) Milníky (návrh)
1. Skeleton pluginu, CPT/Tax, nastavení, i18n, bezpečný prefix/namespace.
2. Importer (UI + WP‑CLI), mapování, deduplikace, logy.
3. Geocoder vrstva + admin náhled; uložení lat/lng.
4. Frontend šablony: úvodní dlaždice, seznamy, detail, shortcody/bloky.
5. Mapa + GeoJSON cache + legenda + filtry.
6. Testy, lint, dokumentace, výkon a bezpečnost.

### 16) Přílohy
- Google Sheet: viz odkaz výše; listy: „Praktičtí lékaři“, „Pediatři“.
- Obrázky/ikony: dodá grafička (bude doplněno), ukládat jako mediální soubory a konfigurovat v Nastavení.

---
Původní zadání ponecháno níže pro trasovatelnost.

obracím se na Vás s žádostí o vytvoření stránky/záložky na webu Strategie pro Desítku, kam bychom rádi umístili databázi kontaktů všech praktických lékařů i pediatrů ordinujících na Praze 10. Formát i uspořádání by byly obdobné jako u databáze dětských skupin.
Jedná se o php plugin do wordpressu. Načti a prostuduj zdrojový kód pluginu detske-skupiny.php, který transformuj do nového pluginu ordinace.php tak, aby oba pluginy mohly být aktivní ve stejnou dobu.
Plugin vytvoří novou CPT v databázi (ordinace, ordinace_typ), do které se uloží data z tabulky prakticni_lekari a pediatri, která je v google docs. Plugin má tři části - zobrazení záznamů, zobrazení mapy s jednotlivými záznamy ordinací praktických lékařů a pediatrů (odlišné barvy pro oba typy ordinací)a editace a vkládání záznamů v backendu včetně geokódování.
v google docs nalezneš tabulku se záznamy, které je nutné importovat do tabulky ordinace a geokódovat.


https://docs.google.com/spreadsheets/d/1SnwP4sFp1OrWYlZxrzVJs9kZVdBFLgic4FDzlguts7k/edit?gid=0#gid=0

záložky Praktiční lékaři, Pediatři

zde jsou doplňující informace:
Na úvodní stránce by byly 3 dlaždice (obdobně jako u dětských skupin) – „Praktičtí lékaři pro děti a dorost“; „Praktičtí lékaři pro dospělé“; „Mapa praktických lékařů na Praze 10 “. Bylo by prosím možné v mapě rozlišit například barvou praporků, jestli se jedná o praktické lékaře pro děti nebo pro rodiče.


Po rozkliknutí výše popsaných dlaždic prosím, aby měl každý lékař svojí „rozklikávací ikonu“ stejně jako u dětských skupin (viz foto níže).


Úvodní fotografii pro všechny ikony Vám dodatečně zašlu, jakmile ji grafička zpracuje.



Prosím, aby na prvním řádku bylo jméno lékaře. V případě, že je v 1 ordinaci více lékařů, tak všechny pod sebou (viz vzor). Dále kolonka čtvrť.



Prosím, aby se po rozkliknutí „více informací“ zobrazovala pole – Organizace, Kontakt na lékaře, Čtvrť, Adresa a E-mail.



Mohlo by na každé stránce být prosím pro přehlednost 10 lékařů?


CHCI SAMOSTATNÉ TESTOVÁNÍ, profesionální plugin

---

## ROZŠÍŘENÍ: CSV Import funkcionalita

### Motivace
Alternativa k Google Sheets pro uživatele, kteří preferují lokální soubory nebo nemají přístup k Google Sheets API.

### Implementace
- **Nová karta "CSV Import"** v admin nastavení pluginu
- **Upload přes WordPress media library** s podporou .csv souborů
- **Automatická detekce** kódování (UTF-8, Windows-1250) a oddělovače (čárka, středník, tabulátor)
- **Validace povinných sloupců** před importem
- **Stejný mapovací systém** jako u Google Sheets
- **Dry-run režim** s náhledem změn
- **Integrace s existujícím** logováním a deduplikací

### Nový sloupec kategorie_ordinace
- **Nahrazuje rozdělení** na 2 Google Sheets záložky
- **Povinný sloupec** s hodnotami: "pediatr" nebo "praktik-dospeli"
- **Automatické přiřazení** taxonomie "ordinace_typ" podle hodnoty
- **Zpětná kompatibilita:** Google Sheets import zůstává beze změny

### CSV struktura
```
kategorie_ordinace,jmena_lekaru,organizace,ctvrt,adresa,email,telefon,web,poznamky
pediatr,"Dr. Jana Nováková",Dětská ordinace,Vršovice,"Korunní 1234, Praha 10",<EMAIL>,+420123456789,www.ordinace.cz,Specializace na alergologie
praktik-dospeli,"Dr. Petr Svoboda",Praktická ordinace,Strašnice,"Průmyslová 567, Praha 10",<EMAIL>,+420987654321,www.prakticka.cz,Preventivní prohlídky
```

### Bezpečnost
- **Validace MIME typu** uploadovaného souboru
- **Omezení velikosti** souboru (max 10MB)
- **Sanitizace všech** importovaných dat
- **Capability check** pro upload a import operace
