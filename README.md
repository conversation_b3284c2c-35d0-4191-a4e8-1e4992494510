# WordPress Plugin: Ordinace (Strategie pro Desítku)

Plugin pro správu databáze ordinací praktických lékařů a pediatrů na Praze 10. Kompatibilní s existujícím pluginem "Dětské skupiny".

## 📋 Popis

Plugin umožňuje:
- Import dat z Google Sheets (CSV export)
- **NOVÉ:** Import z CSV souborů nebo copy-paste z tabulek
- Správu ordinací praktických lékařů a pediatrů
- Geokódování adres s podporou více providerů
- Zobrazení na interaktivní mapě s barevným rozlišením typů ordinací
- Automatické importy podle harmonogramu
- WP-CLI příkazy pro správu

## 🚀 Stav implementace

### ✅ Milník 1: Skeleton pluginu (DOKONČENO)
- Základní struktura pluginu s prefixy `spd10_ordinace_`
- CPT "ordinace" a taxonomie "ordinace_typ"
- Meta boxes pro všechna metadatová pole
- Základní shortcodes a lokalizace
- Frontend CSS styly

### ✅ Milník 2: Importer (DOKONČENO)
- Google Sheets connector s cache systémem
- Mapování sloupců s auto-detekcí
- Import engine s deduplikací a dry-run režimem
- Admin UI pro import s test připojení
- WP-CLI příkazy (import, status, clear-logs, test-connection, clear-cache)
- Cron scheduling s email notifikacemi

### ✅ Milník 3: Geocoder (DOKONČENO)
- Geocoding providers (Nominatim, Google Maps API, Mapbox API)
- Databázová cache s statistikami a správou
- Admin náhled map v editaci CPT s Leaflet.js
- Bulk geocoding operace s progress barem
- AJAX handlery pro single a bulk geocoding
- Rate limiting a retry logika

### 🔄 Milník 4: Frontend šablony (PŘIPRAVENO)
- Úvodní stránka se 3 dlaždicemi
- Seznamy ordinací se stránkováním
- Detail ordinace s mini-mapou
- Shortcodes a Gutenberg bloky

### 🔄 Milník 5: Mapa (PŘIPRAVENO)
- Konfigurovatelný mapový provider
- GeoJSON cache pro výkon
- Barevné značky podle typu ordinace
- Filtry a legenda

### 🔄 Milník 6: QA (PŘIPRAVENO)
- Unit a integrační testy
- Code quality (PHPCS, statická analýza)
- Výkon a bezpečnost
- Dokumentace

## 📦 Instalace

1. Nahrajte plugin do složky `/wp-content/plugins/ordinace/`
2. Aktivujte plugin v administraci WordPressu
3. Přejděte do Nastavení → Ordinace
4. Nastavte ID Google Sheets a názvy listů
5. Spusťte test připojení
6. Proveďte první import (doporučujeme dry-run)

## ⚙️ Konfigurace

### Google Sheets
- ID Google Sheets: Nastavte v administraci pluginu
- Listy: "Praktičtí lékaři", "Pediatři" (konfigurovatelné)
- Přístup: veřejný CSV export

### Mapování sloupců
Plugin automaticky detekuje sloupce podle názvů:
- **Jména lékařů**: "Jméno lékaře", "Lékaři", "Jména"
- **Organizace**: "Organizace", "Název", "Ordinace"
- **Čtvrť**: "Čtvrť", "Lokalita", "Oblast"
- **Adresa**: "Adresa" (povinné)
- **E-mail**: "E-mail", "Email", "Kontakt"
- **Telefon**: "Telefon", "Phone", "Tel"
- **Web**: "Web", "Website", "URL"

## 🖥️ WP-CLI příkazy

```bash
# Import dat
wp ordinace import --tab="Praktičtí lékaři" --dry-run
wp ordinace import --tab="Pediatři" --verbose
wp ordinace import --sheet=YOUR_SHEET_ID --tab="Praktičtí lékaři"

# Stav a statistiky
wp ordinace status
wp ordinace status --logs

# Správa
wp ordinace test-connection
wp ordinace clear-cache
wp ordinace clear-logs
```

## 🔧 Technické detaily

### Prefixy a kompatibilita
- **Prefix funkcí**: `spd10_ordinace_`
- **Text-domain**: `spd10-ordinace`
- **Options prefix**: `spd10_ordinace_`
- **CSS třídy**: `spd10-ordinace-`
- **Kompatibilní** s pluginem "detske-skupiny"

### Databáze
- **CPT**: `ordinace`
- **Taxonomie**: `ordinace_typ`
- **Termíny**: `praktik-dospeli`, `pediatr`

### Cache systém
- WordPress transients (1 hodina)
- Cache klíče s MD5 hash
- Automatická invalidace při změnách

### Bezpečnost
- Nonce ověření pro všechny formuláře
- Capability checks (`manage_options`)
- Sanitizace všech vstupů
- Escapování výstupů

## 📊 Shortcodes

### Seznam ordinací - [ordinace_list]

```php
// Základní použití
[ordinace_list]

// S filtry
[ordinace_list typ="pediatr" per_page="10" ctvrt="Vinohrady"]

// S interaktivními filtry
[ordinace_list show_filters="true"]

// S předvyplněným vyhledáváním
[ordinace_list search="Urbanová" show_filters="true"]

// Kombinace všech parametrů
[ordinace_list typ="praktik-dospeli" per_page="6" ctvrt="Vinohrady" style="cards" show_filters="true" search=""]
```

**Parametry:**
- `typ` - Filtr podle typu ordinace (`pediatr`, `praktik-dospeli`)
- `per_page` - Počet zobrazených ordinací (výchozí: 10)
- `ctvrt` - Filtr podle čtvrti (např. "Vinohrady")
- `style` - Styl zobrazení (`cards` nebo `simple`, výchozí: `cards`)
- `show_filters` - Zobrazit interaktivní filtry (`true`/`false`, výchozí: `false`)
- `search` - Předvyplněné textové vyhledávání

**Responzivní layout:**
- **Desktop (1200px+)**: 3 karty vedle sebe
- **Tablet (768-1199px)**: 2 karty vedle sebe
- **Mobil (do 767px)**: 1 karta

### Mapa ordinací - [ordinace_map]

```php
// Základní mapa
[ordinace_map]

// Mapa s parametry
[ordinace_map typ="pediatr" height="500px" show_filters="true"]
```

**Parametry:**
- `typ` - Filtr podle typu ordinace
- `height` - Výška mapy (výchozí: 400px)
- `show_filters` - Zobrazit filtry mapy (`true`/`false`)
- `show_legend` - Zobrazit legendu (`true`/`false`)
- `zoom` - Úroveň přiblížení

## 🗂️ Struktura souborů

```
ordinace/
├── ordinace.php                    # Hlavní soubor pluginu
├── includes/
│   ├── ordinace-post-types.php     # CPT a taxonomie
│   ├── meta-boxes.php              # Meta boxes
│   ├── shortcodes.php              # Shortcodes
│   ├── admin-settings.php          # Admin nastavení
│   ├── class-sheets-connector.php  # Google Sheets connector
│   ├── class-column-mapper.php     # Mapování sloupců
│   ├── class-importer.php          # Import engine
│   ├── class-cli-commands.php      # WP-CLI příkazy
│   └── class-cron-scheduler.php    # Cron scheduling
├── templates/
│   └── metabox-ordinace-details.php # Template pro meta box
├── assets/
│   └── css/
│       └── spd10-ordinace-frontend.css # Frontend styly
└── languages/
    └── spd10-ordinace.pot          # Překlady
```

## 🔗 Odkazy

- **Cílová URL**: https://strategieprodesitku.cz/lekari
- **PRD**: Viz `PRD.md`
- **Tasks**: Viz `tasks.md`
- **Google Sheets**: Nastavte ID v administraci pluginu

## 📝 Changelog

### v1.0.0 (2024-08-15)
- ✅ Milník 1: Skeleton pluginu dokončen
- ✅ Milník 2: Importer dokončen
- ✅ Milník 3: Geocoder dokončen
- 🔄 Připraveno pro milník 4: Frontend šablony

## 👥 Autoři

- **Vývojář**: AI Agent (Claude Sonnet 4)
- **Zadavatel**: Pavel Roušar
- **Projekt**: Strategie pro Desítku

## 📄 Licence

GPL v2 or later
