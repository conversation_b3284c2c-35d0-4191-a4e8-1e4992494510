/* Moderní styly pro dětské skupiny */

/* Základní animace a přechody */
.card, .list-group-item, .btn, .bg-white {
    transition: all 0.3s ease;
}

/* Hover efekty pro karty */
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}

/* Modernější v<PERSON>hled tlačítek */
.btn-primary {
    background: linear-gradient(45deg, #007bff, #00c6ff);
    border: none;
    border-radius: 30px;
    padding: 8px 20px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0069d9, #00a6ff);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
    transform: translateY(-2px);
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #868e96);
    border: none;
    border-radius: 30px;
    padding: 8px 20px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.2);
}

.btn-secondary:hover {
    background: linear-gradient(45deg, #5a6268, #727b84);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
    transform: translateY(-2px);
}

/* Moderněj<PERSON>í boxy */
.bg-white {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    overflow: hidden;
}

/* Proužkovaná tabulka s menšími řádky */
.list-group-item {
    padding: 0.5rem 1rem;
    border-left: none;
    border-right: none;
}

.list-group-item:nth-child(odd) {
    background-color: rgba(0,0,0,0.02);
}

.list-group-item:hover {
    background-color: rgba(0,123,255,0.05);
}

/* Zarovnání horní hrany obrázku a mapy */
@media (min-width: 768px) {
    .ds-image-container {
        margin-top: 0;
    }
    .ds-map-container {
        margin-top: 0;
    }
}

/* Styl pro nadpisy */
.h4 {
    position: relative;
    padding-bottom: 10px;
}

.h4:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(45deg, #007bff, #00c6ff);
    border-radius: 3px;
}

/* Styl pro galerii */
.ds-gallery .col-md-4 {
    transition: all 0.3s ease;
}

.ds-gallery .col-md-4:hover {
    transform: scale(1.05);
}

.ds-gallery img {
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

/* Animace pro načítání stránky */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.ds-details, .ds-gallery, .entry-content {
    animation: fadeIn 0.6s ease-out forwards;
}

.ds-details {
    animation-delay: 0.2s;
}

.ds-gallery {
    animation-delay: 0.4s;
}

/* Styl pro mapu */
#ds-map {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

/* Styl pro vysvětlující text */
.has-small-font-size {
    font-size: 0.85rem;
    line-height: 1.5;
    color: #6c757d;
}

.has-small-font-size a {
    color: #007bff;
    text-decoration: none;
    position: relative;
}

.has-small-font-size a:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 1px;
    bottom: 0;
    left: 0;
    background-color: #007bff;
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease-out;
}

.has-small-font-size a:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
}
