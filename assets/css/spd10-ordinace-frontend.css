/**
 * Professional Frontend Styles for SPD10 Ordinace Plugin
 *
 * @package SPD10_Ordinace
 */

/* Global Styles */
.spd10-ordinace-list,
.spd10-ordinace-grid,
.spd10-ordinace-single-container,
.spd10-ordinace-archive-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Správné formátování českých textů - žádná velká písmena v každém slově */
.spd10-ordinace-card-badge,
.spd10-ordinace-type-badge,
.spd10-ordinace-landing-card-title,
[class*="spd10-ordinace"] .badge,
[class*="spd10-ordinace"] .type-name {
    text-transform: none !important;
}

/* Shortcode List Styles - Professional Cards with Responsive Layout */
.spd10-ordinace-list,
.spd10-ordinace-grid {
    display: grid;
    gap: 25px;
    margin: 30px 0;
}

/* Default: 1 column for mobile-first approach */
.spd10-ordinace-list,
.spd10-ordinace-grid {
    grid-template-columns: 1fr;
}

/* Tablet: 2 columns */
@media (min-width: 768px) {
    .spd10-ordinace-list,
    .spd10-ordinace-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

/* Desktop: 3 columns */
@media (min-width: 1200px) {
    .spd10-ordinace-list,
    .spd10-ordinace-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 25px;
    }
}

.spd10-ordinace-item,
.spd10-ordinace-card-item {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    animation: slideUp 0.6s ease-out;
}

.spd10-ordinace-item:hover,
.spd10-ordinace-card-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.spd10-ordinace-item h3 {
    margin: 0;
    padding: 20px 20px 15px;
    color: #2d3748;
    font-size: 1.3rem;
    font-weight: 700;
    border-bottom: 1px solid #f0f0f0;
}

.spd10-ordinace-item h3 a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.spd10-ordinace-item h3 a:hover {
    color: #667eea;
}

.spd10-ordinace-item-content {
    padding: 20px;
}

.spd10-ordinace-doctors {
    margin-bottom: 15px;
}

.spd10-ordinace-doctor-name {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 8px;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 8px;
    font-size: 0.9rem;
}

.spd10-ordinace-doctor-name::before {
    content: '👨‍⚕️';
    font-size: 1.1rem;
}

.spd10-ordinace-quarter,
.spd10-ordinace-organization {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 10px 0;
    color: #4a5568;
    font-size: 0.9rem;
    padding: 8px 0;
}

.spd10-ordinace-quarter::before {
    content: '📍';
    font-size: 1rem;
}

.spd10-ordinace-organization::before {
    content: '🏥';
    font-size: 1rem;
}

.spd10-ordinace-quarter strong,
.spd10-ordinace-organization strong {
    color: #2d3748;
    font-weight: 600;
}

.spd10-ordinace-more-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    margin-top: 20px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-sizing: border-box;
}

.spd10-ordinace-more-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

.spd10-ordinace-more-info::after {
    content: '→';
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.spd10-ordinace-more-info:hover::after {
    transform: translateX(4px);
}

.spd10-ordinace-no-results {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    color: #4a5568;
    margin: 30px 0;
}

.spd10-ordinace-no-results::before {
    content: '🔍';
    font-size: 3rem;
    display: block;
    margin-bottom: 20px;
}

/* Map Placeholder Styles */
.spd10-ordinace-map-placeholder {
    border-radius: 12px;
    color: #4a5568;
    font-style: italic;
    background: #f8fafc;
    border: 2px dashed #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

/* Professional Card Styles for Shortcode */
.spd10-ordinace-card-image {
    position: relative;
    height: 120px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.spd10-ordinace-card-illustration {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 10px;
}

.spd10-ordinace-card-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    color: #2d3748;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: none;
}

.spd10-ordinace-card-content {
    padding: 20px;
}

.spd10-ordinace-card-title {
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    font-weight: 700;
    color: #2d3748;
}

.spd10-ordinace-card-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.spd10-ordinace-card-title a:hover {
    color: #667eea;
}

.spd10-ordinace-card-location,
.spd10-ordinace-card-doctors {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: #4a5568;
    font-size: 0.9rem;
}

.spd10-ordinace-card-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    color: #667eea;
}

.spd10-ordinace-card-doctors-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.spd10-ordinace-card-doctor {
    font-weight: 500;
    color: #2d3748;
}

.spd10-ordinace-card-more {
    font-size: 0.8rem;
    color: #667eea;
    font-style: italic;
}

.spd10-ordinace-card-contact {
    margin: 15px 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.spd10-ordinace-card-contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.spd10-ordinace-card-contact-item a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.spd10-ordinace-card-contact-item a:hover {
    color: #4c51bf;
    text-decoration: underline;
}

.spd10-ordinace-card-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.spd10-ordinace-card-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.spd10-ordinace-card-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

.spd10-ordinace-card-arrow {
    width: 16px;
    height: 16px;
    transition: transform 0.3s ease;
}

.spd10-ordinace-card-button:hover .spd10-ordinace-card-arrow {
    transform: translateX(4px);
}

/* Animation Keyframes */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design - 3-2-1 Layout */
@media (max-width: 1200px) {
    .spd10-ordinace-list,
    .spd10-ordinace-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

@media (max-width: 767px) {
    .spd10-ordinace-list,
    .spd10-ordinace-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        margin: 20px 0;
    }

    .spd10-ordinace-item h3 {
        font-size: 1.2rem;
        padding: 15px 15px 12px;
    }

    .spd10-ordinace-item-content {
        padding: 15px;
    }

    /* Adjust card badge position for mobile */
    .spd10-ordinace-card-badge {
        top: 10px;
        right: 10px;
        font-size: 0.75rem;
        padding: 4px 8px;
    }
}



/* Professional Single Ordinace Styles - Embedded in template for better control */
/* These styles are primarily defined in the single template for maximum compatibility */

/* Fallback styles for single ordinace page */
.spd10-ordinace-single-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.spd10-ordinace-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 30px;
    color: white;
}

.spd10-ordinace-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.spd10-ordinace-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.spd10-ordinace-map-container {
    height: 400px; /* Default height - will be overridden by inline styles */
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: relative; /* For fullscreen button positioning */
    display: flex;
    flex-direction: column;
}

/* Professional Archive Styles - Embedded in template for better control */
/* These styles are primarily defined in the archive template for maximum compatibility */

/* Fallback styles for archive page */
.spd10-ordinace-archive-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.spd10-ordinace-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.spd10-ordinace-card-item {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.4s ease;
}

.spd10-ordinace-card-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

/* Utility Classes */
.spd10-ordinace-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.spd10-ordinace-slide-up {
    animation: slideUp 0.6s ease-out;
}

.spd10-ordinace-bounce {
    animation: bounce 2s ease-in-out infinite;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Loading States */
.spd10-ordinace-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #667eea;
}

.spd10-ordinace-loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #667eea;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
    .spd10-ordinace-card,
    .spd10-ordinace-item {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .spd10-ordinace-more-info,
    .spd10-ordinace-card-button {
        display: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .spd10-ordinace-card,
    .spd10-ordinace-item {
        border: 2px solid #000;
    }

    .spd10-ordinace-more-info,
    .spd10-ordinace-card-button {
        background: #000;
        border: 2px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .spd10-ordinace-item,
    .spd10-ordinace-card,
    .spd10-ordinace-card-item {
        animation: none;
        transition: none;
    }

    .spd10-ordinace-item:hover,
    .spd10-ordinace-card:hover,
    .spd10-ordinace-card-item:hover {
        transform: none;
    }
}

/* Map Styles */
.spd10-ordinace-map {
    width: 100%;
    min-height: 300px;
    border: 1px solid #ddd;
    border-radius: 8px;
    position: relative;
}

.spd10-ordinace-admin-map {
    width: 100%;
    height: 250px;
    border: 1px solid #ddd;
    border-radius: 4px;
    position: relative;
}

.map-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.map-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #f8d7da;
    color: #721c24;
    padding: 20px;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
    z-index: 1000;
}

.ordinace-popup {
    max-width: 500px; /* Rozšířeno z 250px na 500px (2x) */
    min-width: 300px; /* Minimální šířka pro lepší čitelnost */
}

.ordinace-popup h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: bold;
}

.ordinace-popup p {
    margin: 5px 0;
    font-size: 12px;
}

.ordinace-popup a {
    color: #0073aa;
    text-decoration: none;
}

.ordinace-popup a:hover {
    text-decoration: underline;
}

/* Grouped popup styles */
.ordinace-popup-group {
    max-width: 600px; /* Rozšířeno z 350px na 600px */
    min-width: 400px; /* Minimální šířka */
    max-height: 400px;
    overflow-y: auto;
}

.ordinace-group-list {
    margin-top: 10px;
}

.ordinace-group-item {
    margin-bottom: 10px;
}

.ordinace-group-item h5 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 14px;
}

.ordinace-group-item p {
    margin: 3px 0;
    font-size: 12px;
}

/* Map controls */
.spd10-ordinace-map-controls {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
    position: relative;
    z-index: 10; /* Above map */
}

.spd10-ordinace-map-filters {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.spd10-ordinace-map-filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.spd10-ordinace-map-filter-group label {
    font-weight: bold;
    font-size: 12px;
    color: #666;
}

.spd10-ordinace-map-filter-group select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.spd10-ordinace-map-legend {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #ddd;
    position: relative;
    z-index: 10; /* Above map */
}

.spd10-ordinace-legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.spd10-ordinace-legend-marker {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Enhanced Map Styles for Shortcode */
.spd10-ordinace-map-container {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    margin: 30px 0;
}

.spd10-ordinace-map-container .spd10-ordinace-map-controls {
    padding: 20px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 0;
    position: relative;
    z-index: 10; /* Above map */
    flex-shrink: 0; /* Don't shrink in flex container */
}

.spd10-ordinace-map-container .spd10-ordinace-map-filters {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.spd10-ordinace-map-container .spd10-ordinace-map-filter-group {
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.spd10-ordinace-map-container .spd10-ordinace-map-filter-group label {
    display: block;
    margin-bottom: 8px;
    color: #4a5568;
    font-weight: 500;
    font-size: 0.9rem;
}

.spd10-ordinace-map-container .spd10-ordinace-map-filter-group select {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #2d3748;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.spd10-ordinace-map-container .spd10-ordinace-map-filter-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.spd10-ordinace-map-container .map-reset-filters {
    padding: 10px 20px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.spd10-ordinace-map-container .map-reset-filters:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.spd10-ordinace-map-container .spd10-ordinace-map-legend {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e2e8f0;
    position: relative;
    z-index: 10; /* Above map */
}

.spd10-ordinace-map-container .spd10-ordinace-legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #4a5568;
}

.spd10-ordinace-map-container .spd10-ordinace-legend-marker {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.spd10-ordinace-map-container .spd10-ordinace-map {
    width: 100% !important;
    flex: 1; /* Take remaining space in flex container */
    border: none;
    border-radius: 0;
    position: relative;
    z-index: 1; /* Below controls (z-index: 10) */
}

/* Fullscreen map styles */
.spd10-ordinace-map-fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 999999 !important;
    border-radius: 0 !important;
    background: white !important;
    margin: 0 !important;
    box-shadow: none !important;
}

.spd10-ordinace-map-fullscreen .spd10-ordinace-map {
    height: 100vh !important;
    width: 100vw !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Prevent body scroll when in fullscreen */
body.spd10-map-fullscreen-active {
    overflow: hidden !important;
}

/* Ensure fullscreen is above everything */
.spd10-ordinace-map-fullscreen {
    z-index: 2147483647 !important; /* Maximum z-index value */
}

/* Fullscreen button in Leaflet control */
.leaflet-control-container .spd10-ordinace-map-fullscreen-btn {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
    font-family: inherit;
    color: #333;
}

.leaflet-control-container .spd10-ordinace-map-fullscreen-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.leaflet-control-container .spd10-ordinace-map-fullscreen-btn svg {
    width: 16px;
    height: 16px;
}

/* Hide filters in fullscreen */
.spd10-ordinace-map-fullscreen .spd10-ordinace-map-controls {
    display: none;
}

/* Responsive Map Styles */
@media (max-width: 768px) {
    .spd10-ordinace-map-container .spd10-ordinace-map-filters {
        flex-direction: column;
        gap: 15px;
    }

    .spd10-ordinace-map-container .spd10-ordinace-map-filter-group {
        min-width: auto;
    }

    .spd10-ordinace-map-container .spd10-ordinace-map-legend {
        justify-content: center;
    }

    .spd10-ordinace-map-container .spd10-ordinace-map {
        height: 300px;
    }
}
