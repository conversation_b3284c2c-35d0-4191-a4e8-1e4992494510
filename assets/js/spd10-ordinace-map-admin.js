/**
 * SPD10 Ordinace Map Admin JavaScript
 * 
 * @package SPD10_Ordinace
 */

(function($) {
    'use strict';

    var adminMapInstance = null;
    var currentMarker = null;

    /**
     * Initialize admin map functionality
     */
    function initAdminMap() {
        var mapContainer = $('.spd10-ordinace-admin-map');
        
        if (mapContainer.length === 0) {
            return;
        }

        var provider = spd10OrdinaceMap.provider;
        var config = spd10OrdinaceMap.config;
        var center = spd10OrdinaceMap.center;

        // Get current coordinates from form fields
        var currentLat = parseFloat($('#spd10_ordinace_lat').val()) || center.lat;
        var currentLng = parseFloat($('#spd10_ordinace_lng').val()) || center.lng;

        switch (provider) {
            case 'leaflet':
                initAdminLeafletMap(mapContainer, config, {lat: currentLat, lng: currentLng});
                break;
            case 'google':
                initAdminGoogleMap(mapContainer, config, {lat: currentLat, lng: currentLng});
                break;
            case 'mapbox':
                initAdminMapboxMap(mapContainer, config, {lat: currentLat, lng: currentLng});
                break;
            default:
                console.error('Unknown map provider: ' + provider);
        }

        // Bind geocode button
        bindGeocodeButton();
    }

    /**
     * Initialize admin Leaflet map
     */
    function initAdminLeafletMap(container, config, center) {
        var mapId = container.attr('id');
        
        adminMapInstance = L.map(mapId, {
            center: [center.lat, center.lng],
            zoom: 15,
            maxZoom: config.max_zoom || 18
        });

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: config.attribution,
            maxZoom: config.max_zoom || 18
        }).addTo(adminMapInstance);

        // Add marker if coordinates exist
        if (center.lat && center.lng) {
            addAdminMarker(center.lat, center.lng);
        }

        // Allow clicking to set marker
        adminMapInstance.on('click', function(e) {
            setMarkerPosition(e.latlng.lat, e.latlng.lng);
        });
    }

    /**
     * Initialize admin Google Maps
     */
    function initAdminGoogleMap(container, config, center) {
        if (typeof google === 'undefined' || !google.maps) {
            console.error('Google Maps API not loaded');
            return;
        }

        var mapId = container.attr('id');
        var mapElement = document.getElementById(mapId);

        adminMapInstance = new google.maps.Map(mapElement, {
            center: {lat: center.lat, lng: center.lng},
            zoom: 15,
            maxZoom: config.max_zoom || 20
        });

        // Add marker if coordinates exist
        if (center.lat && center.lng) {
            addAdminMarker(center.lat, center.lng);
        }

        // Allow clicking to set marker
        adminMapInstance.addListener('click', function(e) {
            setMarkerPosition(e.latLng.lat(), e.latLng.lng());
        });
    }

    /**
     * Initialize admin Mapbox map
     */
    function initAdminMapboxMap(container, config, center) {
        if (typeof mapboxgl === 'undefined') {
            console.error('Mapbox GL JS not loaded');
            return;
        }

        var mapId = container.attr('id');
        var apiKey = spd10OrdinaceMap.apiKeys.mapbox;

        if (!apiKey) {
            console.error('Mapbox API key not configured');
            return;
        }

        mapboxgl.accessToken = apiKey;

        adminMapInstance = new mapboxgl.Map({
            container: mapId,
            style: 'mapbox://styles/mapbox/streets-v11',
            center: [center.lng, center.lat],
            zoom: 15,
            maxZoom: config.max_zoom || 18
        });

        // Add marker if coordinates exist
        adminMapInstance.on('load', function() {
            if (center.lat && center.lng) {
                addAdminMarker(center.lat, center.lng);
            }
        });

        // Allow clicking to set marker
        adminMapInstance.on('click', function(e) {
            setMarkerPosition(e.lngLat.lat, e.lngLat.lng);
        });
    }

    /**
     * Add marker to admin map
     */
    function addAdminMarker(lat, lng) {
        var provider = spd10OrdinaceMap.provider;

        // Remove existing marker
        removeAdminMarker();

        switch (provider) {
            case 'leaflet':
                currentMarker = L.marker([lat, lng], {
                    draggable: true
                }).addTo(adminMapInstance);

                currentMarker.on('dragend', function(e) {
                    var position = e.target.getLatLng();
                    updateCoordinateFields(position.lat, position.lng);
                });
                break;

            case 'google':
                currentMarker = new google.maps.Marker({
                    position: {lat: lat, lng: lng},
                    map: adminMapInstance,
                    draggable: true
                });

                currentMarker.addListener('dragend', function() {
                    var position = currentMarker.getPosition();
                    updateCoordinateFields(position.lat(), position.lng());
                });
                break;

            case 'mapbox':
                var el = document.createElement('div');
                el.className = 'mapbox-admin-marker';
                el.style.backgroundColor = '#ff0000';
                el.style.width = '20px';
                el.style.height = '20px';
                el.style.borderRadius = '50%';
                el.style.border = '2px solid #fff';
                el.style.cursor = 'pointer';

                currentMarker = new mapboxgl.Marker({
                    element: el,
                    draggable: true
                })
                .setLngLat([lng, lat])
                .addTo(adminMapInstance);

                currentMarker.on('dragend', function() {
                    var lngLat = currentMarker.getLngLat();
                    updateCoordinateFields(lngLat.lat, lngLat.lng);
                });
                break;
        }
    }

    /**
     * Remove current admin marker
     */
    function removeAdminMarker() {
        if (!currentMarker) {
            return;
        }

        var provider = spd10OrdinaceMap.provider;

        switch (provider) {
            case 'leaflet':
                adminMapInstance.removeLayer(currentMarker);
                break;
            case 'google':
                currentMarker.setMap(null);
                break;
            case 'mapbox':
                currentMarker.remove();
                break;
        }

        currentMarker = null;
    }

    /**
     * Set marker position and update fields
     */
    function setMarkerPosition(lat, lng) {
        addAdminMarker(lat, lng);
        updateCoordinateFields(lat, lng);
        
        // Center map on new position
        centerMapOnPosition(lat, lng);
    }

    /**
     * Update coordinate input fields
     */
    function updateCoordinateFields(lat, lng) {
        $('#spd10_ordinace_lat').val(lat.toFixed(6));
        $('#spd10_ordinace_lng').val(lng.toFixed(6));
        
        // Update geocode status
        $('#spd10_ordinace_geocode_status').val('manual');
        $('.geocode-status-indicator').removeClass('status-ok status-pending status-failed')
                                      .addClass('status-manual')
                                      .text('Ručně nastaveno');
    }

    /**
     * Center map on position
     */
    function centerMapOnPosition(lat, lng) {
        var provider = spd10OrdinaceMap.provider;

        switch (provider) {
            case 'leaflet':
                adminMapInstance.setView([lat, lng], 15);
                break;
            case 'google':
                adminMapInstance.setCenter({lat: lat, lng: lng});
                adminMapInstance.setZoom(15);
                break;
            case 'mapbox':
                adminMapInstance.flyTo({
                    center: [lng, lat],
                    zoom: 15
                });
                break;
        }
    }

    /**
     * Bind geocode button functionality
     */
    function bindGeocodeButton() {
        $('.geocode-button').on('click', function(e) {
            e.preventDefault();
            
            var button = $(this);
            var address = $('#spd10_ordinace_address').val();
            
            if (!address) {
                alert('Prosím zadejte adresu pro geokódování.');
                return;
            }

            button.prop('disabled', true).text('Geokóduji...');

            $.ajax({
                url: spd10OrdinaceMap.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'spd10_ordinace_geocode_address',
                    address: address,
                    nonce: spd10OrdinaceMap.nonce
                },
                success: function(response) {
                    if (response.success && response.data.lat && response.data.lng) {
                        setMarkerPosition(response.data.lat, response.data.lng);
                        
                        // Update geocode status
                        $('#spd10_ordinace_geocode_status').val('ok');
                        $('.geocode-status-indicator').removeClass('status-manual status-pending status-failed')
                                                      .addClass('status-ok')
                                                      .text('Úspěšně geokódováno');
                    } else {
                        alert('Geokódování se nezdařilo: ' + (response.data || 'Neznámá chyba'));
                        
                        // Update geocode status
                        $('#spd10_ordinace_geocode_status').val('failed');
                        $('.geocode-status-indicator').removeClass('status-ok status-pending status-manual')
                                                      .addClass('status-failed')
                                                      .text('Geokódování selhalo');
                    }
                },
                error: function() {
                    alert('Chyba při komunikaci se serverem.');
                },
                complete: function() {
                    button.prop('disabled', false).text('Geokódovat');
                }
            });
        });
    }

    // Initialize when document is ready
    $(document).ready(function() {
        initAdminMap();
    });

})(jQuery);
