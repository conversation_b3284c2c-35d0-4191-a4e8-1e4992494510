/**
 * SPD10 Ordinace Map Frontend JavaScript
 * Version: 2.0.1
 *
 * @package SPD10_Ordinace
 */

(function($) {
    'use strict';

    // Global map instance
    var mapInstance = null;
    var markersLayer = null;
    var currentMarkers = [];
    var allMarkers = []; // Store all markers for filtering
    var currentFilters = {
        type: 'all',
        quarter: 'all'
    };

    /**
     * @typedef {Object} Ordinace
     * @property {number|string} id Unique identifier.
     * @property {string} title Display name.
     * @property {number} lat Latitude in decimal degrees.
     * @property {number} lng Longitude in decimal degrees.
     * @property {string} type Type slug used for coloring and filtering.
     * @property {string} [doctors_names] Comma-separated doctor names.
     * @property {string} [organization] Organization name.
     * @property {string} [address] Human-readable address.
     * @property {string} [quarter] City quarter key used for filtering.
     * @property {string} [email]
     * @property {string} [phone]
     * @property {string} [website]
     * @property {string} permalink Absolute URL to detail page.
     */

    /**
     * Initialize map functionality
     */
    function initMap() {
        var mapContainer = $('.spd10-ordinace-map');

        if (mapContainer.length === 0) {
            return;
        }

        var provider = spd10OrdinaceMap.provider;
        var config = spd10OrdinaceMap.config;
        var center = spd10OrdinaceMap.center;

        // Wait for map libraries to load
        function tryInitMap() {
            switch (provider) {
                case 'leaflet':
                    if (typeof L !== 'undefined') {
                        initLeafletMap(mapContainer, config, center);
                    } else {
                        setTimeout(tryInitMap, 100);
                    }
                    break;
                case 'google':
                    if (typeof google !== 'undefined' && google.maps) {
                        initGoogleMap(mapContainer, config, center);
                    } else {
                        setTimeout(tryInitMap, 100);
                    }
                    break;
                case 'mapbox':
                    if (typeof mapboxgl !== 'undefined') {
                        initMapboxMap(mapContainer, config, center);
                    } else {
                        setTimeout(tryInitMap, 100);
                    }
                    break;
                default:
                    console.error('Unknown map provider: ' + provider);
            }
        }

        tryInitMap();
    }

    /**
     * Initialize Leaflet map
     */
    function initLeafletMap(container, config, center) {
        var mapId = container.attr('id');

        mapInstance = L.map(mapId, {
            center: [center.lat, center.lng],
            zoom: config.default_zoom || 13,
            maxZoom: config.max_zoom || 18
        });

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: config.attribution,
            maxZoom: config.max_zoom || 18
        }).addTo(mapInstance);

        // Create markers cluster group (if available) or regular layer group
        if (typeof L.MarkerClusterGroup !== 'undefined') {
            markersLayer = L.markerClusterGroup({
                maxClusterRadius: 50, // Cluster radius in pixels
                spiderfyOnMaxZoom: true, // Show individual markers when max zoom reached
                showCoverageOnHover: false, // Don't show cluster coverage on hover
                zoomToBoundsOnClick: true, // Zoom to cluster bounds on click
                spiderfyDistanceMultiplier: 1.5, // Increase distance between spiderfied markers
                chunkedLoading: true // Better performance for large datasets
                // Note: No disableClusteringAtZoom - clustering works at all zoom levels
            }).addTo(mapInstance);
        } else {
            // Fallback to regular layer group if MarkerClusterGroup not available
            markersLayer = L.layerGroup().addTo(mapInstance);
        }

        // Add fullscreen control to map
        addFullscreenControl(mapId);

        // Data will be loaded by shortcode initialization
    }

    /**
     * Initialize Google Maps
     */
    function initGoogleMap(container, config, center) {
        if (typeof google === 'undefined' || !google.maps) {
            console.error('Google Maps API not loaded');
            return;
        }

        var mapId = container.attr('id');
        var mapElement = document.getElementById(mapId);

        mapInstance = new google.maps.Map(mapElement, {
            center: {lat: center.lat, lng: center.lng},
            zoom: config.default_zoom || 13,
            maxZoom: config.max_zoom || 20
        });

        // Data will be loaded by shortcode initialization
    }

    /**
     * Initialize Mapbox map
     */
    function initMapboxMap(container, config, center) {
        if (typeof mapboxgl === 'undefined') {
            console.error('Mapbox GL JS not loaded');
            return;
        }

        var mapId = container.attr('id');
        var apiKey = spd10OrdinaceMap.apiKeys.mapbox;

        if (!apiKey) {
            console.error('Mapbox API key not configured');
            return;
        }

        mapboxgl.accessToken = apiKey;

        mapInstance = new mapboxgl.Map({
            container: mapId,
            style: 'mapbox://styles/mapbox/streets-v11',
            center: [center.lng, center.lat],
            zoom: config.default_zoom || 13,
            maxZoom: config.max_zoom || 18
        });

        // Data will be loaded by shortcode initialization
    }

    /**
     * Load ordinace data - try AJAX first, then GeoJSON as fallback
     * @param {string} [typeFilter='all'] Type slug to filter by.
     * @param {string} [quarterFilter='all'] Quarter key to filter by.
     */
    function loadOrdinaceData(typeFilter, quarterFilter) {
        console.log('Loading ordinace data with filters:', typeFilter, quarterFilter);

        // Try AJAX endpoint first (more reliable)
        if (typeof spd10OrdinaceMap.ajaxUrl !== 'undefined') {
            console.log('Trying AJAX endpoint first...');
            tryAjaxEndpoint(typeFilter, quarterFilter);
        } else {
            console.log('AJAX URL not available, trying GeoJSON...');
            tryGeoJsonEndpoint(typeFilter, quarterFilter);
        }
    }

    /**
     * Try AJAX endpoint for loading data
     * @param {string} [typeFilter='all'] Type slug to filter by.
     * @param {string} [quarterFilter='all'] Quarter key to filter by.
     */
    function tryAjaxEndpoint(typeFilter, quarterFilter) {
        showMapLoading();

        $.ajax({
            url: spd10OrdinaceMap.ajaxUrl,
            type: 'POST',
            data: {
                action: 'spd10_ordinace_get_map_data',
                type_filter: typeFilter || '',
                quarter_filter: quarterFilter || ''
            },
            success: function(response) {
                hideMapLoading();
                console.log('AJAX response:', response);

                if (response.success && response.data && response.data.length > 0) {
                    console.log('AJAX successful, got', response.data.length, 'ordinace');
                    displayOrdinaceMarkers(response.data);
                } else {
                    console.log('AJAX returned no data, trying GeoJSON fallback...');
                    tryGeoJsonEndpoint(typeFilter, quarterFilter);
                }
            },
            error: function(xhr, status, error) {
                hideMapLoading();
                console.error('AJAX endpoint failed:', status, error);
                console.log('Trying GeoJSON fallback...');
                tryGeoJsonEndpoint(typeFilter, quarterFilter);
            }
        });
    }

    /**
     * Try GeoJSON endpoint as fallback
     * @param {string} [typeFilter='all'] Type slug to filter by.
     * @param {string} [quarterFilter='all'] Quarter key to filter by.
     */
    function tryGeoJsonEndpoint(typeFilter, quarterFilter) {
        // Build GeoJSON URL
        var geojsonUrl = spd10OrdinaceMap.geojsonUrl || (spd10OrdinaceMap.homeUrl + '/ordinace-geojson');

        if (typeFilter && typeFilter !== 'all') {
            geojsonUrl += '/' + encodeURIComponent(typeFilter);
        }

        var params = [];
        if (quarterFilter && quarterFilter !== 'all') {
            params.push('quarter=' + encodeURIComponent(quarterFilter));
        }

        if (params.length > 0) {
            geojsonUrl += '?' + params.join('&');
        }

        console.log('Loading ordinace data from GeoJSON:', geojsonUrl);
        showMapLoading();

        $.ajax({
            url: geojsonUrl,
            type: 'GET',
            dataType: 'json',
            success: function(geojsonData) {
                hideMapLoading();
                console.log('GeoJSON data received:', geojsonData);

                if (geojsonData && geojsonData.features && geojsonData.features.length > 0) {
                    console.log('Displaying', geojsonData.features.length, 'ordinace markers from GeoJSON');
                    displayOrdinaceMarkersFromGeoJSON(geojsonData);
                } else {
                    console.log('No ordinace data found in GeoJSON');
                    showMapError(spd10OrdinaceMap.strings.noResults || 'Žádné ordinace nebyly nalezeny.');
                }
            },
            error: function(xhr, status, error) {
                hideMapLoading();
                console.error('GeoJSON endpoint also failed:', status, error);
                showMapError(spd10OrdinaceMap.strings.error || 'Chyba při načítání dat ordinací.');
            }
        });
    }



    /**
     * Display ordinace markers from GeoJSON data
     * @param {Object} geojsonData FeatureCollection-like JSON with ordinace features.
     */
    function displayOrdinaceMarkersFromGeoJSON(geojsonData) {
        if (!mapInstance || !geojsonData.features) {
            return;
        }

        var provider = spd10OrdinaceMap.provider;

        // Clear existing markers including stored data
        clearAllMarkers();

        // Clear all markers data
        allMarkers = [];

        // Create individual markers for each ordinace (let MarkerClusterGroup handle clustering)
        geojsonData.features.forEach(function(feature) {
            if (!feature.geometry || !feature.geometry.coordinates) {
                return; // Skip features without coordinates
            }

            var coordinates = feature.geometry.coordinates;
            var properties = feature.properties;

            // Convert GeoJSON coordinates (lng, lat) to our format
            var ordinace = {
                id: properties.id,
                title: properties.title,
                lat: coordinates[1],
                lng: coordinates[0],
                type: properties.type,
                doctors_names: properties.doctors_names,
                organization: properties.organization,
                address: properties.address,
                quarter: properties.quarter,
                email: properties.email,
                phone: properties.phone,
                website: properties.website,
                permalink: properties.permalink
            };

            var markerColor = getMarkerColor(ordinace.type);
            var marker = null;

            // Create individual marker for each ordinace
            switch (provider) {
                case 'leaflet':
                    marker = createLeafletMarker(ordinace, markerColor);
                    break;
                case 'google':
                    marker = createGoogleMarker(ordinace, markerColor);
                    break;
                case 'mapbox':
                    marker = createMapboxMarker(ordinace, markerColor);
                    break;
            }

            if (marker) {
                // Store marker data for filtering
                allMarkers.push({
                    marker: marker,
                    ordinace: ordinace
                });

                // Add to current markers
                currentMarkers.push(marker);
            }
        });

        console.log('Created', currentMarkers.length, 'individual markers from', geojsonData.features.length, 'ordinace');



        // Fit map to markers if we have any
        if (currentMarkers.length > 0) {
            fitMapToMarkers();
        }
    }

    /**
     * Display ordinace markers on map (legacy function for backward compatibility)
     * @param {Ordinace[]} ordinaceData Array of ordinace objects with coordinates.
     */
    function displayOrdinaceMarkers(ordinaceData) {
        if (!mapInstance) {
            return;
        }

        var provider = spd10OrdinaceMap.provider;

        // Clear existing markers including stored data
        clearAllMarkers();

        ordinaceData.forEach(function(ordinace) {
            if (!ordinace.lat || !ordinace.lng) {
                return; // Skip ordinace without coordinates
            }

            var markerColor = getMarkerColor(ordinace.type);
            var marker = null;

            switch (provider) {
                case 'leaflet':
                    marker = createLeafletMarker(ordinace, markerColor);
                    break;
                case 'google':
                    marker = createGoogleMarker(ordinace, markerColor);
                    break;
                case 'mapbox':
                    marker = createMapboxMarker(ordinace, markerColor);
                    break;
            }

            if (marker) {
                // Store marker data for filtering
                allMarkers.push({
                    marker: marker,
                    ordinace: ordinace
                });

                // Add to current markers
                currentMarkers.push(marker);
            }
        });

        // Fit map to markers if we have any
        console.log('displayOrdinaceMarkers complete. allMarkers:', allMarkers.length, 'currentMarkers:', currentMarkers.length);
        if (currentMarkers.length > 0) {
            fitMapToMarkers();
        }
    }

    /**
     * Create Leaflet marker (without adding to map)
     * @param {Ordinace} ordinace
     * @param {string} color Hex color used for marker fill.
     * @returns {L.CircleMarker}
     */
    function createLeafletMarker(ordinace, color) {
        var marker = L.circleMarker([ordinace.lat, ordinace.lng], {
            radius: 8,
            fillColor: color,
            color: '#fff',
            weight: 2,
            opacity: 1,
            fillOpacity: 0.8
        });

        var popupContent = createPopupContent(ordinace);
        marker.bindPopup(popupContent);

        markersLayer.addLayer(marker);
        return marker;
    }

    /**
     * Add Leaflet marker (legacy function)
     */
    function addLeafletMarker(ordinace, color) {
        var marker = createLeafletMarker(ordinace, color);
        currentMarkers.push(marker);
        return marker;
    }

    /**
     * Create Google Maps marker (without adding to map)
     * @param {Ordinace} ordinace
     * @param {string} color Hex color used for marker fill.
     * @returns {google.maps.Marker}
     */
    function createGoogleMarker(ordinace, color) {
        var marker = new google.maps.Marker({
            position: {lat: parseFloat(ordinace.lat), lng: parseFloat(ordinace.lng)},
            map: mapInstance,
            title: ordinace.title,
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                fillColor: color,
                fillOpacity: 0.8,
                strokeColor: '#fff',
                strokeWeight: 2,
                scale: 8
            }
        });

        var popupContent = createPopupContent(ordinace);

        var infoWindow = new google.maps.InfoWindow({
            content: popupContent
        });

        marker.addListener('click', function() {
            infoWindow.open(mapInstance, marker);
        });

        return marker;
    }

    /**
     * Add Google Maps marker (legacy function)
     */
    function addGoogleMarker(ordinace, color) {
        var marker = createGoogleMarker(ordinace, color);
        currentMarkers.push(marker);
        return marker;
    }

    /**
     * Create Mapbox marker (without adding to map)
     * @param {Ordinace} ordinace
     * @param {string} color Hex color used for marker fill.
     * @returns {mapboxgl.Marker}
     */
    function createMapboxMarker(ordinace, color) {
        var el = document.createElement('div');
        el.className = 'mapbox-marker';
        el.style.backgroundColor = color;
        el.style.width = '16px';
        el.style.height = '16px';
        el.style.borderRadius = '50%';
        el.style.border = '2px solid #fff';
        el.style.cursor = 'pointer';

        var marker = new mapboxgl.Marker(el)
            .setLngLat([ordinace.lng, ordinace.lat])
            .addTo(mapInstance);

        var popupContent = createPopupContent(ordinace);

        var popup = new mapboxgl.Popup({offset: 25})
            .setHTML(popupContent);

        marker.setPopup(popup);
        return marker;
    }

    /**
     * Add Mapbox marker (legacy function)
     */
    function addMapboxMarker(ordinace, color) {
        var marker = createMapboxMarker(ordinace, color);
        currentMarkers.push(marker);
        return marker;
    }

    /**
     * Create popup content for marker
     */
    function createPopupContent(ordinace) {
        var content = '<div class="ordinace-popup">';
        content += '<h4>' + ordinace.title + '</h4>';

        if (ordinace.doctors_names) {
            content += '<p><strong>Lékaři:</strong> ' + ordinace.doctors_names + '</p>';
        }

        if (ordinace.address) {
            content += '<p><strong>Adresa:</strong> ' + ordinace.address + '</p>';
        }

        if (ordinace.quarter) {
            content += '<p><strong>Čtvrť:</strong> ' + ordinace.quarter + '</p>';
        }

        content += '<p><a href="' + ordinace.permalink + '" target="_blank">Více informací</a></p>';
        content += '</div>';

        return content;
    }

    /**
     * Create popup content for grouped markers
     */
    function createGroupPopupContent(ordinaceGroup) {
        var content = '<div class="ordinace-popup ordinace-popup-group">';
        content += '<h4>Ordinace na této adrese (' + ordinaceGroup.length + ')</h4>';
        content += '<p><strong>Adresa:</strong> ' + ordinaceGroup[0].address + '</p>';

        content += '<div class="ordinace-group-list">';
        ordinaceGroup.forEach(function(ordinace) {
            content += '<div class="ordinace-group-item">';
            content += '<h5>' + ordinace.title + '</h5>';
            if (ordinace.doctors_names) {
                content += '<p><strong>Lékaři:</strong> ' + ordinace.doctors_names + '</p>';
            }
            content += '<p><a href="' + ordinace.permalink + '" target="_blank">Více informací</a></p>';
            content += '</div>';
            if (ordinaceGroup.indexOf(ordinace) < ordinaceGroup.length - 1) {
                content += '<hr style="margin: 10px 0; border: 1px solid #eee;">';
            }
        });
        content += '</div>';

        content += '</div>';

        return content;
    }

    /**
     * Get marker color based on ordinace type
     * @param {string} type Ordinace type slug.
     * @returns {string} Hex color string.
     */
    function getMarkerColor(type) {
        // Get colors from configuration or use defaults
        var colors = spd10OrdinaceMap.markerColors || {
            'pediatr': '#4CAF50',
            'praktik-dospeli': '#2196F3'
        };

        return colors[type] || '#757575';
    }

    /**
     * Clear all markers from map
     */
    function clearMarkers() {
        var provider = spd10OrdinaceMap.provider;

        switch (provider) {
            case 'leaflet':
                if (markersLayer) {
                    markersLayer.clearLayers();
                }
                break;
            case 'google':
                currentMarkers.forEach(function(marker) {
                    marker.setMap(null);
                });
                break;
            case 'mapbox':
                currentMarkers.forEach(function(marker) {
                    marker.remove();
                });
                break;
        }

        currentMarkers = [];
        // Note: allMarkers is NOT cleared here - it's preserved for filtering
    }

    /**
     * Clear all markers including stored data (for loading new data)
     */
    function clearAllMarkers() {
        clearMarkers();
        allMarkers = [];
        console.log('All markers cleared, allMarkers count:', allMarkers.length);
    }

    /**
     * Fit map to show all markers
     */
    function fitMapToMarkers() {
        if (currentMarkers.length === 0) {
            console.log('No markers to fit');
            return;
        }

        var provider = spd10OrdinaceMap.provider;
        console.log('Fitting map to', currentMarkers.length, 'markers using provider:', provider);

        // Wait a bit for map to be fully rendered
        setTimeout(function() {
            switch (provider) {
                case 'leaflet':
                    if (markersLayer && markersLayer.getLayers().length > 0) {
                        try {
                            // Create bounds from all markers
                            var group = new L.featureGroup(markersLayer.getLayers());
                            var bounds = group.getBounds();
                            console.log('Leaflet bounds:', bounds);

                            // Ensure map container has proper dimensions
                            mapInstance.invalidateSize();

                            mapInstance.fitBounds(bounds, {
                                padding: [30, 30], // More padding for better view
                                maxZoom: 14 // Prevent zooming too close
                            });
                        } catch (e) {
                            console.error('Error fitting Leaflet bounds:', e);
                        }
                    }
                    break;
                case 'google':
                    var bounds = new google.maps.LatLngBounds();
                    currentMarkers.forEach(function(marker) {
                        bounds.extend(marker.getPosition());
                    });
                    console.log('Google bounds:', bounds);
                    mapInstance.fitBounds(bounds);
                    // Prevent zooming too close
                    setTimeout(function() {
                        if (mapInstance.getZoom() > 14) {
                            mapInstance.setZoom(14);
                        }
                    }, 100);
                    break;
                case 'mapbox':
                    var bounds = new mapboxgl.LngLatBounds();
                    currentMarkers.forEach(function(marker) {
                        bounds.extend(marker.getLngLat());
                    });
                    console.log('Mapbox bounds:', bounds);
                    mapInstance.fitBounds(bounds, {
                        padding: 30,
                        maxZoom: 14 // Prevent zooming too close
                    });
                    break;
            }
        }, 100);
    }

    /**
     * Show loading indicator
     */
    function showMapLoading() {
        $('.spd10-ordinace-map').append('<div class="map-loading">' + spd10OrdinaceMap.strings.loading + '</div>');
    }

    /**
     * Hide loading indicator
     */
    function hideMapLoading() {
        $('.map-loading').remove();
    }

    /**
     * Show error message
     */
    function showMapError(message) {
        $('.spd10-ordinace-map').append('<div class="map-error">' + message + '</div>');
    }

    /**
     * Filter markers based on current filters
     */
    function filterMarkers() {
        console.log('filterMarkers called, allMarkers:', allMarkers.length, 'mapInstance:', !!mapInstance);

        if (!mapInstance || allMarkers.length === 0) {
            console.log('Cannot filter - no map instance or no markers');
            return;
        }

        var provider = spd10OrdinaceMap.provider;
        console.log('Filtering with provider:', provider, 'filters:', currentFilters);

        // Clear current markers
        clearMarkers();

        // Filter and display markers
        allMarkers.forEach(function(markerData) {
            var shouldShow = true;

            // Type filter
            if (currentFilters.type !== 'all' && markerData.ordinace.type !== currentFilters.type) {
                shouldShow = false;
            }

            // Quarter filter
            if (currentFilters.quarter !== 'all' && markerData.ordinace.quarter !== currentFilters.quarter) {
                shouldShow = false;
            }

            if (shouldShow) {
                // Re-add marker to map
                switch (provider) {
                    case 'leaflet':
                        markersLayer.addLayer(markerData.marker);
                        currentMarkers.push(markerData.marker);
                        break;
                    case 'google':
                        markerData.marker.setMap(mapInstance);
                        currentMarkers.push(markerData.marker);
                        break;
                    case 'mapbox':
                        markerData.marker.addTo(mapInstance);
                        currentMarkers.push(markerData.marker);
                        break;
                }
            }
        });

        // Update map bounds if we have visible markers
        console.log('Filtering complete. Visible markers:', currentMarkers.length);
        if (currentMarkers.length > 0) {
            fitMapToMarkers();
        }
    }

    /**
     * Set filter values and apply filtering
     * @param {string} [typeFilter='all'] Type slug to filter by.
     * @param {string} [quarterFilter='all'] Quarter key to filter by.
     */
    function setFilters(typeFilter, quarterFilter) {
        console.log('Setting filters:', typeFilter, quarterFilter);
        currentFilters.type = typeFilter || 'all';
        currentFilters.quarter = quarterFilter || 'all';
        console.log('Current filters:', currentFilters);
        console.log('All markers count:', allMarkers.length);
        filterMarkers();
    }

    /**
     * Reset all filters
     */
    function resetFilters() {
        currentFilters.type = 'all';
        currentFilters.quarter = 'all';
        filterMarkers();
    }

    // Make functions globally available for shortcode use
    window.spd10OrdinaceMapFilters = {
        setFilters: setFilters,
        resetFilters: resetFilters,
        loadOrdinaceData: loadOrdinaceData
    };

    // Also make loadOrdinaceData globally available for backward compatibility
    window.loadOrdinaceData = loadOrdinaceData;

    // Debug log
    console.log('SPD10 Ordinace Map JS loaded, functions available:', {
        loadOrdinaceData: typeof window.loadOrdinaceData,
        spd10OrdinaceMapFilters: typeof window.spd10OrdinaceMapFilters
    });

    /**
     * Add fullscreen control to Leaflet map
     */
    function addFullscreenControl(mapId) {
        if (!mapInstance || spd10OrdinaceMap.provider !== 'leaflet') {
            return;
        }

        // Create custom fullscreen control
        var FullscreenControl = L.Control.extend({
            onAdd: function(map) {
                var button = L.DomUtil.create('button', 'spd10-ordinace-map-fullscreen-btn');
                button.innerHTML = '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/></svg> Celá obrazovka';
                button.title = 'Celá obrazovka';

                L.DomEvent.on(button, 'click', function() {
                    toggleMapFullscreen(mapId);
                });

                return button;
            }
        });

        // Add control to map
        var fullscreenControl = new FullscreenControl({ position: 'topright' });
        fullscreenControl.addTo(mapInstance);
    }

    /**
     * Toggle fullscreen mode for map
     */
    function toggleMapFullscreen(mapId) {
        var mapContainer = document.getElementById(mapId).closest('.spd10-ordinace-map-container');
        var isFullscreen = mapContainer.classList.contains('spd10-ordinace-map-fullscreen');

        if (isFullscreen) {
            exitMapFullscreen(mapContainer);
        } else {
            enterMapFullscreen(mapContainer);
        }
    }

    /**
     * Enter fullscreen mode
     */
    function enterMapFullscreen(mapContainer) {
        mapContainer.classList.add('spd10-ordinace-map-fullscreen');
        document.body.classList.add('spd10-map-fullscreen-active');

        // Trigger map resize after fullscreen
        setTimeout(function() {
            if (mapInstance && mapInstance.invalidateSize) {
                mapInstance.invalidateSize();
            }
        }, 100);
    }

    /**
     * Exit fullscreen mode
     */
    function exitMapFullscreen(mapContainer) {
        mapContainer.classList.remove('spd10-ordinace-map-fullscreen');
        document.body.classList.remove('spd10-map-fullscreen-active');

        // Trigger map resize after exit fullscreen
        setTimeout(function() {
            if (mapInstance && mapInstance.invalidateSize) {
                mapInstance.invalidateSize();
            }
        }, 100);
    }

    // Make functions globally available for compatibility
    window.toggleMapFullscreen = toggleMapFullscreen;

    // Initialize when document is ready
    $(document).ready(function() {
        initMap();
    });

})(jQuery);
