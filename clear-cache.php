<?php
/**
 * Clear all SPD10 Ordinace cache
 */

// Try to find WordPress
$wp_paths = [
    __DIR__ . '/wp-config.php',
    __DIR__ . '/../wp-config.php', 
    __DIR__ . '/../../wp-config.php',
    __DIR__ . '/../../../wp-config.php',
];

$wp_found = false;
foreach ($wp_paths as $path) {
    if (file_exists($path)) {
        define('WP_USE_THEMES', false);
        require_once dirname($path) . '/wp-load.php';
        $wp_found = true;
        break;
    }
}

if (!$wp_found) {
    die("WordPress not found. Please run this script from WordPress directory.\n");
}

echo "<h1>SPD10 Ordinace Cache Clear</h1>\n";

// Clear all SPD10 ordinace related transients
global $wpdb;

// Delete GeoJSON cache
$deleted_geojson = $wpdb->query($wpdb->prepare(
    "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
    '_transient_spd10_ordinace_geojson_%',
    '_transient_timeout_spd10_ordinace_geojson_%'
));

// Delete map data cache
$deleted_map = $wpdb->query($wpdb->prepare(
    "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
    '_transient_spd10_ordinace_map_data_%',
    '_transient_timeout_spd10_ordinace_map_data_%'
));

echo "✅ Cleared $deleted_geojson GeoJSON cache entries<br>\n";
echo "✅ Cleared $deleted_map map data cache entries<br>\n";

// Also clear any other SPD10 related cache
$deleted_other = $wpdb->query($wpdb->prepare(
    "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
    '_transient_spd10_%',
    '_transient_timeout_spd10_%'
));

echo "✅ Cleared $deleted_other other SPD10 cache entries<br>\n";

echo "<br><strong>All cache cleared! Now try the map again.</strong><br>\n";
?>
