{"name": "spd10/ordinace-plugin", "description": "WordPress plugin pro správu databáze ordinací praktických lékařů a pediatrů na Praze 10", "version": "1.0.0", "type": "wordpress-plugin", "license": "GPL-2.0-or-later", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://strategieprodesitku.cz"}], "homepage": "https://strategieprodesitku.cz/lekari", "support": {"issues": "https://github.com/rousarp/ordinace-plugin/issues", "source": "https://github.com/rousarp/ordinace-plugin"}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.7", "wp-coding-standards/wpcs": "^3.0", "phpcompatibility/phpcompatibility-wp": "^2.1", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "phpstan/phpstan": "^1.10", "szepeviktor/phpstan-wordpress": "^1.3"}, "autoload": {"psr-4": {"SPD10\\Ordinace\\": "includes/"}}, "autoload-dev": {"psr-4": {"SPD10\\Ordinace\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "test:coverage": "phpunit --coverage-html coverage", "cs:check": "phpcs", "cs:fix": "phpcbf", "analyze": "phpstan analyse", "quality": ["@cs:check", "@analyze", "@test"]}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}, "optimize-autoloader": true, "sort-packages": true}, "extra": {"wordpress-install-dir": "vendor/wordpress/wordpress"}}