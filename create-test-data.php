<?php
/**
 * Quick script to create test data for ordinace map
 */

// Include WordPress
require_once('../../../wp-config.php');

// Include test data functions
require_once('test-data.php');

echo "Creating test ordinace data...\n";

// Create test data
$result = spd10_ordinace_create_test_data();

echo "Created: " . $result['created'] . " ordinaces\n";

if (!empty($result['errors'])) {
    echo "Errors:\n";
    foreach ($result['errors'] as $error) {
        echo "- " . $error . "\n";
    }
}

// List created ordinaces
$ordinaces = get_posts(array(
    'post_type' => 'ordinace',
    'posts_per_page' => -1,
    'post_status' => 'publish'
));

echo "\nCurrent ordinaces in database:\n";
foreach ($ordinaces as $ordinace) {
    $lat = get_post_meta($ordinace->ID, '_spd10_ordinace_lat', true);
    $lng = get_post_meta($ordinace->ID, '_spd10_ordinace_lng', true);
    $status = get_post_meta($ordinace->ID, '_spd10_ordinace_geocode_status', true);
    $terms = wp_get_post_terms($ordinace->ID, 'ordinace_typ');
    $type = !empty($terms) ? $terms[0]->slug : 'none';
    
    echo "- {$ordinace->post_title} (Type: {$type}, Coords: {$lat},{$lng}, Status: {$status})\n";
}

echo "\nDone!\n";
?>
