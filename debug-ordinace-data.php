<?php
/**
 * Debug script for ordinace data
 * 
 * This script checks what ordinace data we have in the database
 * and helps identify why the map is not showing any points.
 */

// Try to find WordPress
$wp_paths = [
    __DIR__ . '/wp-config.php',
    __DIR__ . '/../wp-config.php', 
    __DIR__ . '/../../wp-config.php',
    __DIR__ . '/../../../wp-config.php',
];

$wp_found = false;
foreach ($wp_paths as $path) {
    if (file_exists($path)) {
        define('WP_USE_THEMES', false);
        require_once dirname($path) . '/wp-load.php';
        $wp_found = true;
        break;
    }
}

if (!$wp_found) {
    // Try direct access via HTTP if we can't load WordPress
    echo "WordPress not found locally. Please run this script from WordPress directory.\n";
    echo "Or access it via: http://your-site.com/wp-content/plugins/ordinace-plugin/debug-ordinace-data.php\n";
    exit;
}

echo "<h1>SPD10 Ordinace Data Debug</h1>\n";

// Check if we're in WordPress
if (!function_exists('get_posts')) {
    die("WordPress functions not available!");
}

// 1. Check total ordinace posts
echo "<h2>1. Total Ordinace Posts</h2>\n";
$all_posts = get_posts([
    'post_type' => 'ordinace',
    'post_status' => 'publish',
    'posts_per_page' => -1,
]);

echo "Total published ordinace posts: " . count($all_posts) . "<br>\n";

if (count($all_posts) == 0) {
    echo "<strong>❌ No ordinace posts found! You need to import some data first.</strong><br>\n";
    exit;
}

// 2. Check posts with coordinates
echo "<h2>2. Posts with Coordinates</h2>\n";
$posts_with_coords = [];
$posts_without_coords = [];

foreach ($all_posts as $post) {
    $lat = get_post_meta($post->ID, '_spd10_ordinace_lat', true);
    $lng = get_post_meta($post->ID, '_spd10_ordinace_lng', true);
    
    if (!empty($lat) && !empty($lng) && $lat != '0' && $lng != '0') {
        $posts_with_coords[] = $post;
    } else {
        $posts_without_coords[] = $post;
    }
}

echo "Posts with coordinates: " . count($posts_with_coords) . "<br>\n";
echo "Posts without coordinates: " . count($posts_without_coords) . "<br>\n";

// 3. Show sample data
if (count($posts_with_coords) > 0) {
    echo "<h2>3. Sample Posts with Coordinates</h2>\n";
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>ID</th><th>Title</th><th>Lat</th><th>Lng</th><th>Type</th><th>Address</th></tr>\n";
    
    foreach (array_slice($posts_with_coords, 0, 5) as $post) {
        $lat = get_post_meta($post->ID, '_spd10_ordinace_lat', true);
        $lng = get_post_meta($post->ID, '_spd10_ordinace_lng', true);
        $address = get_post_meta($post->ID, '_spd10_ordinace_address', true);
        
        $types = wp_get_post_terms($post->ID, 'ordinace_typ');
        $type = !empty($types) ? $types[0]->name : 'N/A';
        
        echo "<tr>";
        echo "<td>{$post->ID}</td>";
        echo "<td>" . esc_html($post->post_title) . "</td>";
        echo "<td>$lat</td>";
        echo "<td>$lng</td>";
        echo "<td>$type</td>";
        echo "<td>" . esc_html($address) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
}

// 4. Show posts without coordinates
if (count($posts_without_coords) > 0) {
    echo "<h2>4. Sample Posts WITHOUT Coordinates</h2>\n";
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>ID</th><th>Title</th><th>Address</th><th>All Meta Keys</th></tr>\n";
    
    foreach (array_slice($posts_without_coords, 0, 3) as $post) {
        $address = get_post_meta($post->ID, '_spd10_ordinace_address', true);
        $all_meta = get_post_meta($post->ID);
        $meta_keys = array_keys($all_meta);
        
        echo "<tr>";
        echo "<td>{$post->ID}</td>";
        echo "<td>" . esc_html($post->post_title) . "</td>";
        echo "<td>" . esc_html($address) . "</td>";
        echo "<td>" . implode(', ', $meta_keys) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
}

// 5. Test AJAX query
echo "<h2>5. Test AJAX Query</h2>\n";
$query_args = array(
    'post_type' => 'ordinace',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'meta_query' => array(
        'relation' => 'AND',
        array(
            'key' => '_spd10_ordinace_lat',
            'compare' => 'EXISTS',
        ),
        array(
            'key' => '_spd10_ordinace_lng',
            'compare' => 'EXISTS',
        ),
    ),
);

$test_query = new WP_Query($query_args);
echo "AJAX query would find: " . $test_query->found_posts . " posts<br>\n";

// 6. Recommendations
echo "<h2>6. Recommendations</h2>\n";
if (count($posts_with_coords) == 0) {
    echo "❌ <strong>No posts have coordinates!</strong><br>\n";
    echo "You need to:<br>\n";
    echo "1. Go to WordPress admin → Ordinace<br>\n";
    echo "2. Edit each ordinace and add coordinates manually, OR<br>\n";
    echo "3. Use the bulk geocoding feature if available<br>\n";
} else {
    echo "✅ Found " . count($posts_with_coords) . " posts with coordinates<br>\n";
    echo "The map should be working. Check browser console for JavaScript errors.<br>\n";
}

echo "<h2>7. Quick Fix</h2>\n";
echo "<p>If you want to add test coordinates to the first few posts without coordinates:</p>\n";
if (isset($_GET['add_test_coords']) && count($posts_without_coords) > 0) {
    $test_coords = [
        ['lat' => 50.0755, 'lng' => 14.4378], // Prague center
        ['lat' => 50.0813, 'lng' => 14.4267], // Wenceslas Square
        ['lat' => 50.0755, 'lng' => 14.4150], // Charles Square
    ];
    
    $added = 0;
    foreach (array_slice($posts_without_coords, 0, 3) as $i => $post) {
        if (isset($test_coords[$i])) {
            update_post_meta($post->ID, '_spd10_ordinace_lat', $test_coords[$i]['lat']);
            update_post_meta($post->ID, '_spd10_ordinace_lng', $test_coords[$i]['lng']);
            update_post_meta($post->ID, '_spd10_ordinace_geocode_status', 'manual');
            $added++;
        }
    }
    echo "✅ Added test coordinates to $added posts. <a href='?'>Refresh to see results</a><br>\n";
} else {
    echo "<a href='?add_test_coords=1'>Click here to add test coordinates to first 3 posts</a><br>\n";
}
?>
