<?php
/*
Plugin Name: <PERSON><PERSON><PERSON><PERSON> skupiny
Description: Plugin pro správu dětských skupin na Praze 10.
Version: 1.0
Author: <PERSON><PERSON><PERSON><PERSON>
*/

// Načtení potřeb<PERSON>ý<PERSON> souborů
require_once plugin_dir_path(__FILE__) . 'includes/post-types.php';
require_once plugin_dir_path(__FILE__) . 'includes/meta-boxes.php';
require_once plugin_dir_path(__FILE__) . 'includes/admin-columns.php';
require_once plugin_dir_path(__FILE__) . 'includes/bulk-edit.php';
require_once plugin_dir_path(__FILE__) . 'includes/shortcodes.php';
require_once plugin_dir_path(__FILE__) . 'includes/admin-settings.php';

// Inicializace pluginu
function ds_init() {
    // Registrace post typů
    ds_register_post_types();

    // Přidání meta boxů
    add_action('add_meta_boxes', 'ds_add_meta_boxes');

    // Uložení metadat
    add_action('save_post', 'ds_save_meta_boxes');

    // Přidání sloupců do administrace
    add_filter('manage_ds_posts_columns', 'ds_custom_columns');
    add_action('manage_ds_posts_custom_column', 'ds_custom_column_content', 10, 2);

    // Přidání filtrů
    add_action('restrict_manage_posts', 'ds_filter_by_ctvrt', 10, 2);
    add_action('restrict_manage_posts', 'ds_filter_by_author', 10, 2);
    add_filter('parse_query', 'ds_convert_id_to_term_in_query');
    add_filter('pre_get_posts', 'ds_filter_by_kapacita');

    // Smazání souvisejících dětských skupin při smazání provozovatele
    add_action('before_delete_post', 'ds_delete_related_groups');

    // Přidání admin stylů
    add_action('admin_enqueue_scripts', 'ds_enqueue_admin_styles');

    // Předvyplnění provozovatele při vytváření nové dětské skupiny
    add_action('admin_footer-post-new.php', 'ds_prefill_provozovatel');

    // Přidání podpory pro vlastní šablony
    add_filter('template_include', 'ds_load_custom_templates');

    // Přidání stylů pro frontend
    add_action('wp_enqueue_scripts', 'ds_enqueue_frontend_styles');
}

function ds_load_custom_templates($template) {
    if (is_singular('ds')) {
        $new_template = plugin_dir_path(__FILE__) . 'templates/single-ds.php';
        if (file_exists($new_template)) {
            return $new_template;
        }
    }
    if (is_post_type_archive('ds')) {
        $new_template = plugin_dir_path(__FILE__) . 'templates/archive-ds.php';
        if (file_exists($new_template)) {
            return $new_template;
        }
    }
    if (is_singular('provozovatel')) {
        $new_template = plugin_dir_path(__FILE__) . 'templates/single-provozovatel.php';
        if (file_exists($new_template)) {
            return $new_template;
        }
    }
    if (is_post_type_archive('provozovatel')) {
        $new_template = plugin_dir_path(__FILE__) . 'templates/archive-provozovatel.php';
        if (file_exists($new_template)) {
            return $new_template;
        }
    }
    return $template;
}

add_filter('template_include', 'ds_load_custom_templates');

function ds_flush_rewrite_rules() {
    ds_register_post_types();
    flush_rewrite_rules();
}
register_activation_hook(__FILE__, 'ds_flush_rewrite_rules');

function ds_enqueue_frontend_styles() {
    if (is_singular('ds') || is_post_type_archive('ds') || is_singular('provozovatel') || is_post_type_archive('provozovatel')) {
        wp_enqueue_style('bootstrap', 'https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css');
        wp_enqueue_script('bootstrap', 'https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js', array('jquery'), null, true);

        // Načtení vlastních CSS stylů pro dětské skupiny
        wp_enqueue_style('ds-custom-styles', plugin_dir_url(__FILE__) . 'assets/css/ds-custom-styles.css', array(), '1.0.0');
    }
}

function ds_prefill_provozovatel() {
    global $pagenow;
    if ('post-new.php' !== $pagenow || 'ds' !== $_GET['post_type'] || !isset($_GET['provozovatel_id'])) {
        return;
    }
    $provozovatel_id = intval($_GET['provozovatel_id']);
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        $('#ds_provozovatel_id').val(<?php echo $provozovatel_id; ?>);
    });
    </script>
    <?php
}

function ds_enqueue_admin_styles() {
    wp_enqueue_style('ds-admin-styles', plugin_dir_url(__FILE__) . 'assets/css/admin-style.css');
}

add_action('init', 'ds_init');

// Přidání oprávnění pro autory a redaktory
function ds_add_capabilities() {
    // Přidání oprávnění pro administrátora
    $admin = get_role('administrator');
    if ($admin) {
        // Oprávnění pro provozovatele
        $admin->add_cap('edit_provozovatel');
        $admin->add_cap('read_provozovatel');
        $admin->add_cap('delete_provozovatel');
        $admin->add_cap('edit_provozovatels');
        $admin->add_cap('edit_others_provozovatels');
        $admin->add_cap('publish_provozovatels');
        $admin->add_cap('read_private_provozovatels');
        $admin->add_cap('delete_provozovatels');
        $admin->add_cap('delete_private_provozovatels');
        $admin->add_cap('delete_published_provozovatels');
        $admin->add_cap('delete_others_provozovatels');
        $admin->add_cap('edit_private_provozovatels');
        $admin->add_cap('edit_published_provozovatels');

        // Oprávnění pro dětské skupiny
        $admin->add_cap('edit_ds');
        $admin->add_cap('read_ds');
        $admin->add_cap('delete_ds');
        $admin->add_cap('edit_dss');
        $admin->add_cap('edit_others_dss');
        $admin->add_cap('publish_dss');
        $admin->add_cap('read_private_dss');
        $admin->add_cap('delete_dss');
        $admin->add_cap('delete_private_dss');
        $admin->add_cap('delete_published_dss');
        $admin->add_cap('delete_others_dss');
        $admin->add_cap('edit_private_dss');
        $admin->add_cap('edit_published_dss');
    }

    // Přidání oprávnění pro autory a redaktory
    $roles = array('author', 'editor');
    $post_types = array('provozovatel', 'ds');

    foreach ($roles as $role) {
        $role_obj = get_role($role);
        if (!$role_obj) continue;

        foreach ($post_types as $post_type) {
            // Základní oprávnění
            $role_obj->add_cap("edit_{$post_type}");
            $role_obj->add_cap("read_{$post_type}");
            $role_obj->add_cap("delete_{$post_type}");
            $role_obj->add_cap("edit_{$post_type}s");
            $role_obj->add_cap("edit_others_{$post_type}s");
            $role_obj->add_cap("publish_{$post_type}s");
            $role_obj->add_cap("read_private_{$post_type}s");

            // Další potřebná oprávnění
            $role_obj->add_cap("delete_{$post_type}s");
            $role_obj->add_cap("delete_private_{$post_type}s");
            $role_obj->add_cap("delete_published_{$post_type}s");
            $role_obj->add_cap("delete_others_{$post_type}s");
            $role_obj->add_cap("edit_private_{$post_type}s");
            $role_obj->add_cap("edit_published_{$post_type}s");
        }
    }
}

// Přidání oprávnění při aktivaci pluginu
register_activation_hook(__FILE__, 'ds_add_capabilities');

// Funkce pro resetování oprávnění při deaktivaci pluginu
function ds_remove_capabilities() {
    $roles = array('administrator', 'editor', 'author');
    $post_types = array('provozovatel', 'ds');

    foreach ($roles as $role_name) {
        $role = get_role($role_name);
        if (!$role) continue;

        foreach ($post_types as $post_type) {
            $role->remove_cap("edit_{$post_type}");
            $role->remove_cap("read_{$post_type}");
            $role->remove_cap("delete_{$post_type}");
            $role->remove_cap("edit_{$post_type}s");
            $role->remove_cap("edit_others_{$post_type}s");
            $role->remove_cap("publish_{$post_type}s");
            $role->remove_cap("read_private_{$post_type}s");
            $role->remove_cap("delete_{$post_type}s");
            $role->remove_cap("delete_private_{$post_type}s");
            $role->remove_cap("delete_published_{$post_type}s");
            $role->remove_cap("delete_others_{$post_type}s");
            $role->remove_cap("edit_private_{$post_type}s");
            $role->remove_cap("edit_published_{$post_type}s");
        }
    }
}

// Registrace funkce pro deaktivaci pluginu
register_deactivation_hook(__FILE__, 'ds_remove_capabilities');

// Funkce pro okamžité přidání oprávnění - můžeme spustit ručně
function ds_fix_capabilities() {
    // Nejprve odstraníme všechna oprávnění
    ds_remove_capabilities();

    // Poté přidáme oprávnění znovu
    ds_add_capabilities();

    // Resetujeme rewrite rules
    flush_rewrite_rules();
}

// Přidání admin menu pro opravu oprávnění
function ds_add_fix_menu() {
    add_submenu_page(
        'edit.php?post_type=provozovatel',
        'Oprava oprávnění',
        'Oprava oprávnění',
        'manage_options',
        'ds-fix-capabilities',
        'ds_fix_capabilities_page'
    );
}
add_action('admin_menu', 'ds_add_fix_menu');

// Stránka pro opravu oprávnění
function ds_fix_capabilities_page() {
    // Kontrola, zda byl odeslán formulář
    if (isset($_POST['ds_fix_capabilities']) && check_admin_referer('ds_fix_capabilities_nonce')) {
        // Spustíme funkci pro opravu oprávnění
        ds_fix_capabilities();
        echo '<div class="notice notice-success"><p>Oprávnění byla úspěšně opravena.</p></div>';
    }

    // Zobrazení formuláře
    echo '<div class="wrap">';
    echo '<h1>Oprava oprávnění pro Provozovatele a Dětské skupiny</h1>';
    echo '<p>Tato funkce resetuje a znovu přidá všechna oprávnění pro práci s Provozovateli a Dětskými skupinami.</p>';
    echo '<form method="post">';
    wp_nonce_field('ds_fix_capabilities_nonce');
    echo '<p><input type="submit" name="ds_fix_capabilities" class="button button-primary" value="Opravit oprávnění"></p>';
    echo '</form>';
    echo '</div>';
}

function ds_delete_related_groups($post_id) {
    if (get_post_type($post_id) == 'provozovatel') {
        $args = array(
            'post_type' => 'ds',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_ds_provozovatel_id',
                    'value' => $post_id,
                ),
            ),
        );
        $related_groups = get_posts($args);

        foreach ($related_groups as $group) {
            wp_delete_post($group->ID, true);
        }
    }
}
?>
