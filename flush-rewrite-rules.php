<?php
/**
 * Simple script to flush rewrite rules for ordinace plugin
 * This should be run from WordPress admin or via direct access
 */

// Try to find WordPress
$wp_paths = [
    __DIR__ . '/../../../../wp-load.php',
    __DIR__ . '/../../../wp-load.php', 
    __DIR__ . '/../../wp-load.php',
    __DIR__ . '/../wp-load.php',
    '/var/www/html/wp-load.php',
    '/home/<USER>/public_html/wp-load.php'
];

$wp_loaded = false;
foreach ($wp_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        echo "WordPress loaded from: $path\n";
        break;
    }
}

if (!$wp_loaded) {
    echo "WordPress not found. Trying to access via HTTP...\n";
    
    // Try to trigger flush via HTTP request
    $urls = [
        'http://localhost/wp-admin/admin.php?page=spd10-ordinace-settings&flush_rewrite_rules=1',
        'http://localhost/vyvoj/ordinace-plugin/wp-admin/admin.php?page=spd10-ordinace-settings&flush_rewrite_rules=1'
    ];
    
    foreach ($urls as $url) {
        echo "Trying: $url\n";
        $response = @file_get_contents($url);
        if ($response !== false) {
            echo "Success!\n";
            break;
        }
    }
    exit;
}

// If WordPress is loaded, flush rewrite rules
echo "Flushing rewrite rules...\n";

// Set the flag for GeoJSON cache to flush rules
update_option('spd10_ordinace_flush_rewrite_rules', true);

// Flush rewrite rules
flush_rewrite_rules();

echo "Rewrite rules flushed!\n";

// Test if GeoJSON endpoint is working
echo "Testing GeoJSON endpoint...\n";

// Simulate a request to the GeoJSON endpoint
$_GET['spd10_ordinace_geojson'] = '1';
set_query_var('spd10_ordinace_geojson', '1');

// Check if the query var is recognized
if (get_query_var('spd10_ordinace_geojson')) {
    echo "✓ GeoJSON query var is working\n";
} else {
    echo "✗ GeoJSON query var not working\n";
}

// Check if rewrite rules are in place
global $wp_rewrite;
$rules = $wp_rewrite->wp_rewrite_rules();

if (isset($rules['ordinace-geojson/?$'])) {
    echo "✓ Basic GeoJSON rewrite rule found\n";
} else {
    echo "✗ Basic GeoJSON rewrite rule NOT found\n";
}

if (isset($rules['ordinace-geojson/([^/]+)/?$'])) {
    echo "✓ Type filter GeoJSON rewrite rule found\n";
} else {
    echo "✗ Type filter GeoJSON rewrite rule NOT found\n";
}

echo "Done!\n";
?>
