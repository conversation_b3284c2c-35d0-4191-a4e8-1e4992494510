<?php
function ds_custom_columns($columns) {
    $columns['provozovatel'] = 'Provozovatel';
    $columns['kapacita'] = 'Kapacita';
    $columns['ctvrt'] = 'Čtvrť';
    return $columns;
}

function ds_filter_by_kapacita($query) {
    if (!is_admin() && $query->is_main_query() && is_post_type_archive('ds')) {
        if (isset($_GET['ds_kapacita']) && !empty($_GET['ds_kapacita'])) {
            $kapacita = intval($_GET['ds_kapacita']);
            $meta_query = array(
                array(
                    'key' => '_ds_kapacita',
                    'value' => $kapacita,
                    'compare' => '>=',
                    'type' => 'NUMERIC'
                )
            );
            $query->set('meta_query', $meta_query);
        }
    }
}

function ds_custom_column_content($column, $post_id) {
    switch ($column) {
        case 'provozovatel':
            $provozovatel_id = get_post_meta($post_id, '_ds_provozovatel_id', true);
            if ($provozovatel_id) {
                $provozovatel_title = get_the_title($provozovatel_id);
                echo '<a href="' . esc_url(get_permalink($provozovatel_id)) . '">' . esc_html($provozovatel_title) . '</a>';
            } else {
                echo '&mdash;';
            }
            break;
        case 'kapacita':
            echo get_post_meta($post_id, '_ds_kapacita', true);
            break;
        case 'ctvrt':
            echo get_post_meta($post_id, '_ds_ctvrt', true);
            break;
    }
}

function ds_filter_by_ctvrt($post_type, $which) {
    if ('ds' !== $post_type) {
        return;
    }

    global $wpdb;
    $selected = isset($_GET['ds_ctvrt']) ? $_GET['ds_ctvrt'] : '';
    $ctvrti = $wpdb->get_col("SELECT DISTINCT meta_value FROM $wpdb->postmeta WHERE meta_key = '_ds_ctvrt' AND meta_value != ''");

    echo '<select name="ds_ctvrt">';
    echo '<option value="">' . __("Všechny čtvrti") . '</option>';
    foreach ($ctvrti as $ctvrt) {
        printf(
            '<option value="%s"%s>%s</option>',
            esc_attr($ctvrt),
            selected($selected, $ctvrt, false),
            esc_html($ctvrt)
        );
    }
    echo '</select>';
}

function ds_filter_by_author($post_type, $which) {
    if ('ds' !== $post_type) {
        return;
    }

    $selected = isset($_GET['author']) ? $_GET['author'] : '';
    wp_dropdown_users(array(
        'show_option_all' => __("Všichni autoři"),
        'name'            => 'author',
        'selected'        => $selected,
    ));
}

function ds_convert_id_to_term_in_query($query) {
    global $pagenow;
    $q_vars = &$query->query_vars;
    if ($pagenow == 'edit.php' && isset($q_vars['post_type']) && 'ds' == $q_vars['post_type'] && isset($q_vars['ds_ctvrt']) && is_numeric($q_vars['ds_ctvrt']) && $q_vars['ds_ctvrt'] != 0) {
        $term = get_term_by('id', $q_vars['ds_ctvrt'], 'ds_ctvrt');
        $q_vars['ds_ctvrt'] = $term->slug;
    }
}
