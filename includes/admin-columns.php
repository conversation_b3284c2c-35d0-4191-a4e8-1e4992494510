<?php
/**
 * Admin Columns for Ordinace Post Type
 *
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add custom columns to ordinace post type admin list
 */
function spd10_ordinace_custom_columns($columns) {
    // Remove date column and add our custom columns
    unset($columns['date']);

    $columns['spd10_ordinace_type'] = __('Typ ordinace', SPD10_ORDINACE_TEXT_DOMAIN);
    $columns['spd10_ordinace_doctors'] = __('Lékaři', SPD10_ORDINACE_TEXT_DOMAIN);
    $columns['spd10_ordinace_quarter'] = __('Čtvrť', SPD10_ORDINACE_TEXT_DOMAIN);
    $columns['spd10_ordinace_address'] = __('Adresa', SPD10_ORDINACE_TEXT_DOMAIN);
    $columns['spd10_ordinace_contact'] = __('Kontakt', SPD10_ORDINACE_TEXT_DOMAIN);
    $columns['date'] = __('Datum', SPD10_ORDINACE_TEXT_DOMAIN);

    return $columns;
}

/**
 * Display content for custom columns
 */
function spd10_ordinace_custom_column_content($column, $post_id) {
    switch ($column) {
        case 'spd10_ordinace_type':
            $terms = get_the_terms($post_id, 'ordinace_typ');
            if ($terms && !is_wp_error($terms)) {
                $term_names = array();
                foreach ($terms as $term) {
                    $term_names[] = esc_html($term->name);
                }
                echo implode(', ', $term_names);
            } else {
                echo '&mdash;';
            }
            break;

        case 'spd10_ordinace_doctors':
            $doctors = get_post_meta($post_id, '_spd10_ordinace_doctors_names', true);
            if (!empty($doctors)) {
                echo esc_html(wp_trim_words($doctors, 5, '...'));
            } else {
                echo '&mdash;';
            }
            break;

        case 'spd10_ordinace_quarter':
            $quarter = get_post_meta($post_id, '_spd10_ordinace_quarter', true);
            echo !empty($quarter) ? esc_html($quarter) : '&mdash;';
            break;

        case 'spd10_ordinace_address':
            $address = get_post_meta($post_id, '_spd10_ordinace_address', true);
            echo !empty($address) ? esc_html(wp_trim_words($address, 8, '...')) : '&mdash;';
            break;

        case 'spd10_ordinace_contact':
            $phone = get_post_meta($post_id, '_spd10_ordinace_phone', true);
            $email = get_post_meta($post_id, '_spd10_ordinace_email', true);

            $contact_info = array();
            if (!empty($phone)) {
                $contact_info[] = '<span title="' . esc_attr($phone) . '">' . esc_html(wp_trim_words($phone, 2, '...')) . '</span>';
            }
            if (!empty($email)) {
                $contact_info[] = '<span title="' . esc_attr($email) . '">' . esc_html(wp_trim_words($email, 2, '...')) . '</span>';
            }

            echo !empty($contact_info) ? implode('<br>', $contact_info) : '&mdash;';
            break;
    }
}

/**
 * Make custom columns sortable
 */
function spd10_ordinace_sortable_columns($columns) {
    $columns['spd10_ordinace_quarter'] = 'spd10_ordinace_quarter';
    $columns['spd10_ordinace_doctors'] = 'spd10_ordinace_doctors';
    return $columns;
}

/**
 * Handle sorting for custom columns
 */
function spd10_ordinace_column_orderby($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }

    $orderby = $query->get('orderby');

    if ('spd10_ordinace_quarter' === $orderby) {
        $query->set('meta_key', '_spd10_ordinace_quarter');
        $query->set('orderby', 'meta_value');
    } elseif ('spd10_ordinace_doctors' === $orderby) {
        $query->set('meta_key', '_spd10_ordinace_doctors_names');
        $query->set('orderby', 'meta_value');
    }
}

/**
 * Add filter dropdown for ordinace type
 */
function spd10_ordinace_filter_by_type($post_type, $which) {
    if ('ordinace' !== $post_type) {
        return;
    }

    $selected = isset($_GET['ordinace_typ']) ? $_GET['ordinace_typ'] : '';
    $terms = get_terms(array(
        'taxonomy' => 'ordinace_typ',
        'hide_empty' => true,
    ));

    if (!empty($terms) && !is_wp_error($terms)) {
        echo '<select name="ordinace_typ">';
        echo '<option value="">' . __('Všechny typy ordinací', SPD10_ORDINACE_TEXT_DOMAIN) . '</option>';
        foreach ($terms as $term) {
            printf(
                '<option value="%s"%s>%s</option>',
                esc_attr($term->slug),
                selected($selected, $term->slug, false),
                esc_html($term->name)
            );
        }
        echo '</select>';
    }
}

/**
 * Add filter dropdown for quarter
 */
function spd10_ordinace_filter_by_quarter($post_type, $which) {
    if ('ordinace' !== $post_type) {
        return;
    }

    global $wpdb;
    $selected = isset($_GET['spd10_ordinace_quarter']) ? $_GET['spd10_ordinace_quarter'] : '';
    $quarters = $wpdb->get_col(
        "SELECT DISTINCT meta_value FROM $wpdb->postmeta
         WHERE meta_key = '_spd10_ordinace_quarter'
         AND meta_value != ''
         ORDER BY meta_value"
    );

    if (!empty($quarters)) {
        echo '<select name="spd10_ordinace_quarter">';
        echo '<option value="">' . __('Všechny čtvrti', SPD10_ORDINACE_TEXT_DOMAIN) . '</option>';
        foreach ($quarters as $quarter) {
            printf(
                '<option value="%s"%s>%s</option>',
                esc_attr($quarter),
                selected($selected, $quarter, false),
                esc_html($quarter)
            );
        }
        echo '</select>';
    }
}

/**
 * Handle admin filters
 */
function spd10_ordinace_handle_admin_filters($query) {
    global $pagenow;

    if (!is_admin() || $pagenow !== 'edit.php' || !$query->is_main_query()) {
        return;
    }

    if (!isset($_GET['post_type']) || $_GET['post_type'] !== 'ordinace') {
        return;
    }

    // Handle quarter filter
    if (isset($_GET['spd10_ordinace_quarter']) && !empty($_GET['spd10_ordinace_quarter'])) {
        $query->set('meta_query', array(
            array(
                'key' => '_spd10_ordinace_quarter',
                'value' => sanitize_text_field($_GET['spd10_ordinace_quarter']),
                'compare' => '='
            )
        ));
    }
}

// Hook into WordPress admin
add_filter('manage_ordinace_posts_columns', 'spd10_ordinace_custom_columns');
add_action('manage_ordinace_posts_custom_column', 'spd10_ordinace_custom_column_content', 10, 2);
add_filter('manage_edit-ordinace_sortable_columns', 'spd10_ordinace_sortable_columns');
add_action('pre_get_posts', 'spd10_ordinace_column_orderby');
add_action('restrict_manage_posts', 'spd10_ordinace_filter_by_type', 10, 2);
add_action('restrict_manage_posts', 'spd10_ordinace_filter_by_quarter', 10, 2);
add_action('pre_get_posts', 'spd10_ordinace_handle_admin_filters');
