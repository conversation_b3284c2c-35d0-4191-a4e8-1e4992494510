<?php
/**
 * Admin Settings for Ordinace Plugin
 *
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register admin settings
 */
function spd10_ordinace_register_settings() {
    // Register settings groups
    register_setting('spd10_ordinace_data_settings', 'spd10_ordinace_google_sheet_id');
    register_setting('spd10_ordinace_data_settings', 'spd10_ordinace_sheet_tab_praktici');
    register_setting('spd10_ordinace_data_settings', 'spd10_ordinace_sheet_tab_pediatri');

    register_setting('spd10_ordinace_geocoding_settings', 'spd10_ordinace_geocoder_provider');
    register_setting('spd10_ordinace_geocoding_settings', 'spd10_ordinace_google_maps_api_key');
    register_setting('spd10_ordinace_geocoding_settings', 'spd10_ordinace_mapbox_api_key');

    register_setting('spd10_ordinace_frontend_settings', 'spd10_ordinace_posts_per_page');
    register_setting('spd10_ordinace_frontend_settings', 'spd10_ordinace_post_type_slug');
    register_setting('spd10_ordinace_frontend_settings', 'spd10_ordinace_map_provider');
    register_setting('spd10_ordinace_frontend_settings', 'spd10_ordinace_marker_color_pediatr');
    register_setting('spd10_ordinace_frontend_settings', 'spd10_ordinace_marker_color_praktik');
    register_setting('spd10_ordinace_frontend_settings', 'spd10_ordinace_map_default_zoom');
    register_setting('spd10_ordinace_frontend_settings', 'spd10_ordinace_map_center_lat');
    register_setting('spd10_ordinace_frontend_settings', 'spd10_ordinace_map_center_lng');
}

/**
 * Add admin menu page
 */
function spd10_ordinace_admin_menu() {
    add_options_page(
        __('Nastavení Ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        __('Ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'manage_options',
        'spd10-ordinace-settings',
        'spd10_ordinace_render_settings_page'
    );
}

/**
 * Render settings page
 */
function spd10_ordinace_render_settings_page() {
    // Handle form submissions
    if (isset($_POST['spd10_ordinace_action'])) {
        spd10_ordinace_handle_settings_actions();
    }

    $active_tab = isset($_GET['tab']) ? $_GET['tab'] : 'data';
    ?>
    <div class="wrap">
        <h1><?php _e('Nastavení Ordinace', SPD10_ORDINACE_TEXT_DOMAIN); ?></h1>

        <nav class="nav-tab-wrapper">
            <a href="?page=spd10-ordinace-settings&tab=data"
               class="nav-tab <?php echo $active_tab === 'data' ? 'nav-tab-active' : ''; ?>">
                <?php _e('Zdroj dat', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </a>
            <a href="?page=spd10-ordinace-settings&tab=import"
               class="nav-tab <?php echo $active_tab === 'import' ? 'nav-tab-active' : ''; ?>">
                <?php _e('Import', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </a>
            <a href="?page=spd10-ordinace-settings&tab=geocoding"
               class="nav-tab <?php echo $active_tab === 'geocoding' ? 'nav-tab-active' : ''; ?>">
                <?php _e('Geokódování', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </a>
            <a href="?page=spd10-ordinace-settings&tab=frontend"
               class="nav-tab <?php echo $active_tab === 'frontend' ? 'nav-tab-active' : ''; ?>">
                <?php _e('Frontend', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </a>
        </nav>

        <div class="tab-content">
            <?php
            switch ($active_tab) {
                case 'data':
                    spd10_ordinace_render_data_settings();
                    break;
                case 'import':
                    spd10_ordinace_render_import_settings();
                    break;
                case 'geocoding':
                    spd10_ordinace_render_geocoding_settings();
                    break;
                case 'frontend':
                    spd10_ordinace_render_frontend_settings();
                    break;
            }
            ?>
        </div>
    </div>
    <?php
}

/**
 * Render data settings tab
 */
function spd10_ordinace_render_data_settings() {
    ?>
    <form method="post" action="options.php">
        <?php settings_fields('spd10_ordinace_data_settings'); ?>

        <h2><?php _e('Google Sheets nastavení', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
        <table class="form-table">
            <tr>
                <th scope="row"><?php _e('ID Google Sheets', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="text" name="spd10_ordinace_google_sheet_id"
                           value="<?php echo esc_attr(get_option('spd10_ordinace_google_sheet_id')); ?>"
                           class="regular-text" />
                    <p class="description">
                        <?php _e('ID z URL Google Sheets (např. 1SnwP4sFp1OrWYlZxrzVJs9kZVdBFLgic4FDzlguts7k)', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </p>
                </td>
            </tr>
            <tr>
                <th scope="row"><?php _e('Název listu - Praktičtí lékaři', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="text" name="spd10_ordinace_sheet_tab_praktici"
                           value="<?php echo esc_attr(get_option('spd10_ordinace_sheet_tab_praktici', 'Praktičtí lékaři')); ?>"
                           class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row"><?php _e('Název listu - Pediatři', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="text" name="spd10_ordinace_sheet_tab_pediatri"
                           value="<?php echo esc_attr(get_option('spd10_ordinace_sheet_tab_pediatri', 'Pediatři')); ?>"
                           class="regular-text" />
                </td>
            </tr>
        </table>

        <?php submit_button(); ?>
    </form>

    <?php
    // Test connection section
    $sheet_id = get_option('spd10_ordinace_google_sheet_id');
    if (!empty($sheet_id)) {
        ?>
        <h3><?php _e('Test připojení', SPD10_ORDINACE_TEXT_DOMAIN); ?></h3>
        <form method="post">
            <input type="hidden" name="spd10_ordinace_action" value="test_connection" />
            <?php wp_nonce_field('spd10_ordinace_test_connection'); ?>
            <?php submit_button(__('Otestovat připojení k Google Sheets', SPD10_ORDINACE_TEXT_DOMAIN), 'secondary'); ?>
        </form>
        <?php
    }
}

/**
 * Render import settings tab
 */
function spd10_ordinace_render_import_settings() {
    $sheet_id = get_option('spd10_ordinace_google_sheet_id');

    if (empty($sheet_id)) {
        ?>
        <div class="notice notice-warning">
            <p><?php _e('Nejprve nastavte ID Google Sheets v záložce "Zdroj dat".', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
        </div>
        <?php
        return;
    }

    ?>
    <h2><?php _e('Import dat z Google Sheets', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>

    <div class="spd10-ordinace-import-section">
        <h3><?php _e('Spustit import', SPD10_ORDINACE_TEXT_DOMAIN); ?></h3>

        <form method="post">
            <input type="hidden" name="spd10_ordinace_action" value="run_import" />
            <?php wp_nonce_field('spd10_ordinace_run_import'); ?>

            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('List k importu', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                    <td>
                        <select name="tab_name" required>
                            <option value=""><?php _e('-- Vyberte list --', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                            <option value="<?php echo esc_attr(get_option('spd10_ordinace_sheet_tab_praktici')); ?>">
                                <?php echo esc_html(get_option('spd10_ordinace_sheet_tab_praktici', 'Praktičtí lékaři')); ?>
                            </option>
                            <option value="<?php echo esc_attr(get_option('spd10_ordinace_sheet_tab_pediatri')); ?>">
                                <?php echo esc_html(get_option('spd10_ordinace_sheet_tab_pediatri', 'Pediatři')); ?>
                            </option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Typ ordinace', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                    <td>
                        <select name="taxonomy_term">
                            <option value=""><?php _e('-- Automaticky podle listu --', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                            <option value="praktik-dospeli"><?php _e('Praktičtí lékaři pro dospělé', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                            <option value="pediatr"><?php _e('Praktičtí lékaři pro děti a dorost', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Režim', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="dry_run" value="1" checked />
                            <?php _e('Dry-run (pouze náhled změn)', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                        </label>
                        <p class="description">
                            <?php _e('Doporučujeme nejprve spustit dry-run pro kontrolu.', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                        </p>
                    </td>
                </tr>
            </table>

            <?php submit_button(__('Spustit import', SPD10_ORDINACE_TEXT_DOMAIN), 'primary'); ?>
        </form>
    </div>
    <?php
}

/**
 * Render geocoding settings tab
 */
function spd10_ordinace_render_geocoding_settings() {
    // Load required classes
    require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/interface-geocoder.php';
    require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-geocoder.php';
    require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-geocoding-cache.php';

    $geocoder = new SPD10_Ordinace_Geocoder();
    $cache = new SPD10_Ordinace_Geocoding_Cache();

    ?>
    <h2><?php _e('Nastavení geokódování', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>

    <!-- Provider Configuration -->
    <form method="post" action="options.php">
        <?php settings_fields('spd10_ordinace_geocoding_settings'); ?>

        <h3><?php _e('Geocoding Provider', SPD10_ORDINACE_TEXT_DOMAIN); ?></h3>
        <table class="form-table">
            <tr>
                <th scope="row"><?php _e('Aktivní provider', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <select name="spd10_ordinace_geocoder_provider">
                        <?php
                        $current_provider = spd10_ordinace_get_option('geocoder_provider', 'nominatim');
                        $providers = $geocoder->get_available_providers();

                        foreach ($providers as $key => $provider) {
                            $disabled = !$provider['available'] ? 'disabled' : '';
                            $selected = selected($current_provider, $key, false);
                            echo "<option value='{$key}' {$selected} {$disabled}>";
                            echo esc_html($provider['name']);
                            if (!$provider['available']) {
                                echo ' (' . __('nedostupný', SPD10_ORDINACE_TEXT_DOMAIN) . ')';
                            }
                            echo '</option>';
                        }
                        ?>
                    </select>
                    <p class="description">
                        <?php _e('Vyberte geocoding službu. Nominatim je zdarma, ostatní vyžadují API klíč.', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </p>
                </td>
            </tr>
            <tr>
                <th scope="row"><?php _e('Google Maps API klíč', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="text" name="spd10_ordinace_google_maps_api_key"
                           value="<?php echo esc_attr(get_option('spd10_ordinace_google_maps_api_key')); ?>"
                           class="regular-text" />
                    <p class="description">
                        <?php _e('Potřebný pro Google Geocoding API.', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                        <a href="https://developers.google.com/maps/documentation/geocoding/get-api-key" target="_blank">
                            <?php _e('Získat API klíč', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                        </a>
                    </p>
                </td>
            </tr>
            <tr>
                <th scope="row"><?php _e('Mapbox API klíč', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="text" name="spd10_ordinace_mapbox_api_key"
                           value="<?php echo esc_attr(get_option('spd10_ordinace_mapbox_api_key')); ?>"
                           class="regular-text" />
                    <p class="description">
                        <?php _e('Potřebný pro Mapbox Geocoding API.', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                        <a href="https://docs.mapbox.com/api/search/geocoding/" target="_blank">
                            <?php _e('Získat API klíč', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                        </a>
                    </p>
                </td>
            </tr>
        </table>

        <?php submit_button(); ?>
    </form>

    <!-- Bulk Geocoding -->
    <div class="spd10-ordinace-geocoding-section">
        <h3><?php _e('Hromadné geokódování', SPD10_ORDINACE_TEXT_DOMAIN); ?></h3>

        <?php
        $pending_addresses = $cache->get_pending_addresses(100);
        $pending_count = count($pending_addresses);
        ?>

        <p>
            <?php printf(__('Počet adres čekajících na geokódování: %d', SPD10_ORDINACE_TEXT_DOMAIN), $pending_count); ?>
        </p>

        <?php if ($pending_count > 0): ?>
        <form method="post">
            <input type="hidden" name="spd10_ordinace_action" value="bulk_geocode" />
            <?php wp_nonce_field('spd10_ordinace_bulk_geocode'); ?>

            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Velikost dávky', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                    <td>
                        <input type="number" name="batch_size" value="10" min="1" max="50" />
                        <p class="description">
                            <?php _e('Počet adres zpracovaných najednou (1-50).', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                        </p>
                    </td>
                </tr>
            </table>

            <div id="spd10-ordinace-bulk-progress" style="display: none;">
                <div class="spd10-ordinace-progress-bar">
                    <div class="spd10-ordinace-progress-fill" style="width: 0%;"></div>
                </div>
                <div class="spd10-ordinace-progress-text">0%</div>
            </div>

            <button type="button" id="spd10-ordinace-start-bulk-geocode" class="button button-primary">
                <?php _e('Spustit hromadné geokódování', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </button>

            <div id="spd10-ordinace-bulk-results" style="margin-top: 20px;"></div>
        </form>
        <?php else: ?>
        <p><?php _e('Všechny adresy jsou geokódovány.', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
        <?php endif; ?>
    </div>

    <!-- Cache Statistics -->
    <div class="spd10-ordinace-geocoding-section">
        <h3><?php _e('Statistiky cache', SPD10_ORDINACE_TEXT_DOMAIN); ?></h3>

        <?php
        $stats = $cache->get_statistics();
        $geocoder_stats = $geocoder->get_statistics();
        ?>

        <table class="wp-list-table widefat fixed striped">
            <tbody>
                <tr>
                    <td><strong><?php _e('Celkem záznamů v cache', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong></td>
                    <td><?php echo esc_html($stats['total_entries']); ?></td>
                </tr>
                <tr>
                    <td><strong><?php _e('Úspěšné geokódování', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong></td>
                    <td><?php echo esc_html($stats['success_count']); ?> (<?php echo esc_html($stats['success_rate']); ?>%)</td>
                </tr>
                <tr>
                    <td><strong><?php _e('Chyby', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong></td>
                    <td><?php echo esc_html($stats['error_count']); ?></td>
                </tr>
                <tr>
                    <td><strong><?php _e('Aktivní provider', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong></td>
                    <td><?php echo esc_html($geocoder_stats['current_provider']); ?></td>
                </tr>
                <tr>
                    <td><strong><?php _e('Požadavky tuto hodinu', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong></td>
                    <td><?php echo esc_html($geocoder_stats['requests_this_hour']); ?> / <?php echo esc_html($geocoder_stats['hourly_limit']); ?></td>
                </tr>
            </tbody>
        </table>

        <h4><?php _e('Providery v cache', SPD10_ORDINACE_TEXT_DOMAIN); ?></h4>
        <?php if (!empty($stats['providers'])): ?>
        <ul>
            <?php foreach ($stats['providers'] as $provider => $count): ?>
            <li><?php echo esc_html($provider); ?>: <?php echo esc_html($count); ?></li>
            <?php endforeach; ?>
        </ul>
        <?php else: ?>
        <p><?php _e('Žádné záznamy v cache.', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
        <?php endif; ?>

        <h4><?php _e('Akce', SPD10_ORDINACE_TEXT_DOMAIN); ?></h4>
        <form method="post" style="display: inline;">
            <input type="hidden" name="spd10_ordinace_action" value="clear_geocode_cache" />
            <?php wp_nonce_field('spd10_ordinace_clear_cache'); ?>
            <button type="submit" class="button" onclick="return confirm('<?php _e('Opravdu chcete vymazat celou cache?', SPD10_ORDINACE_TEXT_DOMAIN); ?>')">
                <?php _e('Vymazat cache', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </button>
        </form>

        <form method="post" style="display: inline; margin-left: 10px;">
            <input type="hidden" name="spd10_ordinace_action" value="export_geocode_cache" />
            <?php wp_nonce_field('spd10_ordinace_export_cache'); ?>
            <button type="submit" class="button">
                <?php _e('Export do CSV', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </button>
        </form>
    </div>

    <style>
    .spd10-ordinace-geocoding-section {
        margin: 30px 0;
        padding: 20px;
        background: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .spd10-ordinace-progress-bar {
        width: 100%;
        height: 20px;
        background: #f0f0f0;
        border-radius: 10px;
        overflow: hidden;
        margin: 10px 0;
    }

    .spd10-ordinace-progress-fill {
        height: 100%;
        background: #0073aa;
        transition: width 0.3s ease;
    }

    .spd10-ordinace-progress-text {
        text-align: center;
        font-weight: bold;
    }
    </style>

    <script>
    jQuery(document).ready(function($) {
        $('#spd10-ordinace-start-bulk-geocode').on('click', function() {
            var $btn = $(this);
            var $progress = $('#spd10-ordinace-bulk-progress');
            var $results = $('#spd10-ordinace-bulk-results');
            var batchSize = parseInt($('input[name="batch_size"]').val()) || 10;

            $btn.prop('disabled', true).text('<?php _e('Zpracovávám...', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
            $progress.show();
            $results.empty();

            var processed = 0;
            var totalPending = <?php echo $pending_count; ?>;

            function processBatch() {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'spd10_ordinace_bulk_geocode',
                        batch_size: batchSize,
                        offset: processed,
                        nonce: '<?php echo wp_create_nonce('spd10_ordinace_bulk_geocode'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            var data = response.data;
                            processed += data.processed;

                            // Update progress
                            var progress = Math.round((processed / totalPending) * 100);
                            $('.spd10-ordinace-progress-fill').css('width', progress + '%');
                            $('.spd10-ordinace-progress-text').text(progress + '%');

                            // Show results
                            data.results.forEach(function(result) {
                                var status = result.success ? '✓' : '✗';
                                var message = result.success ?
                                    'Geokódováno (' + result.provider + ')' :
                                    'Chyba: ' + result.error;

                                $results.append(
                                    '<div>' + status + ' ' + result.address + ' - ' + message + '</div>'
                                );
                            });

                            if (!data.completed) {
                                // Continue with next batch
                                setTimeout(processBatch, 1000);
                            } else {
                                $btn.prop('disabled', false).text('<?php _e('Spustit hromadné geokódování', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
                                $results.append('<div style="margin-top: 10px; font-weight: bold; color: green;">✓ <?php _e('Hromadné geokódování dokončeno!', SPD10_ORDINACE_TEXT_DOMAIN); ?></div>');
                            }
                        } else {
                            $btn.prop('disabled', false).text('<?php _e('Spustit hromadné geokódování', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
                            $results.append('<div style="color: red;">✗ ' + response.data + '</div>');
                        }
                    },
                    error: function() {
                        $btn.prop('disabled', false).text('<?php _e('Spustit hromadné geokódování', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
                        $results.append('<div style="color: red;">✗ <?php _e('Chyba při komunikaci se serverem.', SPD10_ORDINACE_TEXT_DOMAIN); ?></div>');
                    }
                });
            }

            processBatch();
        });
    });
    </script>
    <?php
}

/**
 * Render frontend settings tab (placeholder)
 */
function spd10_ordinace_render_frontend_settings() {
    // Load map provider class
    require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-map-provider.php';
    $map_provider = new SPD10_Ordinace_Map_Provider();
    $providers = $map_provider->get_all_providers();
    $default_center = $map_provider->get_default_center();
    ?>
    <form method="post" action="options.php">
        <?php settings_fields('spd10_ordinace_frontend_settings'); ?>
        <h2><?php _e('Nastavení frontendu', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>

        <h3><?php _e('Obecné nastavení', SPD10_ORDINACE_TEXT_DOMAIN); ?></h3>
        <table class="form-table">
            <tr>
                <th scope="row"><?php _e('Počet položek na stránku', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="number" min="1" max="50" name="spd10_ordinace_posts_per_page" value="<?php echo esc_attr(get_option('spd10_ordinace_posts_per_page', 10)); ?>" />
                    <p class="description"><?php _e('Nastavte výchozí počet ordinací na stránku v archivu.', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row"><?php _e('URL slug pro CPT', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="text" name="spd10_ordinace_post_type_slug" value="<?php echo esc_attr(get_option('spd10_ordinace_post_type_slug', 'ordinace')); ?>" />
                    <p class="description"><?php _e('Po změně je třeba uložit a znovu načíst trvalé odkazy (Nastavení → Trvalé odkazy).', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
        </table>

        <h3><?php _e('Nastavení mapy', SPD10_ORDINACE_TEXT_DOMAIN); ?></h3>
        <table class="form-table">
            <tr>
                <th scope="row"><?php _e('Mapový provider', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <select name="spd10_ordinace_map_provider">
                        <?php
                        $current_provider = get_option('spd10_ordinace_map_provider', 'leaflet');
                        foreach ($providers as $provider_key => $provider_config) {
                            $disabled = !$provider_config['available'] ? 'disabled' : '';
                            $status = $provider_config['available'] ? '' : ' (' . __('nedostupný', SPD10_ORDINACE_TEXT_DOMAIN) . ')';
                            printf(
                                '<option value="%s" %s %s>%s%s</option>',
                                esc_attr($provider_key),
                                selected($current_provider, $provider_key, false),
                                $disabled,
                                esc_html($provider_config['name']),
                                $status
                            );
                        }
                        ?>
                    </select>
                    <p class="description"><?php _e('Vyberte mapový provider. Některé vyžadují API klíč v záložce Geokódování.', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row"><?php _e('Výchozí zoom', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="number" min="8" max="18" name="spd10_ordinace_map_default_zoom" value="<?php echo esc_attr(get_option('spd10_ordinace_map_default_zoom', 13)); ?>" />
                    <p class="description"><?php _e('Výchozí úroveň přiblížení mapy (8-18).', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row"><?php _e('Střed mapy - zeměpisná šířka', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="number" step="0.0001" name="spd10_ordinace_map_center_lat" value="<?php echo esc_attr(get_option('spd10_ordinace_map_center_lat', $default_center['lat'])); ?>" />
                    <p class="description"><?php _e('Zeměpisná šířka středu mapy (výchozí: Praha 10).', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row"><?php _e('Střed mapy - zeměpisná délka', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="number" step="0.0001" name="spd10_ordinace_map_center_lng" value="<?php echo esc_attr(get_option('spd10_ordinace_map_center_lng', $default_center['lng'])); ?>" />
                    <p class="description"><?php _e('Zeměpisná délka středu mapy (výchozí: Praha 10).', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
        </table>

        <h3><?php _e('Barevné značky', SPD10_ORDINACE_TEXT_DOMAIN); ?></h3>
        <table class="form-table">
            <tr>
                <th scope="row"><?php _e('Barva značky - Pediatr', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="color" name="spd10_ordinace_marker_color_pediatr" value="<?php echo esc_attr(get_option('spd10_ordinace_marker_color_pediatr', '#4CAF50')); ?>" />
                    <p class="description"><?php _e('Barva značky pro pediatry na mapě.', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row"><?php _e('Barva značky - Praktický lékař', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                <td>
                    <input type="color" name="spd10_ordinace_marker_color_praktik" value="<?php echo esc_attr(get_option('spd10_ordinace_marker_color_praktik', '#2196F3')); ?>" />
                    <p class="description"><?php _e('Barva značky pro praktické lékaře na mapě.', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
        </table>

        <?php submit_button(); ?>
    </form>
    <?php
}

/**
 * Render CSV import page
 */
function spd10_ordinace_render_csv_import_page() {
    ?>
    <div class="wrap">
        <h1><?php _e('CSV Import', SPD10_ORDINACE_TEXT_DOMAIN); ?></h1>

        <!-- Import Method Tabs -->
        <div class="nav-tab-wrapper">
            <a href="#" class="nav-tab nav-tab-active" data-tab="file-upload">
                <?php _e('Nahrání souboru', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </a>
            <a href="#" class="nav-tab" data-tab="paste-data">
                <?php _e('Vložení dat (Copy-Paste)', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </a>
        </div>

        <div class="spd10-ordinace-csv-import-container">
            <!-- File Upload Tab -->
            <div id="file-upload-tab" class="import-tab-content">
                <!-- Upload Section -->
                <div class="csv-upload-section">
                    <h2><?php _e('1. Nahrání CSV souboru', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
                    <p class="description">
                        <?php _e('Nahrajte CSV soubor s ordinacemi. Soubor musí obsahovat povinný sloupec "kategorie_ordinace" s hodnotami "pediatr" nebo "praktik-dospeli".', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </p>

                    <form id="csv-upload-form" enctype="multipart/form-data">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('CSV soubor', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                                <td>
                                    <input type="file" id="csv-file-input" name="csv_file" accept=".csv" required />
                                    <p class="description">
                                        <?php _e('Maximální velikost: 10MB. Podporované kódování: UTF-8, Windows-1250.', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                                    </p>
                                </td>
                            </tr>
                        </table>

                        <p class="submit">
                            <button type="submit" class="button button-primary" id="upload-csv-btn">
                                <?php _e('Nahrát soubor', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                            </button>
                        </p>
                    </form>

                    <div id="upload-result" class="notice" style="display: none;"></div>
                </div>
            </div>

            <!-- Paste Data Tab -->
            <div id="paste-data-tab" class="import-tab-content" style="display: none;">
                <div class="csv-paste-section">
                    <h2><?php _e('1. Vložení dat z tabulky', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
                    <p class="description">
                        <?php _e('Zkopírujte data přímo z Excel/Google Sheets (Ctrl+C) a vložte je do textového pole níže (Ctrl+V). Data budou automaticky zpracována.', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </p>

                    <div class="paste-instructions">
                        <h4><?php _e('Postup:', SPD10_ORDINACE_TEXT_DOMAIN); ?></h4>
                        <ol>
                            <li><?php _e('V Excel/Google Sheets označte všechny buňky včetně hlaviček', SPD10_ORDINACE_TEXT_DOMAIN); ?></li>
                            <li><?php _e('Zkopírujte data (Ctrl+C nebo Cmd+C)', SPD10_ORDINACE_TEXT_DOMAIN); ?></li>
                            <li><?php _e('Vložte data do textového pole níže (Ctrl+V nebo Cmd+V)', SPD10_ORDINACE_TEXT_DOMAIN); ?></li>
                            <li><?php _e('Klikněte na "Zpracovat data" pro validaci', SPD10_ORDINACE_TEXT_DOMAIN); ?></li>
                        </ol>
                    </div>

                    <form id="csv-paste-form">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('Data z tabulky', SPD10_ORDINACE_TEXT_DOMAIN); ?></th>
                                <td>
                                    <textarea
                                        id="paste-data-textarea"
                                        name="paste_data"
                                        rows="15"
                                        cols="100"
                                        placeholder="<?php esc_attr_e('Vložte zde data zkopírovaná z Excel/Google Sheets...\n\nPříklad:\nkategorie_ordinace	jmena_lekaru	organizace	ctvrt	adresa	email\npediatr	Dr. Jana Nováková	Dětská ordinace	Vršovice	Korunní 1234, Praha 10	<EMAIL>\npraktik-dospeli	Dr. Petr Svoboda	Praktická ordinace	Strašnice	Průmyslová 567, Praha 10	<EMAIL>', SPD10_ORDINACE_TEXT_DOMAIN); ?>"
                                        required
                                    ></textarea>
                                    <p class="description">
                                        <?php _e('Podporované oddělovače: tabulátory (z Excelu), čárky, středníky. Automatická detekce formátu.', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                                    </p>
                                </td>
                            </tr>
                        </table>

                        <p class="submit">
                            <button type="submit" class="button button-primary" id="process-paste-btn">
                                <?php _e('Zpracovat data', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                            </button>
                            <button type="button" class="button" id="clear-paste-btn">
                                <?php _e('Vymazat', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                            </button>
                        </p>
                    </form>

                    <div id="paste-result" class="notice" style="display: none;"></div>
                </div>
            </div>

            <!-- Validation Section -->
            <div class="csv-validation-section" style="display: none;">
                <h2><?php _e('2. Validace a mapování sloupců', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
                <div id="validation-result"></div>

                <div id="column-mapping-section" style="display: none;">
                    <h3><?php _e('Mapování sloupců', SPD10_ORDINACE_TEXT_DOMAIN); ?></h3>
                    <p class="description">
                        <?php _e('Zkontrolujte mapování sloupců CSV na interní pole. Automaticky navržené mapování můžete upravit.', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </p>
                    <div id="column-mapping-table"></div>
                </div>
            </div>

            <!-- Import Section -->
            <div class="csv-import-section" style="display: none;">
                <h2><?php _e('3. Import dat', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>

                <div class="import-options">
                    <label>
                        <input type="checkbox" id="dry-run-checkbox" checked />
                        <?php _e('Dry-run (pouze náhled změn bez skutečného importu)', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </label>
                </div>

                <p class="submit">
                    <button type="button" class="button button-primary" id="start-import-btn">
                        <?php _e('Spustit import', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </button>
                    <button type="button" class="button" id="reset-import-btn">
                        <?php _e('Resetovat', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </button>
                </p>

                <div id="import-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <p class="progress-text"><?php _e('Probíhá import...', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                </div>

                <div id="import-result" style="display: none;"></div>
            </div>

            <!-- Recent Imports -->
            <div class="recent-imports-section">
                <h2><?php _e('Nedávné importy', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
                <?php spd10_ordinace_render_recent_imports(); ?>
            </div>
        </div>

        <!-- CSV Format Help -->
        <div class="csv-format-help">
            <h2><?php _e('Formát CSV souboru', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
            <div class="format-example">
                <h4><?php _e('Povinné sloupce:', SPD10_ORDINACE_TEXT_DOMAIN); ?></h4>
                <ul>
                    <li><strong>kategorie_ordinace</strong> - "pediatr" nebo "praktik-dospeli"</li>
                    <li><strong>jmena_lekaru</strong> - jména lékařů (každé na nový řádek nebo oddělené čárkou)</li>
                    <li><strong>adresa</strong> - úplná adresa pro geokódování</li>
                </ul>

                <h4><?php _e('Volitelné sloupce:', SPD10_ORDINACE_TEXT_DOMAIN); ?></h4>
                <ul>
                    <li><strong>organizace</strong> - název organizace/ordinace</li>
                    <li><strong>ctvrt</strong> - čtvrť na Praze 10</li>
                    <li><strong>email</strong> - kontaktní e-mail</li>
                    <li><strong>telefon</strong> - kontaktní telefon</li>
                    <li><strong>web</strong> - webové stránky</li>
                    <li><strong>poznamky</strong> - dodatečné informace</li>
                </ul>

                <h4><?php _e('Příklad CSV struktury:', SPD10_ORDINACE_TEXT_DOMAIN); ?></h4>
                <pre><code>kategorie_ordinace,jmena_lekaru,organizace,ctvrt,adresa,email,telefon,web
pediatr,"Dr. Jana Nováková",Dětská ordinace,Vršovice,"Korunní 1234, Praha 10",<EMAIL>,+420123456789,www.ordinace.cz
praktik-dospeli,"Dr. Petr Svoboda",Praktická ordinace,Strašnice,"Průmyslová 567, Praha 10",<EMAIL>,+420987654321,www.prakticka.cz</code></pre>
            </div>
        </div>
    </div>

    <style>
    .spd10-ordinace-csv-import-container {
        max-width: 1200px;
    }

    /* Tab Navigation */
    .nav-tab-wrapper {
        margin-bottom: 20px;
    }

    .import-tab-content {
        display: none;
    }

    .import-tab-content.active {
        display: block;
    }

    /* Sections */
    .csv-upload-section,
    .csv-paste-section,
    .csv-validation-section,
    .csv-import-section,
    .recent-imports-section {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
    }

    /* Paste Data Specific Styles */
    .paste-instructions {
        background: #f0f6fc;
        border: 1px solid #c3d9ff;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .paste-instructions h4 {
        margin-top: 0;
        color: #0073aa;
    }

    .paste-instructions ol {
        margin-bottom: 0;
    }

    .paste-instructions li {
        margin-bottom: 5px;
    }

    #paste-data-textarea {
        width: 100%;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        border: 2px dashed #c3c4c7;
        border-radius: 4px;
        padding: 10px;
        background: #fafafa;
        transition: border-color 0.3s ease;
    }

    #paste-data-textarea:focus {
        border-color: #2271b1;
        background: #fff;
        outline: none;
        box-shadow: 0 0 0 1px #2271b1;
    }

    #paste-data-textarea:not(:placeholder-shown) {
        border-style: solid;
        border-color: #00a32a;
        background: #f6fff6;
    }

    .progress-bar {
        width: 100%;
        height: 20px;
        background: #f0f0f1;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 10px;
    }

    .progress-fill {
        height: 100%;
        background: #0073aa;
        width: 0%;
        transition: width 0.3s ease;
    }

    .csv-format-help {
        background: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 20px;
        margin-top: 30px;
    }

    .format-example pre {
        background: #fff;
        border: 1px solid #ddd;
        padding: 15px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 12px;
    }

    .column-mapping-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }

    .column-mapping-table th,
    .column-mapping-table td {
        padding: 10px;
        border: 1px solid #ddd;
        text-align: left;
    }

    .column-mapping-table th {
        background: #f9f9f9;
        font-weight: bold;
    }

    .import-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin: 20px 0;
    }

    .import-stat {
        background: #f9f9f9;
        padding: 15px;
        border-radius: 4px;
        text-align: center;
    }

    .import-stat .number {
        font-size: 24px;
        font-weight: bold;
        color: #0073aa;
    }

    .import-stat .label {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }
    </style>

    <script>
    jQuery(document).ready(function($) {
        var fileKey = null;
        var columnMapping = {};
        var pasteData = null;

        // Tab switching functionality
        $('.nav-tab').on('click', function(e) {
            e.preventDefault();

            var targetTab = $(this).data('tab');

            // Update tab appearance
            $('.nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');

            // Show/hide tab content
            $('.import-tab-content').removeClass('active').hide();
            $('#' + targetTab + '-tab').addClass('active').show();

            // Reset forms and results when switching tabs
            resetImportState();
        });

        // Initialize first tab as active
        $('#file-upload-tab').addClass('active').show();

        // Reset import state
        function resetImportState() {
            fileKey = null;
            columnMapping = {};
            pasteData = null;

            // Hide validation and import sections
            $('.csv-validation-section, .csv-import-section').hide();

            // Clear results
            $('#upload-result, #paste-result, #validation-result, #import-result').hide().empty();

            // Reset forms
            $('#csv-upload-form')[0].reset();
            $('#paste-data-textarea').val('');

            // Reset buttons
            $('#upload-csv-btn').prop('disabled', false).text('<?php _e('Nahrát soubor', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
            $('#process-paste-btn').prop('disabled', false).text('<?php _e('Zpracovat data', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
        }

        // Clear paste data button
        $('#clear-paste-btn').on('click', function() {
            $('#paste-data-textarea').val('');
            $('#paste-result').hide().empty();
            $('.csv-validation-section, .csv-import-section').hide();
        });

        // Paste form handler
        $('#csv-paste-form').on('submit', function(e) {
            e.preventDefault();

            var pasteText = $('#paste-data-textarea').val().trim();

            if (!pasteText) {
                alert('<?php _e('Prosím vložte data do textového pole.', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
                return;
            }

            var $btn = $('#process-paste-btn');
            $btn.prop('disabled', true).text('<?php _e('Zpracovávám...', SPD10_ORDINACE_TEXT_DOMAIN); ?>');

            // Process paste data via AJAX
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'spd10_ordinace_process_paste_data',
                    paste_data: pasteText,
                    nonce: '<?php echo wp_create_nonce('spd10_ordinace_paste_import'); ?>'
                },
                success: function(response) {
                    console.log('SPD10 Ordinace: Paste processing response:', response);
                    if (response.success) {
                        pasteData = response.data;
                        console.log('SPD10 Ordinace: Paste data processed successfully:', pasteData);
                        showPasteSuccess(response.data);
                        showValidationResult(response.data);
                        columnMapping = response.data.column_mapping;
                        $('.csv-validation-section').show();
                        $('.csv-import-section').show();
                    } else {
                        console.error('SPD10 Ordinace: Paste processing failed:', response.data);
                        showPasteError(response.data);
                    }
                },
                error: function() {
                    showPasteError('<?php _e('Chyba při komunikaci se serverem.', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
                },
                complete: function() {
                    $btn.prop('disabled', false).text('<?php _e('Zpracovat data', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
                }
            });
        });

        // Upload form handler
        $('#csv-upload-form').on('submit', function(e) {
            e.preventDefault();

            var formData = new FormData();
            var fileInput = $('#csv-file-input')[0];

            if (!fileInput.files[0]) {
                alert('<?php _e('Prosím vyberte CSV soubor.', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
                return;
            }

            formData.append('csv_file', fileInput.files[0]);
            formData.append('action', 'spd10_ordinace_upload_csv');
            formData.append('nonce', '<?php echo wp_create_nonce('spd10_ordinace_csv_upload'); ?>');

            var $btn = $('#upload-csv-btn');
            $btn.prop('disabled', true).text('<?php _e('Nahrávám...', SPD10_ORDINACE_TEXT_DOMAIN); ?>');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        fileKey = response.data.file_key;
                        showUploadSuccess(response.data);
                        validateCSV();
                    } else {
                        showUploadError(response.data);
                    }
                },
                error: function() {
                    showUploadError('<?php _e('Chyba při komunikaci se serverem.', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
                },
                complete: function() {
                    $btn.prop('disabled', false).text('<?php _e('Nahrát soubor', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
                }
            });
        });

        // Validate CSV
        function validateCSV() {
            if (!fileKey) return;

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'spd10_ordinace_validate_csv',
                    file_key: fileKey,
                    nonce: '<?php echo wp_create_nonce('spd10_ordinace_csv_import'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        showValidationResult(response.data);
                        columnMapping = response.data.column_mapping;
                        $('.csv-validation-section').show();
                        $('.csv-import-section').show();
                    } else {
                        showValidationError(response.data);
                    }
                },
                error: function() {
                    showValidationError('<?php _e('Chyba při validaci souboru.', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
                }
            });
        }

        // Start import
        $('#start-import-btn').on('click', function() {
            var dryRun = $('#dry-run-checkbox').is(':checked');

            var $btn = $(this);
            $btn.prop('disabled', true);
            $('#import-progress').show();

            // Determine import action and data based on current tab
            var importAction = 'spd10_ordinace_import_csv';
            var importData = {
                action: importAction,
                dry_run: dryRun,
                column_mapping: columnMapping,
                nonce: '<?php echo wp_create_nonce('spd10_ordinace_csv_import'); ?>'
            };

            // Add appropriate data source
            if (fileKey) {
                importData.file_key = fileKey;
            } else if (pasteData) {
                // Serialize paste data as JSON for proper transmission
                importData.paste_data = JSON.stringify(pasteData);
                importAction = 'spd10_ordinace_import_paste_data';
                importData.action = importAction;
                importData.nonce = '<?php echo wp_create_nonce('spd10_ordinace_paste_import'); ?>';
            } else {
                showImportError('<?php _e('Žádná data k importu.', SPD10_ORDINACE_TEXT_DOMAIN); ?>');
                $btn.prop('disabled', false);
                $('#import-progress').hide();
                return;
            }

            console.log('SPD10 Ordinace: Starting import with data:', importData);

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: importData,
                timeout: 300000, // 5 minutes timeout
                success: function(response) {
                    console.log('SPD10 Ordinace: Import response received:', response);
                    $('#import-progress').hide();

                    if (response.success) {
                        console.log('SPD10 Ordinace: Import successful:', response.data);
                        showImportResult(response.data);
                    } else {
                        console.error('SPD10 Ordinace: Import failed:', response.data);
                        showImportError(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('SPD10 Ordinace: AJAX error:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        statusCode: xhr.status
                    });
                    $('#import-progress').hide();

                    var errorMsg = '<?php _e('Chyba při importu dat.', SPD10_ORDINACE_TEXT_DOMAIN); ?>';
                    if (status === 'timeout') {
                        errorMsg = '<?php _e('Import byl přerušen kvůli timeout. Zkuste importovat menší množství dat.', SPD10_ORDINACE_TEXT_DOMAIN); ?>';
                    } else if (xhr.status === 500) {
                        errorMsg = '<?php _e('Chyba serveru při importu. Zkontrolujte server logy.', SPD10_ORDINACE_TEXT_DOMAIN); ?>';
                    }

                    showImportError(errorMsg + ' (' + status + ': ' + error + ')');
                },
                complete: function() {
                    $btn.prop('disabled', false);
                }
            });
        });

        // Reset import
        $('#reset-import-btn').on('click', function() {
            if (confirm('<?php _e('Opravdu chcete resetovat import?', SPD10_ORDINACE_TEXT_DOMAIN); ?>')) {
                location.reload();
            }
        });

        // Helper functions
        function showUploadSuccess(data) {
            $('#upload-result')
                .removeClass('notice-error')
                .addClass('notice-success')
                .html('<p><strong><?php _e('Úspěch:', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong> ' + data.message + '</p>')
                .show();
        }

        function showUploadError(message) {
            $('#upload-result')
                .removeClass('notice-success')
                .addClass('notice-error')
                .html('<p><strong><?php _e('Chyba:', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong> ' + message + '</p>')
                .show();
        }

        function showPasteSuccess(data) {
            var html = '<p><strong><?php _e('Úspěch:', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong> ';
            html += '<?php _e('Data byla úspěšně zpracována.', SPD10_ORDINACE_TEXT_DOMAIN); ?> ';
            html += '<?php _e('Detekován formát:', SPD10_ORDINACE_TEXT_DOMAIN); ?> ' + data.detected_format + '</p>';

            $('#paste-result')
                .removeClass('notice-error')
                .addClass('notice-success')
                .html(html)
                .show();
        }

        function showPasteError(message) {
            $('#paste-result')
                .removeClass('notice-success')
                .addClass('notice-error')
                .html('<p><strong><?php _e('Chyba:', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong> ' + message + '</p>')
                .show();
        }

        function showValidationResult(data) {
            var html = '<div class="validation-summary">';
            html += '<h4><?php _e('Výsledek validace:', SPD10_ORDINACE_TEXT_DOMAIN); ?></h4>';
            html += '<div class="import-stats">';
            html += '<div class="import-stat"><div class="number">' + data.total_rows + '</div><div class="label"><?php _e('Celkem řádků', SPD10_ORDINACE_TEXT_DOMAIN); ?></div></div>';
            html += '<div class="import-stat"><div class="number">' + data.valid_rows + '</div><div class="label"><?php _e('Platných řádků', SPD10_ORDINACE_TEXT_DOMAIN); ?></div></div>';
            html += '<div class="import-stat"><div class="number">' + data.errors.length + '</div><div class="label"><?php _e('Chyb', SPD10_ORDINACE_TEXT_DOMAIN); ?></div></div>';
            html += '</div>';

            if (data.errors.length > 0) {
                html += '<div class="validation-errors">';
                html += '<h4><?php _e('Chyby validace:', SPD10_ORDINACE_TEXT_DOMAIN); ?></h4>';
                html += '<ul>';
                data.errors.forEach(function(error) {
                    html += '<li>' + error + '</li>';
                });
                html += '</ul>';
                html += '</div>';
            }

            html += '</div>';

            $('#validation-result').html(html);

            // Show column mapping
            if (data.column_mapping) {
                showColumnMapping(data.headers, data.column_mapping);
                $('#column-mapping-section').show();
            }
        }

        function showValidationError(message) {
            $('#validation-result').html('<div class="notice notice-error"><p><strong><?php _e('Chyba validace:', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong> ' + message + '</p></div>');
        }

        function showColumnMapping(headers, mapping) {
            var html = '<table class="column-mapping-table">';
            html += '<thead><tr><th><?php _e('CSV sloupec', SPD10_ORDINACE_TEXT_DOMAIN); ?></th><th><?php _e('Mapováno na', SPD10_ORDINACE_TEXT_DOMAIN); ?></th></tr></thead>';
            html += '<tbody>';

            headers.forEach(function(header) {
                var mappedField = mapping[header] || '<?php _e('(nemapováno)', SPD10_ORDINACE_TEXT_DOMAIN); ?>';
                html += '<tr>';
                html += '<td><strong>' + header + '</strong></td>';
                html += '<td>' + mappedField + '</td>';
                html += '</tr>';
            });

            html += '</tbody></table>';
            $('#column-mapping-table').html(html);
        }

        function showImportResult(data) {
            console.log('SPD10 Ordinace: showImportResult called with:', data);

            // Support both formats: data.stats.* (old) and data.* (new)
            var stats = data.stats || data;

            var html = '<div class="import-summary">';
            html += '<h4>' + (stats.dry_run ? '<?php _e('Náhled importu:', SPD10_ORDINACE_TEXT_DOMAIN); ?>' : '<?php _e('Výsledek importu:', SPD10_ORDINACE_TEXT_DOMAIN); ?>') + '</h4>';
            html += '<div class="import-stats">';
            html += '<div class="import-stat"><div class="number">' + (stats.processed || 0) + '</div><div class="label"><?php _e('Zpracováno', SPD10_ORDINACE_TEXT_DOMAIN); ?></div></div>';
            html += '<div class="import-stat"><div class="number">' + (stats.created || 0) + '</div><div class="label"><?php _e('Vytvořeno', SPD10_ORDINACE_TEXT_DOMAIN); ?></div></div>';
            html += '<div class="import-stat"><div class="number">' + (stats.updated || 0) + '</div><div class="label"><?php _e('Aktualizováno', SPD10_ORDINACE_TEXT_DOMAIN); ?></div></div>';
            html += '<div class="import-stat"><div class="number">' + (stats.skipped || 0) + '</div><div class="label"><?php _e('Přeskočeno', SPD10_ORDINACE_TEXT_DOMAIN); ?></div></div>';
            html += '</div>';

            var errors = stats.errors || [];
            if (errors.length > 0) {
                html += '<div class="import-errors">';
                html += '<h4><?php _e('Chyby importu:', SPD10_ORDINACE_TEXT_DOMAIN); ?></h4>';
                html += '<ul>';
                errors.forEach(function(error) {
                    html += '<li>' + error + '</li>';
                });
                html += '</ul>';
                html += '</div>';
            }

            // Show debug info if available
            if (data.debug || data.conversion_debug) {
                html += '<div class="import-debug" style="margin-top: 15px; padding: 10px; background: #f0f0f0; border-radius: 4px;">';
                html += '<h5>Debug informace:</h5>';
                html += '<pre style="font-size: 11px; max-height: 200px; overflow-y: auto;">';
                html += JSON.stringify(data.debug || data.conversion_debug, null, 2);
                html += '</pre>';
                html += '</div>';
            }

            html += '</div>';

            $('#import-result').html(html).show();
        }

        function showImportError(message) {
            $('#import-result').html('<div class="notice notice-error"><p><strong><?php _e('Chyba importu:', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong> ' + message + '</p></div>').show();
        }
    });
    </script>
    <?php
}

/**
 * Render recent imports section
 */
function spd10_ordinace_render_recent_imports() {
    $recent_logs = SPD10_Ordinace_CSV_Importer::get_recent_logs();

    if (empty($recent_logs)) {
        echo '<p>' . __('Žádné nedávné importy.', SPD10_ORDINACE_TEXT_DOMAIN) . '</p>';
        return;
    }

    echo '<table class="wp-list-table widefat fixed striped">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>' . __('Datum', SPD10_ORDINACE_TEXT_DOMAIN) . '</th>';
    echo '<th>' . __('Typ', SPD10_ORDINACE_TEXT_DOMAIN) . '</th>';
    echo '<th>' . __('Soubor', SPD10_ORDINACE_TEXT_DOMAIN) . '</th>';
    echo '<th>' . __('Výsledek', SPD10_ORDINACE_TEXT_DOMAIN) . '</th>';
    echo '<th>' . __('Statistiky', SPD10_ORDINACE_TEXT_DOMAIN) . '</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach (array_slice($recent_logs, 0, 5) as $log) {
        echo '<tr>';
        echo '<td>' . esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($log['timestamp']))) . '</td>';
        echo '<td>' . esc_html($log['type']) . '</td>';
        echo '<td>' . esc_html($log['file_name'] ?? 'N/A') . '</td>';
        echo '<td>' . ($log['dry_run'] ? __('Dry-run', SPD10_ORDINACE_TEXT_DOMAIN) : __('Skutečný import', SPD10_ORDINACE_TEXT_DOMAIN)) . '</td>';
        echo '<td>';
        if (isset($log['stats'])) {
            printf(
                __('Zpracováno: %d, Vytvořeno: %d, Aktualizováno: %d, Chyb: %d', SPD10_ORDINACE_TEXT_DOMAIN),
                $log['stats']['processed'],
                $log['stats']['created'],
                $log['stats']['updated'],
                count($log['stats']['errors'])
            );
        }
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

/**
 * Handle settings actions
 */
function spd10_ordinace_handle_settings_actions() {
    if (!current_user_can('manage_options')) {
        wp_die(__('Nemáte oprávnění k této akci.', SPD10_ORDINACE_TEXT_DOMAIN));
    }

    $action = $_POST['spd10_ordinace_action'];

    switch ($action) {
        case 'test_connection':
            if (!wp_verify_nonce($_POST['_wpnonce'], 'spd10_ordinace_test_connection')) {
                wp_die(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
            }
            spd10_ordinace_handle_test_connection();
            break;

        case 'run_import':
            if (!wp_verify_nonce($_POST['_wpnonce'], 'spd10_ordinace_run_import')) {
                wp_die(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
            }
            spd10_ordinace_handle_run_import();
            break;

        case 'clear_geocode_cache':
            if (!wp_verify_nonce($_POST['_wpnonce'], 'spd10_ordinace_clear_cache')) {
                wp_die(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
            }
            spd10_ordinace_handle_clear_geocode_cache();
            break;

        case 'export_geocode_cache':
            if (!wp_verify_nonce($_POST['_wpnonce'], 'spd10_ordinace_export_cache')) {
                wp_die(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
            }
            spd10_ordinace_handle_export_geocode_cache();
            break;
    }
}

/**
 * Handle test connection
 */
function spd10_ordinace_handle_test_connection() {
    require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-sheets-connector.php';

    $sheet_id = get_option('spd10_ordinace_google_sheet_id');
    $connector = new SPD10_Ordinace_Sheets_Connector();

    $result = $connector->test_connection($sheet_id);

    if (is_wp_error($result)) {
        add_settings_error('spd10_ordinace_settings', 'connection_failed',
            sprintf(__('Test připojení selhal: %s', SPD10_ORDINACE_TEXT_DOMAIN), $result->get_error_message()), 'error');
    } else {
        add_settings_error('spd10_ordinace_settings', 'connection_success',
            __('Připojení k Google Sheets je funkční!', SPD10_ORDINACE_TEXT_DOMAIN), 'success');
    }
}

/**
 * Handle run import
 */
function spd10_ordinace_handle_run_import() {
    require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-sheets-connector.php';
    require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-column-mapper.php';
    require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-importer.php';

    $importer = new SPD10_Ordinace_Importer();

    $args = array(
        'tab_name' => sanitize_text_field($_POST['tab_name']),
        'taxonomy_term' => sanitize_text_field($_POST['taxonomy_term']),
        'dry_run' => isset($_POST['dry_run']),
    );

    // Auto-detect taxonomy term if not specified
    if (empty($args['taxonomy_term'])) {
        if (strpos(strtolower($args['tab_name']), 'pediatr') !== false) {
            $args['taxonomy_term'] = 'pediatr';
        } else {
            $args['taxonomy_term'] = 'praktik-dospeli';
        }
    }

    $result = $importer->import($args);

    if (is_wp_error($result)) {
        add_settings_error('spd10_ordinace_settings', 'import_failed',
            sprintf(__('Import selhal: %s', SPD10_ORDINACE_TEXT_DOMAIN), $result->get_error_message()), 'error');
    } else {
        $message = sprintf(
            __('Import dokončen! Zpracováno: %d řádků, Vytvořeno: %d, Aktualizováno: %d, Přeskočeno: %d, Chyby: %d', SPD10_ORDINACE_TEXT_DOMAIN),
            $result['stats']['total_rows'],
            $result['stats']['created'],
            $result['stats']['updated'],
            $result['stats']['skipped'],
            $result['stats']['failed']
        );

        add_settings_error('spd10_ordinace_settings', 'import_success', $message, 'success');
    }
}

/**
 * Handle clear geocode cache
 */
function spd10_ordinace_handle_clear_geocode_cache() {
    require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-geocoding-cache.php';

    $cache = new SPD10_Ordinace_Geocoding_Cache();
    $cache->clear();

    add_settings_error('spd10_ordinace_settings', 'cache_cleared',
        __('Geocoding cache byla vymazána.', SPD10_ORDINACE_TEXT_DOMAIN), 'success');
}

/**
 * Handle export geocode cache
 */
function spd10_ordinace_handle_export_geocode_cache() {
    require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-geocoding-cache.php';

    $cache = new SPD10_Ordinace_Geocoding_Cache();
    $csv_content = $cache->export_to_csv();

    $filename = 'ordinace-geocoding-cache-' . date('Y-m-d-H-i-s') . '.csv';

    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . strlen($csv_content));

    echo $csv_content;
    exit;
}

// Hook admin functions
add_action('admin_init', 'spd10_ordinace_register_settings');
add_action('admin_menu', 'spd10_ordinace_admin_menu');