<?php
/**
 * Bulk Edit functionality for Dětské skupiny plugin
 * 
 * This file provides basic bulk edit functionality for the original plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add bulk edit custom box for ds post type
 */
function ds_bulk_edit_custom_box($column_name, $post_type) {
    if ($post_type == 'ds' && $column_name == 'author') {
        echo '<fieldset class="inline-edit-col-right">';
        echo '<div class="inline-edit-col">';
        echo '<label class="alignleft">';
        echo '<span class="title">Autor</span>';
        echo '<select name="bulk_edit_author">';
        echo '<option value="-1">— Beze změny —</option>';
        // Add users here if needed
        echo '</select>';
        echo '</label>';
        echo '</div>';
        echo '</fieldset>';
    }
}

/**
 * Save bulk edit data
 */
function ds_save_bulk_edit() {
    if (!isset($_POST['bulk_edit']) || !isset($_POST['post_ids'])) {
        return;
    }

    $post_ids = array_map('intval', $_POST['post_ids']);
    $new_author = isset($_POST['bulk_edit_author']) ? intval($_POST['bulk_edit_author']) : 0;

    if ($new_author > 0) {
        foreach ($post_ids as $post_id) {
            if (function_exists('wp_update_post')) {
                wp_update_post(array(
                    'ID' => $post_id,
                    'post_author' => $new_author,
                ));
            }
        }
    }
}

// Hook bulk edit functions
if (function_exists('add_action')) {
    add_action('bulk_edit_custom_box', 'ds_bulk_edit_custom_box', 10, 2);
    add_action('save_post', 'ds_save_bulk_edit');
}
