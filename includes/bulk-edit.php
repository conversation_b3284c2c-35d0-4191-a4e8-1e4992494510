<?php
function ds_bulk_edit_custom_box($column_name, $post_type) {
    if ($post_type == 'ds' && $column_name == 'author') {
        wp_dropdown_users(array(
            'name' => 'bulk_edit_author',
            'show_option_all' => __('No Change'),
        ));
    }
}

function ds_save_bulk_edit() {
    if (!isset($_POST['bulk_edit']) || !isset($_POST['post_ids'])) {
        return;
    }

    $post_ids = array_map('intval', $_POST['post_ids']);
    $new_author = isset($_POST['bulk_edit_author']) ? intval($_POST['bulk_edit_author']) : 0;

    if ($new_author) {
        foreach ($post_ids as $post_id) {
            wp_update_post(array(
                'ID' => $post_id,
                'post_author' => $new_author,
            ));
        }
    }
}

add_action('bulk_edit_custom_box', 'ds_bulk_edit_custom_box', 10, 2);
add_action('save_post', 'ds_save_bulk_edit');
