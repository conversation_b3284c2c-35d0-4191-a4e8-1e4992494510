<?php
/**
 * AJAX Handlers for Ordinace Plugin
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for handling AJAX requests
 */
class SPD10_Ordinace_Ajax_Handlers {
    
    /**
     * Initialize AJAX handlers
     */
    public function __construct() {
        add_action('wp_ajax_spd10_ordinace_geocode_address', array($this, 'handle_geocode_address'));
        add_action('wp_ajax_spd10_ordinace_bulk_geocode', array($this, 'handle_bulk_geocode'));
        add_action('wp_ajax_spd10_ordinace_clear_geocode_cache', array($this, 'handle_clear_geocode_cache'));

        // Map-related AJAX handlers
        add_action('wp_ajax_spd10_ordinace_get_map_data', array($this, 'handle_get_map_data'));
        add_action('wp_ajax_nopriv_spd10_ordinace_get_map_data', array($this, 'handle_get_map_data'));
    }
    
    /**
     * Handle single address geocoding
     */
    public function handle_geocode_address() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'spd10_ordinace_geocode')) {
            wp_send_json_error(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Check permissions
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(__('Nemáte oprávnění k této akci.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $address = sanitize_text_field($_POST['address']);
        $post_id = intval($_POST['post_id']);
        
        if (empty($address)) {
            wp_send_json_error(__('Adresa je povinná.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Load geocoder
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/interface-geocoder.php';
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-geocoder.php';
        
        $geocoder = new SPD10_Ordinace_Geocoder();
        $result = $geocoder->geocode($address);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        // Update post meta if post_id is provided
        if ($post_id > 0) {
            update_post_meta($post_id, '_spd10_ordinace_lat', $result['lat']);
            update_post_meta($post_id, '_spd10_ordinace_lng', $result['lng']);
            update_post_meta($post_id, '_spd10_ordinace_geocode_status', 'OK');
            update_post_meta($post_id, '_spd10_ordinace_geocoded_at', current_time('mysql'));
            update_post_meta($post_id, '_spd10_ordinace_geocode_provider', $result['provider']);
            update_post_meta($post_id, '_spd10_ordinace_geocode_accuracy', $result['accuracy']);
        }
        
        wp_send_json_success($result);
    }
    
    /**
     * Handle bulk geocoding
     */
    public function handle_bulk_geocode() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'spd10_ordinace_bulk_geocode')) {
            wp_send_json_error(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Nemáte oprávnění k této akci.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $batch_size = intval($_POST['batch_size']) ?: 10;
        $offset = intval($_POST['offset']) ?: 0;
        
        // Load required classes
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/interface-geocoder.php';
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-geocoder.php';
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-geocoding-cache.php';
        
        $geocoder = new SPD10_Ordinace_Geocoder();
        $cache = new SPD10_Ordinace_Geocoding_Cache();
        
        // Get addresses that need geocoding
        $pending_addresses = $cache->get_pending_addresses($batch_size);
        
        if (empty($pending_addresses)) {
            wp_send_json_success(array(
                'completed' => true,
                'message' => __('Všechny adresy jsou geokódovány.', SPD10_ORDINACE_TEXT_DOMAIN),
            ));
        }
        
        $results = array();
        $processed = 0;
        
        foreach ($pending_addresses as $address_data) {
            $post_id = $address_data['ID'];
            $address = $address_data['address'];
            
            $result = $geocoder->geocode($address);
            
            if (is_wp_error($result)) {
                update_post_meta($post_id, '_spd10_ordinace_geocode_status', 'FAILED');
                update_post_meta($post_id, '_spd10_ordinace_geocode_error', $result->get_error_message());
                
                $results[] = array(
                    'post_id' => $post_id,
                    'address' => $address,
                    'success' => false,
                    'error' => $result->get_error_message(),
                );
            } else {
                update_post_meta($post_id, '_spd10_ordinace_lat', $result['lat']);
                update_post_meta($post_id, '_spd10_ordinace_lng', $result['lng']);
                update_post_meta($post_id, '_spd10_ordinace_geocode_status', 'OK');
                update_post_meta($post_id, '_spd10_ordinace_geocoded_at', current_time('mysql'));
                update_post_meta($post_id, '_spd10_ordinace_geocode_provider', $result['provider']);
                update_post_meta($post_id, '_spd10_ordinace_geocode_accuracy', $result['accuracy']);
                
                $results[] = array(
                    'post_id' => $post_id,
                    'address' => $address,
                    'success' => true,
                    'lat' => $result['lat'],
                    'lng' => $result['lng'],
                    'provider' => $result['provider'],
                );
            }
            
            $processed++;
            
            // Small delay to be respectful to APIs
            usleep(200000); // 0.2 seconds
        }
        
        // Get total count for progress
        $total_pending = count($cache->get_pending_addresses(1000)); // Get larger count for total
        
        wp_send_json_success(array(
            'processed' => $processed,
            'results' => $results,
            'total_pending' => $total_pending,
            'completed' => $total_pending <= $batch_size,
        ));
    }
    
    /**
     * Handle clearing geocoding cache
     */
    public function handle_clear_geocode_cache() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'spd10_ordinace_clear_cache')) {
            wp_send_json_error(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Nemáte oprávnění k této akci.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-geocoding-cache.php';
        
        $cache = new SPD10_Ordinace_Geocoding_Cache();
        
        $clear_options = array();
        
        // Parse clear options from request
        if (isset($_POST['older_than_days']) && $_POST['older_than_days'] > 0) {
            $clear_options['older_than_days'] = intval($_POST['older_than_days']);
        }
        
        if (isset($_POST['provider']) && !empty($_POST['provider'])) {
            $clear_options['provider'] = sanitize_text_field($_POST['provider']);
        }
        
        if (isset($_POST['status']) && !empty($_POST['status'])) {
            $clear_options['status'] = sanitize_text_field($_POST['status']);
        }
        
        $cache->clear($clear_options);
        
        wp_send_json_success(__('Cache byla vymazána.', SPD10_ORDINACE_TEXT_DOMAIN));
    }

    /**
     * Handle getting map data for frontend
     */
    public function handle_get_map_data() {
        // Skip nonce verification for public endpoint
        // if (!wp_verify_nonce($_POST['nonce'], 'spd10_ordinace_map_nonce')) {
        //     wp_send_json_error(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
        // }

        // Get filter parameters
        $filters = array();

        if (isset($_POST['type_filter']) && !empty($_POST['type_filter']) && $_POST['type_filter'] !== '*' && $_POST['type_filter'] !== 'all') {
            $filters['type'] = sanitize_text_field($_POST['type_filter']);
        }

        if (isset($_POST['quarter_filter']) && !empty($_POST['quarter_filter']) && $_POST['quarter_filter'] !== 'all') {
            $filters['quarter'] = sanitize_text_field($_POST['quarter_filter']);
        }

        // Debug filters
        error_log('SPD10 AJAX Map Data Filters: ' . print_r($filters, true));
        error_log('SPD10 AJAX Map Data Raw POST: ' . print_r($_POST, true));

        // Skip cache for debugging - remove this later
        // $cache_key = 'spd10_ordinace_map_data_' . md5(serialize($filters));
        // $cached_data = get_transient($cache_key);
        // if (false !== $cached_data) {
        //     wp_send_json_success($cached_data);
        // }

        // Query ordinace posts - simplified filters
        $query_args = array(
            'post_type' => 'ordinace',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'meta_query' => array(
                'relation' => 'AND',
                array(
                    'key' => '_spd10_ordinace_lat',
                    'compare' => 'EXISTS',
                ),
                array(
                    'key' => '_spd10_ordinace_lng',
                    'compare' => 'EXISTS',
                ),
            ),
        );

        // Add taxonomy filter if specified
        if (!empty($filters['type'])) {
            $query_args['tax_query'] = array(
                array(
                    'taxonomy' => 'ordinace_typ',
                    'field' => 'slug',
                    'terms' => $filters['type'],
                ),
            );
        }

        // Add quarter filter if specified
        if (!empty($filters['quarter'])) {
            $query_args['meta_query'][] = array(
                'key' => '_spd10_ordinace_quarter',
                'value' => $filters['quarter'],
                'compare' => 'LIKE',
            );
        }

        // Debug logging
        error_log('SPD10 AJAX Map Data Query: ' . print_r($query_args, true));

        $ordinace_query = new WP_Query($query_args);
        $map_data = array();

        error_log('SPD10 AJAX Map Data Found posts: ' . $ordinace_query->found_posts);

        if ($ordinace_query->have_posts()) {
            while ($ordinace_query->have_posts()) {
                $ordinace_query->the_post();
                $post_id = get_the_ID();

                // Get coordinates
                $lat = get_post_meta($post_id, '_spd10_ordinace_lat', true);
                $lng = get_post_meta($post_id, '_spd10_ordinace_lng', true);

                // Debug logging for first few posts
                if (count($map_data) < 3) {
                    error_log("SPD10 AJAX Debug Post $post_id: lat=$lat, lng=$lng");
                    $all_meta = get_post_meta($post_id);
                    error_log("SPD10 AJAX Debug Post $post_id meta: " . print_r(array_keys($all_meta), true));
                }

                if (empty($lat) || empty($lng) || $lat == '0' || $lng == '0') {
                    if (count($map_data) < 3) {
                        error_log("SPD10 AJAX Debug: Skipping post $post_id - no coordinates");
                    }
                    continue;
                }

                // Get ordinace type
                $types = wp_get_post_terms($post_id, 'ordinace_typ');
                $type = !empty($types) ? $types[0]->slug : 'unknown';

                // Get metadata
                $doctors_names = get_post_meta($post_id, '_spd10_ordinace_doctors_names', true);
                $organization = get_post_meta($post_id, '_spd10_ordinace_organization', true);
                $address = get_post_meta($post_id, '_spd10_ordinace_address', true);
                $quarter = get_post_meta($post_id, '_spd10_ordinace_quarter', true);

                $map_data[] = array(
                    'id' => $post_id,
                    'title' => get_the_title(),
                    'lat' => floatval($lat),
                    'lng' => floatval($lng),
                    'type' => $type,
                    'doctors_names' => $doctors_names,
                    'organization' => $organization,
                    'address' => $address,
                    'quarter' => $quarter,
                    'permalink' => get_permalink($post_id),
                );
            }
            wp_reset_postdata();
        }

        // Debug final result
        error_log('SPD10 AJAX Map Data: Returning ' . count($map_data) . ' ordinace');

        // Skip caching for debugging
        // set_transient($cache_key, $map_data, HOUR_IN_SECONDS);

        wp_send_json_success($map_data);
    }
}

// Initialize AJAX handlers
new SPD10_Ordinace_Ajax_Handlers();
