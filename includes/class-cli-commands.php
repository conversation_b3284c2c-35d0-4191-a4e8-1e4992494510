<?php
/**
 * WP-CLI Commands for Ordinace Plugin
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WP-CLI commands for Ordinace plugin
 */
class SPD10_Ordinace_CLI_Commands {
    
    /**
     * Import data from Google Sheets
     * 
     * ## OPTIONS
     * 
     * [--sheet=<sheet_id>]
     * : Google Sheets ID. If not provided, uses the configured sheet ID.
     * 
     * [--tab=<tab_name>]
     * : Tab name to import from. Required.
     * 
     * [--dry-run]
     * : Run in dry-run mode (preview only, no actual changes).
     * 
     * [--force]
     * : Force update existing posts even if they haven't changed.
     * 
     * [--verbose]
     * : Show detailed progress information.
     * 
     * ## EXAMPLES
     * 
     *     wp ordinace import --tab="Praktičtí lékaři" --dry-run
     *     wp ordinace import --tab="Pediatři" --verbose
     *     wp ordinace import --sheet=1SnwP4sFp1OrWYlZxrzVJs9kZVdBFLgic4FDzlguts7k --tab="Praktičtí lékaři"
     * 
     * @param array $args Positional arguments
     * @param array $assoc_args Named arguments
     */
    public function import($args, $assoc_args) {
        // Load required classes
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-sheets-connector.php';
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-column-mapper.php';
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-importer.php';
        
        // Parse arguments
        $sheet_id = isset($assoc_args['sheet']) ? $assoc_args['sheet'] : spd10_ordinace_get_option('google_sheet_id');
        $tab_name = isset($assoc_args['tab']) ? $assoc_args['tab'] : '';
        $dry_run = isset($assoc_args['dry-run']);
        $force_update = isset($assoc_args['force']);
        $verbose = isset($assoc_args['verbose']);
        
        // Validate required arguments
        if (empty($sheet_id)) {
            WP_CLI::error(__('ID Google Sheets je povinné. Použijte --sheet=<id> nebo nastavte v administraci.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        if (empty($tab_name)) {
            WP_CLI::error(__('Název záložky je povinný. Použijte --tab=<název>.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Auto-detect taxonomy term
        $taxonomy_term = '';
        if (strpos(strtolower($tab_name), 'pediatr') !== false) {
            $taxonomy_term = 'pediatr';
        } else {
            $taxonomy_term = 'praktik-dospeli';
        }
        
        if ($verbose) {
            WP_CLI::log(sprintf(__('Spouštím import z Google Sheets...', SPD10_ORDINACE_TEXT_DOMAIN)));
            WP_CLI::log(sprintf(__('Sheet ID: %s', SPD10_ORDINACE_TEXT_DOMAIN), $sheet_id));
            WP_CLI::log(sprintf(__('Záložka: %s', SPD10_ORDINACE_TEXT_DOMAIN), $tab_name));
            WP_CLI::log(sprintf(__('Typ ordinace: %s', SPD10_ORDINACE_TEXT_DOMAIN), $taxonomy_term));
            WP_CLI::log(sprintf(__('Dry-run: %s', SPD10_ORDINACE_TEXT_DOMAIN), $dry_run ? 'Ano' : 'Ne'));
        }
        
        // Prepare import arguments
        $import_args = array(
            'sheet_id' => $sheet_id,
            'tab_name' => $tab_name,
            'taxonomy_term' => $taxonomy_term,
            'dry_run' => $dry_run,
            'force_update' => $force_update,
        );
        
        // Create importer and run import
        $importer = new SPD10_Ordinace_Importer();
        
        // Show progress if verbose
        if ($verbose) {
            WP_CLI::log(__('Načítání dat z Google Sheets...', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $result = $importer->import($import_args);
        
        if (is_wp_error($result)) {
            WP_CLI::error(sprintf(__('Import selhal: %s', SPD10_ORDINACE_TEXT_DOMAIN), $result->get_error_message()));
        }
        
        // Display results
        $stats = $result['stats'];
        
        WP_CLI::success(sprintf(
            __('Import dokončen! Zpracováno: %d řádků, Vytvořeno: %d, Aktualizováno: %d, Přeskočeno: %d, Chyby: %d', SPD10_ORDINACE_TEXT_DOMAIN),
            $stats['total_rows'],
            $stats['created'],
            $stats['updated'],
            $stats['skipped'],
            $stats['failed']
        ));
        
        // Show detailed messages if verbose
        if ($verbose && !empty($result['messages'])) {
            WP_CLI::log(__('Detailní zprávy:', SPD10_ORDINACE_TEXT_DOMAIN));
            foreach ($result['messages'] as $message) {
                $prefix = $message['type'] === 'warning' ? 'Warning' : 'Info';
                WP_CLI::log(sprintf('[%s] %s', $prefix, $message['message']));
            }
        }
        
        // Show errors if any
        if (!empty($result['errors'])) {
            WP_CLI::log(__('Chyby během importu:', SPD10_ORDINACE_TEXT_DOMAIN));
            foreach ($result['errors'] as $error) {
                WP_CLI::warning($error['error']);
            }
        }
        
        // Show duration
        if (isset($result['duration'])) {
            WP_CLI::log(sprintf(__('Doba trvání: %d sekund', SPD10_ORDINACE_TEXT_DOMAIN), $result['duration']));
        }
    }
    
    /**
     * Show import status and statistics
     * 
     * ## OPTIONS
     * 
     * [--logs]
     * : Show recent import logs.
     * 
     * ## EXAMPLES
     * 
     *     wp ordinace status
     *     wp ordinace status --logs
     * 
     * @param array $args Positional arguments
     * @param array $assoc_args Named arguments
     */
    public function status($args, $assoc_args) {
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-importer.php';
        
        $importer = new SPD10_Ordinace_Importer();
        $stats = $importer->get_import_statistics();
        
        WP_CLI::log(__('=== Stav importu Ordinace ===', SPD10_ORDINACE_TEXT_DOMAIN));
        WP_CLI::log(sprintf(__('Celkem importů: %d', SPD10_ORDINACE_TEXT_DOMAIN), $stats['total_imports']));
        WP_CLI::log(sprintf(__('Celkem vytvořeno: %d', SPD10_ORDINACE_TEXT_DOMAIN), $stats['total_created']));
        WP_CLI::log(sprintf(__('Celkem aktualizováno: %d', SPD10_ORDINACE_TEXT_DOMAIN), $stats['total_updated']));
        WP_CLI::log(sprintf(__('Celkem chyb: %d', SPD10_ORDINACE_TEXT_DOMAIN), $stats['total_failed']));
        
        if ($stats['last_import']) {
            $last_import = $stats['last_import'];
            WP_CLI::log(sprintf(__('Poslední import: %s', SPD10_ORDINACE_TEXT_DOMAIN), $last_import['start_time']));
            WP_CLI::log(sprintf(__('Záložka: %s', SPD10_ORDINACE_TEXT_DOMAIN), $last_import['args']['tab_name'] ?? 'N/A'));
        }
        
        // Show current ordinace count
        $ordinace_count = wp_count_posts('ordinace');
        WP_CLI::log(sprintf(__('Aktuální počet ordinací: %d', SPD10_ORDINACE_TEXT_DOMAIN), $ordinace_count->publish));
        
        // Show logs if requested
        if (isset($assoc_args['logs'])) {
            $logs = $importer->get_import_logs();
            
            if (empty($logs)) {
                WP_CLI::log(__('Žádné logy importu.', SPD10_ORDINACE_TEXT_DOMAIN));
                return;
            }
            
            WP_CLI::log(__('=== Poslední importy ===', SPD10_ORDINACE_TEXT_DOMAIN));
            
            foreach (array_reverse(array_slice($logs, -5)) as $log) {
                WP_CLI::log(sprintf(
                    '%s | %s | Řádky: %d | Vytvořeno: %d | Aktualizováno: %d | Chyby: %d',
                    $log['start_time'],
                    $log['args']['tab_name'] ?? 'N/A',
                    $log['stats']['total_rows'],
                    $log['stats']['created'],
                    $log['stats']['updated'],
                    $log['stats']['failed']
                ));
            }
        }
    }
    
    /**
     * Clear import logs
     * 
     * ## EXAMPLES
     * 
     *     wp ordinace clear-logs
     * 
     * @param array $args Positional arguments
     * @param array $assoc_args Named arguments
     */
    public function clear_logs($args, $assoc_args) {
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-importer.php';
        
        $importer = new SPD10_Ordinace_Importer();
        $importer->clear_import_logs();
        
        WP_CLI::success(__('Logy importu byly vymazány.', SPD10_ORDINACE_TEXT_DOMAIN));
    }
    
    /**
     * Test connection to Google Sheets
     * 
     * ## OPTIONS
     * 
     * [--sheet=<sheet_id>]
     * : Google Sheets ID to test. If not provided, uses the configured sheet ID.
     * 
     * ## EXAMPLES
     * 
     *     wp ordinace test-connection
     *     wp ordinace test-connection --sheet=1SnwP4sFp1OrWYlZxrzVJs9kZVdBFLgic4FDzlguts7k
     * 
     * @param array $args Positional arguments
     * @param array $assoc_args Named arguments
     */
    public function test_connection($args, $assoc_args) {
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-sheets-connector.php';
        
        $sheet_id = isset($assoc_args['sheet']) ? $assoc_args['sheet'] : spd10_ordinace_get_option('google_sheet_id');
        
        if (empty($sheet_id)) {
            WP_CLI::error(__('ID Google Sheets je povinné. Použijte --sheet=<id> nebo nastavte v administraci.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        WP_CLI::log(sprintf(__('Testuji připojení k Google Sheets: %s', SPD10_ORDINACE_TEXT_DOMAIN), $sheet_id));
        
        $connector = new SPD10_Ordinace_Sheets_Connector();
        $result = $connector->test_connection($sheet_id);
        
        if (is_wp_error($result)) {
            WP_CLI::error(sprintf(__('Test připojení selhal: %s', SPD10_ORDINACE_TEXT_DOMAIN), $result->get_error_message()));
        }
        
        WP_CLI::success(__('Připojení k Google Sheets je funkční!', SPD10_ORDINACE_TEXT_DOMAIN));
    }
    
    /**
     * Clear Google Sheets cache
     * 
     * ## OPTIONS
     * 
     * [--sheet=<sheet_id>]
     * : Google Sheets ID. If not provided, uses the configured sheet ID.
     * 
     * [--tab=<tab_name>]
     * : Specific tab to clear cache for. If not provided, clears all cache for the sheet.
     * 
     * ## EXAMPLES
     * 
     *     wp ordinace clear-cache
     *     wp ordinace clear-cache --tab="Praktičtí lékaři"
     * 
     * @param array $args Positional arguments
     * @param array $assoc_args Named arguments
     */
    public function clear_cache($args, $assoc_args) {
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-sheets-connector.php';
        
        $sheet_id = isset($assoc_args['sheet']) ? $assoc_args['sheet'] : spd10_ordinace_get_option('google_sheet_id');
        $tab_name = isset($assoc_args['tab']) ? $assoc_args['tab'] : '';
        
        if (empty($sheet_id)) {
            WP_CLI::error(__('ID Google Sheets je povinné. Použijte --sheet=<id> nebo nastavte v administraci.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $connector = new SPD10_Ordinace_Sheets_Connector();
        $connector->clear_cache($sheet_id, $tab_name);
        
        if (!empty($tab_name)) {
            WP_CLI::success(sprintf(__('Cache pro záložku "%s" byla vymazána.', SPD10_ORDINACE_TEXT_DOMAIN), $tab_name));
        } else {
            WP_CLI::success(__('Cache pro Google Sheets byla vymazána.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
    }

    /**
     * Import data from CSV file
     *
     * ## OPTIONS
     *
     * <file>
     * : Path to CSV file to import.
     *
     * [--dry-run]
     * : Run in dry-run mode (preview only, no actual changes).
     *
     * [--verbose]
     * : Show detailed progress information.
     *
     * ## EXAMPLES
     *
     *     wp ordinace import-csv /path/to/ordinace.csv --dry-run
     *     wp ordinace import-csv /path/to/ordinace.csv --verbose
     *
     * @param array $args Positional arguments
     * @param array $assoc_args Named arguments
     */
    public function import_csv($args, $assoc_args) {
        // Validate arguments
        if (empty($args[0])) {
            WP_CLI::error(__('Cesta k CSV souboru je povinná.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        $file_path = $args[0];
        $dry_run = isset($assoc_args['dry-run']);
        $verbose = isset($assoc_args['verbose']);

        // Check if file exists
        if (!file_exists($file_path)) {
            WP_CLI::error(sprintf(__('Soubor "%s" neexistuje.', SPD10_ORDINACE_TEXT_DOMAIN), $file_path));
        }

        // Load CSV importer
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-csv-importer.php';
        $csv_importer = new SPD10_Ordinace_CSV_Importer();

        WP_CLI::log(sprintf(__('Spouštím CSV import ze souboru: %s', SPD10_ORDINACE_TEXT_DOMAIN), $file_path));

        if ($dry_run) {
            WP_CLI::log(__('REŽIM DRY-RUN: Žádné změny nebudou provedeny.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        // Validate CSV file
        WP_CLI::log(__('Validuji CSV soubor...', SPD10_ORDINACE_TEXT_DOMAIN));
        $validation_result = $this->validate_csv_file_cli($file_path);

        if (is_wp_error($validation_result)) {
            WP_CLI::error($validation_result->get_error_message());
        }

        if ($verbose) {
            WP_CLI::log(sprintf(__('Nalezeno %d řádků, %d platných.', SPD10_ORDINACE_TEXT_DOMAIN),
                $validation_result['total_rows'], $validation_result['valid_rows']));

            if (!empty($validation_result['errors'])) {
                WP_CLI::warning(sprintf(__('Nalezeno %d chyb validace:', SPD10_ORDINACE_TEXT_DOMAIN), count($validation_result['errors'])));
                foreach ($validation_result['errors'] as $error) {
                    WP_CLI::log('  - ' . $error);
                }
            }
        }

        // Import data
        WP_CLI::log(__('Spouštím import dat...', SPD10_ORDINACE_TEXT_DOMAIN));
        $import_result = $this->import_csv_data_cli($file_path, $dry_run, $validation_result['column_mapping']);

        if (is_wp_error($import_result)) {
            WP_CLI::error($import_result->get_error_message());
        }

        // Display results
        $stats = $import_result['stats'];

        WP_CLI::log('');
        WP_CLI::log(__('=== VÝSLEDKY IMPORTU ===', SPD10_ORDINACE_TEXT_DOMAIN));
        WP_CLI::log(sprintf(__('Zpracováno řádků: %d', SPD10_ORDINACE_TEXT_DOMAIN), $stats['processed']));
        WP_CLI::log(sprintf(__('Vytvořeno: %d', SPD10_ORDINACE_TEXT_DOMAIN), $stats['created']));
        WP_CLI::log(sprintf(__('Aktualizováno: %d', SPD10_ORDINACE_TEXT_DOMAIN), $stats['updated']));
        WP_CLI::log(sprintf(__('Přeskočeno: %d', SPD10_ORDINACE_TEXT_DOMAIN), $stats['skipped']));

        if (!empty($stats['errors'])) {
            WP_CLI::warning(sprintf(__('Chyby: %d', SPD10_ORDINACE_TEXT_DOMAIN), count($stats['errors'])));
            if ($verbose) {
                foreach ($stats['errors'] as $error) {
                    WP_CLI::log('  - ' . $error);
                }
            }
        }

        if ($dry_run) {
            WP_CLI::success(__('Dry-run dokončen. Pro skutečný import spusťte příkaz bez --dry-run.', SPD10_ORDINACE_TEXT_DOMAIN));
        } else {
            WP_CLI::success(__('CSV import dokončen.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
    }

    /**
     * Validate CSV file for CLI
     */
    private function validate_csv_file_cli($file_path) {
        // Use reflection to access private method
        $csv_importer = new SPD10_Ordinace_CSV_Importer();
        $reflection = new ReflectionClass($csv_importer);
        $method = $reflection->getMethod('validate_csv_file');
        $method->setAccessible(true);

        return $method->invoke($csv_importer, $file_path);
    }

    /**
     * Import CSV data for CLI
     */
    private function import_csv_data_cli($file_path, $dry_run, $column_mapping) {
        // Use reflection to access private method
        $csv_importer = new SPD10_Ordinace_CSV_Importer();
        $reflection = new ReflectionClass($csv_importer);
        $method = $reflection->getMethod('import_csv_data');
        $method->setAccessible(true);

        return $method->invoke($csv_importer, $file_path, $dry_run, $column_mapping);
    }

    /**
     * Process pending geocoding requests
     *
     * ## OPTIONS
     *
     * [--limit=<number>]
     * : Maximum number of addresses to process. Default: 10
     *
     * [--force]
     * : Force re-geocoding of failed addresses
     *
     * ## EXAMPLES
     *
     *     wp ordinace geocode-pending
     *     wp ordinace geocode-pending --limit=50
     *     wp ordinace geocode-pending --force
     */
    public function geocode_pending($args, $assoc_args) {
        $limit = isset($assoc_args['limit']) ? intval($assoc_args['limit']) : 10;
        $force = isset($assoc_args['force']);

        // Find posts with pending geocoding
        $meta_query = array(
            array(
                'key' => '_spd10_ordinace_geocode_status',
                'value' => 'PENDING',
            ),
        );

        if ($force) {
            // Also include failed geocoding if force is specified
            $meta_query = array(
                'relation' => 'OR',
                array(
                    'key' => '_spd10_ordinace_geocode_status',
                    'value' => 'PENDING',
                ),
                array(
                    'key' => '_spd10_ordinace_geocode_status',
                    'value' => 'FAILED',
                ),
            );
        }

        $posts = get_posts(array(
            'post_type' => 'ordinace',
            'post_status' => 'publish',
            'numberposts' => $limit,
            'meta_query' => $meta_query,
        ));

        if (empty($posts)) {
            WP_CLI::success('No pending geocoding requests found.');
            return;
        }

        WP_CLI::log(sprintf('Found %d posts with pending geocoding.', count($posts)));

        if (!class_exists('SPD10_Ordinace_Geocoder')) {
            WP_CLI::error('Geocoder class not found.');
            return;
        }

        $geocoder = new SPD10_Ordinace_Geocoder();
        $processed = 0;
        $success = 0;
        $failed = 0;

        foreach ($posts as $post) {
            $address = get_post_meta($post->ID, '_spd10_ordinace_address', true);

            if (empty($address)) {
                WP_CLI::warning(sprintf('Post %d has no address, skipping.', $post->ID));
                continue;
            }

            WP_CLI::log(sprintf('Geocoding post %d: %s', $post->ID, $address));

            try {
                $result = $geocoder->geocode($address, true); // Use cache

                if (!is_wp_error($result)) {
                    update_post_meta($post->ID, '_spd10_ordinace_lat', $result['lat']);
                    update_post_meta($post->ID, '_spd10_ordinace_lng', $result['lng']);
                    update_post_meta($post->ID, '_spd10_ordinace_geocode_status', 'OK');
                    if (isset($result['formatted_address'])) {
                        update_post_meta($post->ID, '_spd10_ordinace_formatted_address', $result['formatted_address']);
                    }
                    $success++;
                    WP_CLI::log(sprintf('✓ Success: %s, %s', $result['lat'], $result['lng']));
                } else {
                    update_post_meta($post->ID, '_spd10_ordinace_geocode_status', 'FAILED');
                    update_post_meta($post->ID, '_spd10_ordinace_geocode_error', $result->get_error_message());
                    $failed++;
                    WP_CLI::warning(sprintf('✗ Failed: %s', $result->get_error_message()));
                }
            } catch (Exception $e) {
                update_post_meta($post->ID, '_spd10_ordinace_geocode_status', 'FAILED');
                update_post_meta($post->ID, '_spd10_ordinace_geocode_error', $e->getMessage());
                $failed++;
                WP_CLI::warning(sprintf('✗ Exception: %s', $e->getMessage()));
            }

            $processed++;

            // Small delay to avoid rate limiting
            usleep(100000); // 0.1 second
        }

        WP_CLI::success(sprintf(
            'Processed %d addresses. Success: %d, Failed: %d',
            $processed,
            $success,
            $failed
        ));
    }
}

// Register WP-CLI commands if WP-CLI is available
if (defined('WP_CLI') && WP_CLI) {
    WP_CLI::add_command('ordinace', 'SPD10_Ordinace_CLI_Commands');
}
