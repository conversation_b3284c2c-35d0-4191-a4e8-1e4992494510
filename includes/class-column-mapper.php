<?php
/**
 * Column Mapper Class
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for mapping Google Sheets columns to ordinace metadata
 */
class SPD10_Ordinace_Column_Mapper {
    
    /**
     * Get default column mapping
     * 
     * @return array Default mapping configuration
     */
    public function get_default_mapping() {
        return array(
            'doctors_names' => array(
                'label' => __('<PERSON><PERSON><PERSON>', SPD10_ORDINACE_TEXT_DOMAIN),
                'required' => false,
                'type' => 'text',
                'description' => __('<PERSON><PERSON><PERSON> (oddělená čárkami nebo na nových řádcích)', SPD10_ORDINACE_TEXT_DOMAIN),
                'common_names' => array('<PERSON><PERSON><PERSON> lé<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Doctor Names', 'Names'),
            ),
            'organization' => array(
                'label' => __('Organizace', SPD10_ORDINACE_TEXT_DOMAIN),
                'required' => false,
                'type' => 'text',
                'description' => __('Název organizace nebo ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
                'common_names' => array('Organizace', 'Název', 'Organization', 'Name', 'Ordinace'),
            ),
            'quarter' => array(
                'label' => __('Čtvrť', SPD10_ORDINACE_TEXT_DOMAIN),
                'required' => false,
                'type' => 'text',
                'description' => __('Čtvrť v Praze 10', SPD10_ORDINACE_TEXT_DOMAIN),
                'common_names' => array('Čtvrť', 'Quarter', 'Lokalita', 'Oblast'),
            ),
            'address' => array(
                'label' => __('Adresa', SPD10_ORDINACE_TEXT_DOMAIN),
                'required' => true,
                'type' => 'text',
                'description' => __('Úplná adresa ordinace (povinné pole)', SPD10_ORDINACE_TEXT_DOMAIN),
                'common_names' => array('Adresa', 'Address', 'Ulice', 'Místo'),
            ),
            'email' => array(
                'label' => __('E-mail', SPD10_ORDINACE_TEXT_DOMAIN),
                'required' => false,
                'type' => 'email',
                'description' => __('Kontaktní e-mailová adresa', SPD10_ORDINACE_TEXT_DOMAIN),
                'common_names' => array('E-mail', 'Email', 'Kontakt', 'Mail'),
            ),
            'phone' => array(
                'label' => __('Telefon', SPD10_ORDINACE_TEXT_DOMAIN),
                'required' => false,
                'type' => 'text',
                'description' => __('Kontaktní telefon', SPD10_ORDINACE_TEXT_DOMAIN),
                'common_names' => array('Telefon', 'Phone', 'Tel', 'Mobil'),
            ),
            'website' => array(
                'label' => __('Webové stránky', SPD10_ORDINACE_TEXT_DOMAIN),
                'required' => false,
                'type' => 'url',
                'description' => __('URL webových stránek', SPD10_ORDINACE_TEXT_DOMAIN),
                'common_names' => array('Web', 'Website', 'Webové stránky', 'URL', 'Stránky'),
            ),
        );
    }
    
    /**
     * Get saved column mapping for specific sheet and tab
     * 
     * @param string $sheet_id Google Sheets ID
     * @param string $tab_name Tab name
     * @return array Saved mapping or default mapping
     */
    public function get_mapping($sheet_id, $tab_name) {
        $mapping_key = $this->get_mapping_key($sheet_id, $tab_name);
        $saved_mapping = get_option($mapping_key, array());
        
        if (empty($saved_mapping)) {
            return $this->get_default_mapping();
        }
        
        // Merge with defaults to ensure all fields are present
        $default_mapping = $this->get_default_mapping();
        foreach ($default_mapping as $field => $config) {
            if (!isset($saved_mapping[$field])) {
                $saved_mapping[$field] = $config;
            } else {
                // Preserve saved column but merge other config
                $saved_mapping[$field] = array_merge($config, $saved_mapping[$field]);
            }
        }
        
        return $saved_mapping;
    }
    
    /**
     * Save column mapping
     * 
     * @param string $sheet_id Google Sheets ID
     * @param string $tab_name Tab name
     * @param array $mapping Column mapping
     * @return bool Success
     */
    public function save_mapping($sheet_id, $tab_name, $mapping) {
        $mapping_key = $this->get_mapping_key($sheet_id, $tab_name);
        return update_option($mapping_key, $mapping);
    }
    
    /**
     * Auto-detect column mapping based on header row
     * 
     * @param array $header_row Header row from CSV
     * @return array Suggested mapping
     */
    public function auto_detect_mapping($header_row) {
        $default_mapping = $this->get_default_mapping();
        $detected_mapping = array();
        
        foreach ($default_mapping as $field => $config) {
            $detected_mapping[$field] = $config;
            $detected_mapping[$field]['column'] = '';
            
            // Try to find matching column
            foreach ($header_row as $column_name) {
                $column_name_clean = trim(strtolower($column_name));
                
                foreach ($config['common_names'] as $common_name) {
                    $common_name_clean = trim(strtolower($common_name));
                    
                    if ($column_name_clean === $common_name_clean || 
                        strpos($column_name_clean, $common_name_clean) !== false ||
                        strpos($common_name_clean, $column_name_clean) !== false) {
                        
                        $detected_mapping[$field]['column'] = $column_name;
                        break 2; // Break both loops
                    }
                }
            }
        }
        
        return $detected_mapping;
    }
    
    /**
     * Validate column mapping
     * 
     * @param array $mapping Column mapping
     * @param array $header_row Available columns
     * @return array|WP_Error Validation result or error
     */
    public function validate_mapping($mapping, $header_row) {
        $errors = array();
        $warnings = array();
        
        foreach ($mapping as $field => $config) {
            // Check required fields
            if (!empty($config['required']) && empty($config['column'])) {
                $errors[] = sprintf(
                    __('Povinné pole "%s" nemá přiřazený sloupec.', SPD10_ORDINACE_TEXT_DOMAIN),
                    $config['label']
                );
                continue;
            }
            
            // Check if selected column exists
            if (!empty($config['column']) && !in_array($config['column'], $header_row)) {
                $errors[] = sprintf(
                    __('Sloupec "%s" pro pole "%s" neexistuje v CSV.', SPD10_ORDINACE_TEXT_DOMAIN),
                    $config['column'],
                    $config['label']
                );
            }
        }
        
        // Check for duplicate column assignments
        $used_columns = array();
        foreach ($mapping as $field => $config) {
            if (!empty($config['column'])) {
                if (in_array($config['column'], $used_columns)) {
                    $warnings[] = sprintf(
                        __('Sloupec "%s" je použit pro více polí.', SPD10_ORDINACE_TEXT_DOMAIN),
                        $config['column']
                    );
                } else {
                    $used_columns[] = $config['column'];
                }
            }
        }
        
        if (!empty($errors)) {
            return new WP_Error('mapping_validation_failed', 
                __('Mapování sloupců obsahuje chyby:', SPD10_ORDINACE_TEXT_DOMAIN), 
                array('errors' => $errors, 'warnings' => $warnings));
        }
        
        return array(
            'valid' => true,
            'warnings' => $warnings,
        );
    }
    
    /**
     * Map row data according to column mapping
     * 
     * @param array $row_data Raw row data from CSV
     * @param array $mapping Column mapping
     * @return array Mapped data
     */
    public function map_row_data($row_data, $mapping) {
        $mapped_data = array();
        
        foreach ($mapping as $field => $config) {
            $value = '';
            
            if (!empty($config['column']) && isset($row_data[$config['column']])) {
                $value = $row_data[$config['column']];
                
                // Process value based on field type
                $value = $this->process_field_value($value, $config['type'], $field);
            }
            
            $mapped_data[$field] = $value;
        }
        
        // Add source row tracking
        $mapped_data['source_row_id'] = isset($row_data['_row_number']) ? $row_data['_row_number'] : '';
        $mapped_data['updated_from_source_at'] = current_time('mysql');
        
        return $mapped_data;
    }
    
    /**
     * Process field value based on type
     * 
     * @param string $value Raw value
     * @param string $type Field type
     * @param string $field Field name
     * @return string Processed value
     */
    private function process_field_value($value, $type, $field) {
        $value = trim($value);
        
        switch ($type) {
            case 'email':
                // Basic email validation
                if (!empty($value) && !is_email($value)) {
                    return ''; // Invalid email, return empty
                }
                break;
                
            case 'url':
                // Basic URL validation
                if (!empty($value)) {
                    // Add http:// if missing
                    if (!preg_match('/^https?:\/\//', $value)) {
                        $value = 'http://' . $value;
                    }
                    if (!filter_var($value, FILTER_VALIDATE_URL)) {
                        return ''; // Invalid URL, return empty
                    }
                }
                break;
                
            case 'text':
            default:
                // For doctors_names, convert commas to newlines
                if ($field === 'doctors_names' && !empty($value)) {
                    // Split by comma and clean up
                    $names = array_map('trim', explode(',', $value));
                    $names = array_filter($names); // Remove empty names
                    $value = implode("\n", $names);
                }
                break;
        }
        
        return $value;
    }
    
    /**
     * Get mapping option key
     * 
     * @param string $sheet_id Google Sheets ID
     * @param string $tab_name Tab name
     * @return string Option key
     */
    private function get_mapping_key($sheet_id, $tab_name) {
        return 'spd10_ordinace_mapping_' . md5($sheet_id . '_' . $tab_name);
    }
    
    /**
     * Get all available mappings
     * 
     * @return array All saved mappings
     */
    public function get_all_mappings() {
        global $wpdb;
        
        $option_name_pattern = 'spd10_ordinace_mapping_%';
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE %s",
            $option_name_pattern
        ));
        
        $mappings = array();
        foreach ($results as $result) {
            $mappings[$result->option_name] = maybe_unserialize($result->option_value);
        }
        
        return $mappings;
    }
}
