<?php
/**
 * Cron Scheduler for Ordinace Plugin
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for managing scheduled imports
 */
class SPD10_Ordinace_Cron_Scheduler {
    
    /**
     * Cron hook name
     */
    const CRON_HOOK = 'spd10_ordinace_scheduled_import';
    
    /**
     * Initialize cron scheduler
     */
    public function __construct() {
        add_action(self::CRON_HOOK, array($this, 'run_scheduled_import'));
        add_action('wp', array($this, 'schedule_import_if_needed'));

        // CSV cleanup hook
        add_action('spd10_ordinace_csv_cleanup', array($this, 'run_csv_cleanup'));
        add_action('wp', array($this, 'schedule_csv_cleanup_if_needed'));

        // Clean up on plugin deactivation
        register_deactivation_hook(SPD10_ORDINACE_PLUGIN_FILE, array($this, 'clear_scheduled_import'));
    }
    
    /**
     * Schedule import if needed
     */
    public function schedule_import_if_needed() {
        $enabled = spd10_ordinace_get_option('cron_enabled', false);
        $frequency = spd10_ordinace_get_option('cron_frequency', 'daily');
        
        if ($enabled && !wp_next_scheduled(self::CRON_HOOK)) {
            $this->schedule_import($frequency);
        } elseif (!$enabled && wp_next_scheduled(self::CRON_HOOK)) {
            $this->clear_scheduled_import();
        }
    }
    
    /**
     * Schedule import
     * 
     * @param string $frequency Cron frequency (hourly, daily, weekly)
     */
    public function schedule_import($frequency = 'daily') {
        // Clear existing schedule first
        $this->clear_scheduled_import();
        
        // Validate frequency
        $allowed_frequencies = array('hourly', 'daily', 'weekly');
        if (!in_array($frequency, $allowed_frequencies)) {
            $frequency = 'daily';
        }
        
        // Schedule new import
        $scheduled = wp_schedule_event(time(), $frequency, self::CRON_HOOK);
        
        if ($scheduled === false) {
            error_log('SPD10 Ordinace: Failed to schedule import');
            return false;
        }
        
        error_log(sprintf('SPD10 Ordinace: Scheduled import with frequency: %s', $frequency));
        return true;
    }
    
    /**
     * Clear scheduled import
     */
    public function clear_scheduled_import() {
        $timestamp = wp_next_scheduled(self::CRON_HOOK);
        if ($timestamp) {
            wp_unschedule_event($timestamp, self::CRON_HOOK);
            error_log('SPD10 Ordinace: Cleared scheduled import');
        }
    }
    
    /**
     * Run scheduled import
     */
    public function run_scheduled_import() {
        // Load required classes
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-sheets-connector.php';
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-column-mapper.php';
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-importer.php';
        
        $importer = new SPD10_Ordinace_Importer();
        
        // Get configured tabs to import
        $tabs_to_import = $this->get_tabs_to_import();
        
        if (empty($tabs_to_import)) {
            error_log('SPD10 Ordinace: No tabs configured for scheduled import');
            return;
        }
        
        $overall_success = true;
        $results = array();
        
        foreach ($tabs_to_import as $tab_config) {
            $args = array(
                'tab_name' => $tab_config['tab_name'],
                'taxonomy_term' => $tab_config['taxonomy_term'],
                'dry_run' => false,
                'force_update' => false,
            );
            
            $result = $importer->import($args);
            
            if (is_wp_error($result)) {
                $overall_success = false;
                $results[] = array(
                    'tab' => $tab_config['tab_name'],
                    'success' => false,
                    'error' => $result->get_error_message(),
                );
                
                error_log(sprintf('SPD10 Ordinace: Scheduled import failed for tab "%s": %s', 
                    $tab_config['tab_name'], $result->get_error_message()));
            } else {
                $results[] = array(
                    'tab' => $tab_config['tab_name'],
                    'success' => true,
                    'stats' => $result['stats'],
                );
                
                error_log(sprintf('SPD10 Ordinace: Scheduled import completed for tab "%s": %d created, %d updated, %d failed', 
                    $tab_config['tab_name'], 
                    $result['stats']['created'], 
                    $result['stats']['updated'], 
                    $result['stats']['failed']));
            }
        }
        
        // Send notification email if configured
        $this->send_notification_email($results, $overall_success);
        
        // Update last run timestamp
        spd10_ordinace_update_option('cron_last_run', current_time('mysql'));
    }
    
    /**
     * Get tabs to import for scheduled runs
     * 
     * @return array Array of tab configurations
     */
    private function get_tabs_to_import() {
        $tabs = array();
        
        // Add praktici tab if enabled
        if (spd10_ordinace_get_option('cron_import_praktici', true)) {
            $tabs[] = array(
                'tab_name' => spd10_ordinace_get_option('sheet_tab_praktici', 'Praktičtí lékaři'),
                'taxonomy_term' => 'praktik-dospeli',
            );
        }
        
        // Add pediatri tab if enabled
        if (spd10_ordinace_get_option('cron_import_pediatri', true)) {
            $tabs[] = array(
                'tab_name' => spd10_ordinace_get_option('sheet_tab_pediatri', 'Pediatři'),
                'taxonomy_term' => 'pediatr',
            );
        }
        
        return $tabs;
    }
    
    /**
     * Send notification email
     * 
     * @param array $results Import results
     * @param bool $overall_success Overall success status
     */
    private function send_notification_email($results, $overall_success) {
        $email_enabled = spd10_ordinace_get_option('cron_email_notifications', false);
        $email_address = spd10_ordinace_get_option('cron_email_address', get_option('admin_email'));
        
        // Only send email if enabled and there are failures (or if configured to always send)
        $send_always = spd10_ordinace_get_option('cron_email_always', false);
        
        if (!$email_enabled || (!$send_always && $overall_success)) {
            return;
        }
        
        if (empty($email_address) || !is_email($email_address)) {
            error_log('SPD10 Ordinace: Invalid email address for notifications');
            return;
        }
        
        // Prepare email content
        $subject = sprintf(__('[%s] Výsledek automatického importu ordinací', SPD10_ORDINACE_TEXT_DOMAIN), get_bloginfo('name'));
        
        $message = sprintf(__('Automatický import ordinací byl dokončen v %s.', SPD10_ORDINACE_TEXT_DOMAIN), current_time('Y-m-d H:i:s')) . "\n\n";
        
        $message .= __('Výsledky:', SPD10_ORDINACE_TEXT_DOMAIN) . "\n";
        $message .= str_repeat('-', 50) . "\n";
        
        foreach ($results as $result) {
            $message .= sprintf(__('Záložka: %s', SPD10_ORDINACE_TEXT_DOMAIN), $result['tab']) . "\n";
            
            if ($result['success']) {
                $stats = $result['stats'];
                $message .= sprintf(__('✓ Úspěch - Vytvořeno: %d, Aktualizováno: %d, Chyby: %d', SPD10_ORDINACE_TEXT_DOMAIN), 
                    $stats['created'], $stats['updated'], $stats['failed']) . "\n";
            } else {
                $message .= sprintf(__('✗ Chyba: %s', SPD10_ORDINACE_TEXT_DOMAIN), $result['error']) . "\n";
            }
            
            $message .= "\n";
        }
        
        $message .= sprintf(__('Celkový stav: %s', SPD10_ORDINACE_TEXT_DOMAIN), 
            $overall_success ? __('Úspěch', SPD10_ORDINACE_TEXT_DOMAIN) : __('Chyba', SPD10_ORDINACE_TEXT_DOMAIN)) . "\n\n";
        
        $message .= sprintf(__('Administrace pluginu: %s', SPD10_ORDINACE_TEXT_DOMAIN), 
            admin_url('options-general.php?page=spd10-ordinace-settings')) . "\n";
        
        // Send email
        $sent = wp_mail($email_address, $subject, $message);
        
        if (!$sent) {
            error_log('SPD10 Ordinace: Failed to send notification email');
        } else {
            error_log(sprintf('SPD10 Ordinace: Notification email sent to %s', $email_address));
        }
    }
    
    /**
     * Get next scheduled run time
     * 
     * @return string|false Next run time or false if not scheduled
     */
    public function get_next_scheduled_run() {
        $timestamp = wp_next_scheduled(self::CRON_HOOK);
        
        if (!$timestamp) {
            return false;
        }
        
        return date('Y-m-d H:i:s', $timestamp);
    }
    
    /**
     * Get last run time
     * 
     * @return string|false Last run time or false if never run
     */
    public function get_last_run() {
        return spd10_ordinace_get_option('cron_last_run', false);
    }
    
    /**
     * Check if cron is enabled
     * 
     * @return bool True if enabled
     */
    public function is_enabled() {
        return (bool) spd10_ordinace_get_option('cron_enabled', false);
    }
    
    /**
     * Enable cron scheduling
     * 
     * @param string $frequency Cron frequency
     * @return bool Success
     */
    public function enable($frequency = 'daily') {
        spd10_ordinace_update_option('cron_enabled', true);
        spd10_ordinace_update_option('cron_frequency', $frequency);
        
        return $this->schedule_import($frequency);
    }
    
    /**
     * Disable cron scheduling
     */
    public function disable() {
        spd10_ordinace_update_option('cron_enabled', false);
        $this->clear_scheduled_import();
    }
    
    /**
     * Get available cron frequencies
     * 
     * @return array Available frequencies
     */
    public function get_available_frequencies() {
        return array(
            'hourly' => __('Každou hodinu', SPD10_ORDINACE_TEXT_DOMAIN),
            'daily' => __('Denně', SPD10_ORDINACE_TEXT_DOMAIN),
            'weekly' => __('Týdně', SPD10_ORDINACE_TEXT_DOMAIN),
        );
    }

    /**
     * Schedule CSV cleanup if needed
     */
    public function schedule_csv_cleanup_if_needed() {
        if (!wp_next_scheduled('spd10_ordinace_csv_cleanup')) {
            wp_schedule_event(time(), 'daily', 'spd10_ordinace_csv_cleanup');
        }
    }

    /**
     * Run CSV cleanup
     */
    public function run_csv_cleanup() {
        if (class_exists('SPD10_Ordinace_CSV_Importer')) {
            SPD10_Ordinace_CSV_Importer::cleanup_old_files();
        }
    }
}
