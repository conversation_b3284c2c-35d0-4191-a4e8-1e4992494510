<?php
/**
 * CSV Importer Class
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * CSV importer class for ordinace data
 */
class SPD10_Ordinace_CSV_Importer {
    
    /**
     * Maximum file size in bytes (10MB)
     */
    const MAX_FILE_SIZE = 10485760;
    
    /**
     * Allowed MIME types for CSV files
     */
    const ALLOWED_MIME_TYPES = array(
        'text/csv',
        'text/plain',
        'application/csv',
        'application/excel',
        'application/vnd.ms-excel',
        'application/vnd.msexcel',
    );
    
    /**
     * Required columns for CSV import
     */
    private $required_columns = array(
        'kategorie_ordinace',
        'jmena_lekaru',
        'adresa',
    );
    
    /**
     * Column mapping for CSV to internal fields
     */
    private $column_mapping = array(
        'kategorie_ordinace' => 'type',
        'jmena_lekaru' => 'doctors_names',
        'organizace' => 'organization',
        'ctvrt' => 'quarter',
        'adresa' => 'address',
        'email' => 'email',
        'telefon' => 'phone',
        'web' => 'website',
        'poznamky' => 'notes',
        'source_row_id' => 'source_row_id',
        'updated_at' => 'updated_at',
    );
    
    /**
     * Valid values for kategorie_ordinace column
     */
    private $valid_categories = array('pediatr', 'praktik-dospeli');
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_ajax_spd10_ordinace_upload_csv', array($this, 'handle_csv_upload'));
        add_action('wp_ajax_spd10_ordinace_import_csv', array($this, 'handle_csv_import'));
        add_action('wp_ajax_spd10_ordinace_validate_csv', array($this, 'handle_csv_validation'));
        add_action('wp_ajax_spd10_ordinace_process_paste_data', array($this, 'handle_paste_data_processing'));
        add_action('wp_ajax_spd10_ordinace_import_paste_data', array($this, 'handle_paste_data_import'));
    }
    
    /**
     * Handle CSV file upload
     */
    public function handle_csv_upload() {
        // Verify nonce and capabilities
        if (!wp_verify_nonce($_POST['nonce'], 'spd10_ordinace_csv_upload') || 
            !current_user_can('manage_options')) {
            wp_send_json_error(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Check if file was uploaded
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(__('Chyba při nahrávání souboru.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $file = $_FILES['csv_file'];
        
        // Validate file size
        if ($file['size'] > self::MAX_FILE_SIZE) {
            wp_send_json_error(sprintf(
                __('Soubor je příliš velký. Maximální velikost je %s.', SPD10_ORDINACE_TEXT_DOMAIN),
                size_format(self::MAX_FILE_SIZE)
            ));
        }
        
        // Validate MIME type
        $file_type = wp_check_filetype($file['name']);
        if (!in_array($file['type'], self::ALLOWED_MIME_TYPES) && $file_type['ext'] !== 'csv') {
            wp_send_json_error(__('Neplatný typ souboru. Povoleny jsou pouze CSV soubory.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Move uploaded file to uploads directory
        $upload_dir = wp_upload_dir();
        $csv_dir = $upload_dir['basedir'] . '/spd10-ordinace-csv/';
        
        if (!file_exists($csv_dir)) {
            wp_mkdir_p($csv_dir);
        }
        
        $filename = 'import_' . date('Y-m-d_H-i-s') . '_' . sanitize_file_name($file['name']);
        $file_path = $csv_dir . $filename;
        
        if (!move_uploaded_file($file['tmp_name'], $file_path)) {
            wp_send_json_error(__('Nepodařilo se uložit nahraný soubor.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Store file path in transient for later use
        $file_key = 'csv_import_' . wp_generate_uuid4();
        set_transient($file_key, $file_path, HOUR_IN_SECONDS);
        
        wp_send_json_success(array(
            'file_key' => $file_key,
            'filename' => $filename,
            'size' => size_format($file['size']),
            'message' => __('Soubor byl úspěšně nahrán.', SPD10_ORDINACE_TEXT_DOMAIN),
        ));
    }
    
    /**
     * Handle CSV validation
     */
    public function handle_csv_validation() {
        // Verify nonce and capabilities
        if (!wp_verify_nonce($_POST['nonce'], 'spd10_ordinace_csv_import') || 
            !current_user_can('manage_options')) {
            wp_send_json_error(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $file_key = sanitize_text_field($_POST['file_key']);
        $file_path = get_transient($file_key);
        
        if (!$file_path || !file_exists($file_path)) {
            wp_send_json_error(__('Soubor nebyl nalezen. Nahrajte soubor znovu.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Parse and validate CSV
        $validation_result = $this->validate_csv_file($file_path);
        
        if (is_wp_error($validation_result)) {
            wp_send_json_error($validation_result->get_error_message());
        }
        
        wp_send_json_success($validation_result);
    }
    
    /**
     * Handle CSV import
     */
    public function handle_csv_import() {
        // Verify nonce and capabilities
        if (!wp_verify_nonce($_POST['nonce'], 'spd10_ordinace_csv_import') || 
            !current_user_can('manage_options')) {
            wp_send_json_error(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $file_key = sanitize_text_field($_POST['file_key']);
        $dry_run = isset($_POST['dry_run']) && $_POST['dry_run'] === 'true';
        $column_mapping = isset($_POST['column_mapping']) ? $_POST['column_mapping'] : array();
        
        $file_path = get_transient($file_key);
        
        if (!$file_path || !file_exists($file_path)) {
            wp_send_json_error(__('Soubor nebyl nalezen. Nahrajte soubor znovu.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Parse CSV and import data
        $import_result = $this->import_csv_data($file_path, $dry_run, $column_mapping);
        
        if (is_wp_error($import_result)) {
            wp_send_json_error($import_result->get_error_message());
        }
        
        // Clean up file if not dry run
        if (!$dry_run) {
            unlink($file_path);
            delete_transient($file_key);
        }
        
        wp_send_json_success($import_result);
    }
    
    /**
     * Validate CSV file structure and content
     */
    private function validate_csv_file($file_path) {
        // Detect encoding and delimiter
        $file_content = file_get_contents($file_path);
        $encoding = $this->detect_encoding($file_content);
        $delimiter = $this->detect_delimiter($file_content);
        
        // Convert encoding if needed
        if ($encoding !== 'UTF-8') {
            $file_content = mb_convert_encoding($file_content, 'UTF-8', $encoding);
            file_put_contents($file_path, $file_content);
        }
        
        // Parse CSV
        $csv_data = $this->parse_csv($file_path, $delimiter);
        
        if (empty($csv_data)) {
            return new WP_Error('empty_csv', __('CSV soubor je prázdný nebo neplatný.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $headers = array_shift($csv_data);
        $headers = array_map('trim', $headers);
        
        // Validate required columns
        $missing_columns = array();
        foreach ($this->required_columns as $required_col) {
            if (!in_array($required_col, $headers)) {
                $missing_columns[] = $required_col;
            }
        }
        
        if (!empty($missing_columns)) {
            return new WP_Error('missing_columns', sprintf(
                __('Chybí povinné sloupce: %s', SPD10_ORDINACE_TEXT_DOMAIN),
                implode(', ', $missing_columns)
            ));
        }
        
        // Validate data rows
        $validation_errors = array();
        $valid_rows = 0;
        
        foreach ($csv_data as $row_index => $row) {
            $row_data = array_combine($headers, $row);
            $row_errors = $this->validate_csv_row($row_data, $row_index + 2); // +2 for header and 0-based index
            
            if (!empty($row_errors)) {
                $validation_errors = array_merge($validation_errors, $row_errors);
            } else {
                $valid_rows++;
            }
        }
        
        return array(
            'headers' => $headers,
            'total_rows' => count($csv_data),
            'valid_rows' => $valid_rows,
            'errors' => $validation_errors,
            'encoding' => $encoding,
            'delimiter' => $delimiter,
            'column_mapping' => $this->suggest_column_mapping($headers),
        );
    }
    
    /**
     * Validate individual CSV row
     */
    private function validate_csv_row($row_data, $row_number) {
        $errors = array();
        
        // Validate kategorie_ordinace
        if (empty($row_data['kategorie_ordinace'])) {
            $errors[] = sprintf(__('Řádek %d: Chybí kategorie ordinace.', SPD10_ORDINACE_TEXT_DOMAIN), $row_number);
        } elseif (!in_array($row_data['kategorie_ordinace'], $this->valid_categories)) {
            $errors[] = sprintf(
                __('Řádek %d: Neplatná kategorie ordinace "%s". Povolené hodnoty: %s', SPD10_ORDINACE_TEXT_DOMAIN),
                $row_number,
                $row_data['kategorie_ordinace'],
                implode(', ', $this->valid_categories)
            );
        }
        
        // Validate required fields
        if (empty($row_data['jmena_lekaru']) && empty($row_data['organizace'])) {
            $errors[] = sprintf(__('Řádek %d: Musí být vyplněno alespoň jméno lékaře nebo organizace.', SPD10_ORDINACE_TEXT_DOMAIN), $row_number);
        }
        
        if (empty($row_data['adresa'])) {
            $errors[] = sprintf(__('Řádek %d: Chybí adresa.', SPD10_ORDINACE_TEXT_DOMAIN), $row_number);
        }
        
        // Validate email format
        if (!empty($row_data['email']) && !is_email($row_data['email'])) {
            $errors[] = sprintf(__('Řádek %d: Neplatný formát e-mailu.', SPD10_ORDINACE_TEXT_DOMAIN), $row_number);
        }
        
        // Validate website URL
        if (!empty($row_data['web']) && !filter_var($row_data['web'], FILTER_VALIDATE_URL)) {
            $errors[] = sprintf(__('Řádek %d: Neplatný formát webové stránky.', SPD10_ORDINACE_TEXT_DOMAIN), $row_number);
        }
        
        return $errors;
    }
    
    /**
     * Detect file encoding
     */
    private function detect_encoding($content) {
        $encodings = array('UTF-8', 'Windows-1250', 'ISO-8859-2');
        
        foreach ($encodings as $encoding) {
            if (mb_check_encoding($content, $encoding)) {
                return $encoding;
            }
        }
        
        return 'UTF-8'; // Default fallback
    }
    
    /**
     * Detect CSV delimiter
     */
    private function detect_delimiter($content) {
        $delimiters = array(',', ';', "\t");
        $line = strtok($content, "\n");
        
        $delimiter_counts = array();
        foreach ($delimiters as $delimiter) {
            $delimiter_counts[$delimiter] = substr_count($line, $delimiter);
        }
        
        return array_search(max($delimiter_counts), $delimiter_counts);
    }
    
    /**
     * Parse CSV file
     */
    private function parse_csv($file_path, $delimiter = ',') {
        $data = array();
        
        if (($handle = fopen($file_path, 'r')) !== FALSE) {
            while (($row = fgetcsv($handle, 0, $delimiter)) !== FALSE) {
                $data[] = $row;
            }
            fclose($handle);
        }
        
        return $data;
    }
    
    /**
     * Suggest column mapping based on headers
     */
    private function suggest_column_mapping($headers) {
        $mapping = array();
        
        foreach ($headers as $header) {
            $header_lower = strtolower(trim($header));
            
            // Direct mapping
            if (isset($this->column_mapping[$header_lower])) {
                $mapping[$header] = $this->column_mapping[$header_lower];
                continue;
            }
            
            // Fuzzy matching
            foreach ($this->column_mapping as $csv_col => $internal_field) {
                if (strpos($header_lower, $csv_col) !== false || 
                    strpos($csv_col, $header_lower) !== false) {
                    $mapping[$header] = $internal_field;
                    break;
                }
            }
        }
        
        return $mapping;
    }

    /**
     * Import CSV data
     */
    private function import_csv_data($file_path, $dry_run = true, $column_mapping = array()) {
        // Parse CSV
        $delimiter = $this->detect_delimiter(file_get_contents($file_path));
        $csv_data = $this->parse_csv($file_path, $delimiter);

        if (empty($csv_data)) {
            return new WP_Error('empty_csv', __('CSV soubor je prázdný.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        $headers = array_shift($csv_data);
        $headers = array_map('trim', $headers);

        // Use provided mapping or auto-suggest
        if (empty($column_mapping)) {
            $column_mapping = $this->suggest_column_mapping($headers);
        }

        // Initialize counters
        $stats = array(
            'total_rows' => count($csv_data),
            'processed' => 0,
            'created' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => array(),
        );

        // Load existing importer for reuse
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-importer.php';
        $importer = new SPD10_Ordinace_Importer();

        foreach ($csv_data as $row_index => $row) {
            $row_number = $row_index + 2; // +2 for header and 0-based index
            $row_data = array_combine($headers, $row);

            // Map CSV data to internal format
            $mapped_data = $this->map_csv_row_data($row_data, $column_mapping);

            if (is_wp_error($mapped_data)) {
                $stats['errors'][] = sprintf(__('Řádek %d: %s', SPD10_ORDINACE_TEXT_DOMAIN), $row_number, $mapped_data->get_error_message());
                continue;
            }

            // Set source information
            $mapped_data['source_type'] = 'csv';

            // Generate stable source_row_id based on content, not row position
            if (empty($mapped_data['source_row_id'])) {
                // Create unique ID based on key data fields to ensure consistent deduplication
                $unique_data = array(
                    'address' => $mapped_data['address'] ?? '',
                    'organization' => $mapped_data['organization'] ?? '',
                    'doctors_names' => $mapped_data['doctors_names'] ?? '',
                    'ordinace_typ' => $mapped_data['ordinace_typ'] ?? '',
                );
                $mapped_data['source_row_id'] = 'csv_' . md5(serialize($unique_data));
            }

            try {
                if ($dry_run) {
                    // Dry run - just validate and count
                    $existing_post = $importer->find_existing_ordinace($mapped_data);
                    if ($existing_post) {
                        $stats['updated']++;
                    } else {
                        $stats['created']++;
                    }
                } else {
                    // Actual import
                    $result = $importer->import_ordinace_data($mapped_data);

                    if (is_wp_error($result)) {
                        $stats['errors'][] = sprintf(__('Řádek %d: %s', SPD10_ORDINACE_TEXT_DOMAIN), $row_number, $result->get_error_message());
                    } else {
                        if ($result['action'] === 'created') {
                            $stats['created']++;
                        } elseif ($result['action'] === 'updated') {
                            $stats['updated']++;
                        } else {
                            $stats['skipped']++;
                        }
                    }
                }

                $stats['processed']++;

            } catch (Exception $e) {
                $stats['errors'][] = sprintf(__('Řádek %d: %s', SPD10_ORDINACE_TEXT_DOMAIN), $row_number, $e->getMessage());
            }
        }

        // Generate import log
        $log_data = array(
            'type' => 'csv_import',
            'dry_run' => $dry_run,
            'timestamp' => current_time('mysql'),
            'file_name' => basename($file_path),
            'stats' => $stats,
            'column_mapping' => $column_mapping,
        );

        // Save log
        $this->save_import_log($log_data);

        return array(
            'success' => true,
            'dry_run' => $dry_run,
            'stats' => $stats,
            'message' => $dry_run
                ? __('Náhled importu dokončen.', SPD10_ORDINACE_TEXT_DOMAIN)
                : __('Import dokončen.', SPD10_ORDINACE_TEXT_DOMAIN),
        );
    }

    /**
     * Map CSV row data to internal format
     */
    private function map_csv_row_data($row_data, $column_mapping) {
        $mapped_data = array();

        foreach ($column_mapping as $csv_column => $internal_field) {
            if (isset($row_data[$csv_column])) {
                $value = trim($row_data[$csv_column]);

                // Special handling for specific fields
                switch ($internal_field) {
                    case 'type':
                        // Map kategorie_ordinace to taxonomy term
                        $mapped_data['ordinace_typ'] = $value;
                        break;

                    case 'doctors_names':
                        // Split multiple names by newline or comma
                        $names = preg_split('/[\r\n,]+/', $value);
                        $mapped_data['doctors_names'] = implode("\n", array_filter(array_map('trim', $names)));
                        break;

                    case 'website':
                        // Ensure URL has protocol
                        if (!empty($value) && !preg_match('/^https?:\/\//', $value)) {
                            $value = 'http://' . $value;
                        }
                        $mapped_data[$internal_field] = $value;
                        break;

                    default:
                        $mapped_data[$internal_field] = $value;
                        break;
                }
            }
        }

        // Validate required fields
        if (empty($mapped_data['ordinace_typ'])) {
            return new WP_Error('missing_type', __('Chybí kategorie ordinace.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        if (!in_array($mapped_data['ordinace_typ'], $this->valid_categories)) {
            return new WP_Error('invalid_type', sprintf(
                __('Neplatná kategorie ordinace: %s', SPD10_ORDINACE_TEXT_DOMAIN),
                $mapped_data['ordinace_typ']
            ));
        }

        if (empty($mapped_data['doctors_names']) && empty($mapped_data['organization'])) {
            return new WP_Error('missing_name_org', __('Musí být vyplněno alespoň jméno lékaře nebo organizace.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        if (empty($mapped_data['address'])) {
            return new WP_Error('missing_address', __('Chybí adresa.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        return $mapped_data;
    }

    /**
     * Save import log
     */
    private function save_import_log($log_data) {
        $logs_dir = wp_upload_dir()['basedir'] . '/spd10-ordinace-logs/';

        if (!file_exists($logs_dir)) {
            wp_mkdir_p($logs_dir);
        }

        $log_file = $logs_dir . 'csv_import_' . date('Y-m-d_H-i-s') . '.json';
        file_put_contents($log_file, wp_json_encode($log_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        // Also save to database option for admin display
        $recent_logs = get_option('spd10_ordinace_recent_import_logs', array());
        array_unshift($recent_logs, $log_data);

        // Keep only last 10 logs
        $recent_logs = array_slice($recent_logs, 0, 10);
        update_option('spd10_ordinace_recent_import_logs', $recent_logs);
    }

    /**
     * Get recent import logs
     */
    public static function get_recent_logs() {
        return get_option('spd10_ordinace_recent_import_logs', array());
    }

    /**
     * Clean up old CSV files
     */
    public static function cleanup_old_files() {
        $csv_dir = wp_upload_dir()['basedir'] . '/spd10-ordinace-csv/';

        if (!file_exists($csv_dir)) {
            return;
        }

        $files = glob($csv_dir . 'import_*.csv');
        $cutoff_time = time() - (24 * HOUR_IN_SECONDS); // 24 hours ago

        foreach ($files as $file) {
            if (filemtime($file) < $cutoff_time) {
                unlink($file);
            }
        }
    }

    /**
     * Handle paste data processing
     */
    public function handle_paste_data_processing() {
        // Verify nonce and capabilities
        if (!wp_verify_nonce($_POST['nonce'], 'spd10_ordinace_paste_import') ||
            !current_user_can('manage_options')) {
            wp_send_json_error(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        $paste_data = sanitize_textarea_field($_POST['paste_data']);

        if (empty($paste_data)) {
            wp_send_json_error(__('Žádná data nebyla vložena.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        // Process and validate paste data
        $validation_result = $this->process_paste_data($paste_data);

        if (is_wp_error($validation_result)) {
            wp_send_json_error($validation_result->get_error_message());
        }

        wp_send_json_success($validation_result);
    }

    /**
     * Handle paste data import
     */
    public function handle_paste_data_import() {
        try {
            // Set execution time limit to prevent timeouts
            if (!ini_get('safe_mode')) {
                set_time_limit(300); // 5 minutes max
            }

            // Verify nonce and capabilities
            if (!wp_verify_nonce($_POST['nonce'], 'spd10_ordinace_paste_import') ||
                !current_user_can('manage_options')) {
                wp_send_json_error(__('Bezpečnostní kontrola selhala.', SPD10_ORDINACE_TEXT_DOMAIN));
            }

            // Get paste data - it should be the processed data from the previous step
            $paste_data_raw = isset($_POST['paste_data']) ? $_POST['paste_data'] : null;
            $dry_run = isset($_POST['dry_run']) && $_POST['dry_run'] === 'true';
            $column_mapping = isset($_POST['column_mapping']) ? $_POST['column_mapping'] : array();

            if (empty($paste_data_raw)) {
                wp_send_json_error(__('Žádná data k importu.', SPD10_ORDINACE_TEXT_DOMAIN));
            }

            // Deserialize JSON data from JavaScript
            $paste_data = json_decode(stripslashes($paste_data_raw), true);

            // Debug info for console
            $debug_info = [
                'paste_data_raw_type' => gettype($paste_data_raw),
                'paste_data_raw_length' => is_string($paste_data_raw) ? strlen($paste_data_raw) : 'not string',
                'paste_data_type' => gettype($paste_data),
                'paste_data_keys' => is_array($paste_data) ? array_keys($paste_data) : 'not array',
                'json_error' => json_last_error_msg(),
                'dry_run' => $dry_run,
                'column_mapping' => $column_mapping,
                'parsed_headers' => isset($paste_data['headers']) ? $paste_data['headers'] : 'no headers',
                'parsed_data_count' => isset($paste_data['data']) ? count($paste_data['data']) : 'no data',
                'first_data_row' => isset($paste_data['data'][0]) ? $paste_data['data'][0] : 'no first row'
            ];

            if (json_last_error() !== JSON_ERROR_NONE) {
                wp_send_json_error(sprintf(
                    __('Chyba při deserializaci dat: %s', SPD10_ORDINACE_TEXT_DOMAIN),
                    json_last_error_msg()
                ));
            }

            // The paste_data should already be processed data from JavaScript
            // No need to process it again, just validate it has the right structure
            if (!is_array($paste_data) || !isset($paste_data['data']) || !isset($paste_data['headers'])) {
                wp_send_json_error(__('Neplatná struktura dat pro import. Očekávané klíče: data, headers', SPD10_ORDINACE_TEXT_DOMAIN));
            }

            // Import paste data
            $import_result = $this->import_paste_data($paste_data, $dry_run, $column_mapping);

            if (is_wp_error($import_result)) {
                wp_send_json_error([
                    'message' => $import_result->get_error_message(),
                    'debug' => $debug_info
                ]);
            }

            // Add debug info to successful response
            if (is_array($import_result)) {
                $import_result['debug'] = $debug_info;
            }

            wp_send_json_success($import_result);

        } catch (Exception $e) {
            if (WP_DEBUG_LOG) {
                error_log('SPD10 Ordinace Paste Import Exception: ' . $e->getMessage());
            }
            wp_send_json_error(sprintf(
                __('Chyba při importu: %s', SPD10_ORDINACE_TEXT_DOMAIN),
                $e->getMessage()
            ));
        }
    }

    /**
     * Process paste data and detect format
     */
    private function process_paste_data($paste_data) {
        // Detect delimiter
        $delimiter = $this->detect_delimiter($paste_data);

        if (!$delimiter) {
            return new WP_Error('invalid_format', __('Nepodařilo se detekovat formát dat. Podporované oddělovače: tabulátor, čárka, středník.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        // Parse data into rows - custom parser for paste data with multiline support
        $data = array();

        // Normalize line endings
        $normalized_data = str_replace(["\r\n", "\r"], "\n", $paste_data);
        $lines = explode("\n", $normalized_data);

        // Custom CSV parser that handles quoted multiline values
        $current_row = array();
        $in_quotes = false;
        $current_cell = '';
        $quote_char = '"';

        foreach ($lines as $line_index => $line) {
            $line = rtrim($line); // Remove trailing whitespace but keep leading

            if (empty($line) && !$in_quotes) {
                continue; // Skip empty lines when not inside quotes
            }

            $i = 0;
            $line_length = strlen($line);

            while ($i < $line_length) {
                $char = $line[$i];

                if ($char === $quote_char) {
                    if ($in_quotes) {
                        // Check for escaped quote (double quote)
                        if ($i + 1 < $line_length && $line[$i + 1] === $quote_char) {
                            $current_cell .= $quote_char;
                            $i += 2;
                            continue;
                        } else {
                            // End of quoted value
                            $in_quotes = false;
                        }
                    } else {
                        // Start of quoted value
                        $in_quotes = true;
                    }
                } elseif ($char === $delimiter && !$in_quotes) {
                    // End of cell
                    $current_row[] = trim($current_cell);
                    $current_cell = '';
                } else {
                    $current_cell .= $char;
                }

                $i++;
            }

            // If we're in quotes, add newline to current cell and continue to next line
            if ($in_quotes) {
                $current_cell .= "\n";
            } else {
                // End of line, finish current cell and row
                $current_row[] = trim($current_cell);
                $current_cell = '';

                // Add completed row if it has content
                if (!empty(array_filter($current_row, function($cell) { return trim($cell) !== ''; }))) {
                    $data[] = $current_row;
                }
                $current_row = array();
            }
        }

        // Handle any remaining data
        if (!empty($current_cell) || !empty($current_row)) {
            $current_row[] = trim($current_cell);
            if (!empty(array_filter($current_row, function($cell) { return trim($cell) !== ''; }))) {
                $data[] = $current_row;
            }
        }

        if (empty($data)) {
            return new WP_Error('no_data', __('Žádná platná data nebyla nalezena.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        // First row should be headers
        $headers = array_shift($data);

        if (empty($headers)) {
            return new WP_Error('no_headers', __('Nebyla nalezena hlavička s názvy sloupců.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        // Validate required columns
        $validation_errors = array();
        foreach ($this->required_columns as $required_col) {
            if (!in_array($required_col, $headers)) {
                $validation_errors[] = sprintf(
                    __('Chybí povinný sloupec: %s', SPD10_ORDINACE_TEXT_DOMAIN),
                    $required_col
                );
            }
        }

        // Auto-detect column mapping
        $column_mapping = $this->auto_detect_column_mapping($headers);

        // Validate data rows
        $valid_rows = 0;
        foreach ($data as $row_index => $row) {
            if (count($row) !== count($headers)) {
                $validation_errors[] = sprintf(
                    __('Řádek %d má nesprávný počet sloupců (%d místo %d).', SPD10_ORDINACE_TEXT_DOMAIN),
                    $row_index + 2, // +2 because we start from 1 and removed header
                    count($row),
                    count($headers)
                );
                continue;
            }

            // Validate kategorie_ordinace if present
            $kategorie_index = array_search('kategorie_ordinace', $headers);
            if ($kategorie_index !== false && !empty($row[$kategorie_index])) {
                if (!in_array($row[$kategorie_index], $this->valid_categories)) {
                    $validation_errors[] = sprintf(
                        __('Řádek %d: Neplatná hodnota v sloupci kategorie_ordinace: %s. Povolené hodnoty: %s', SPD10_ORDINACE_TEXT_DOMAIN),
                        $row_index + 2,
                        $row[$kategorie_index],
                        implode(', ', $this->valid_categories)
                    );
                    continue;
                }
            }

            $valid_rows++;
        }

        // Determine detected format
        $format_names = array(
            "\t" => 'Tabulátor (Excel)',
            ',' => 'Čárka (CSV)',
            ';' => 'Středník (CSV)'
        );
        $detected_format = isset($format_names[$delimiter]) ? $format_names[$delimiter] : 'Neznámý';

        return array(
            'detected_format' => $detected_format,
            'delimiter' => $delimiter,
            'headers' => $headers,
            'data' => $data,
            'total_rows' => count($data),
            'valid_rows' => $valid_rows,
            'errors' => $validation_errors,
            'column_mapping' => $column_mapping,
            'debug_parsing' => [
                'original_lines_count' => count(explode("\n", trim($paste_data))),
                'parsed_rows_count' => count($data),
                'headers_count' => count($headers),
                'first_data_row_sample' => !empty($data) ? $data[0] : 'no data',
                'delimiter_used' => $delimiter === "\t" ? 'TAB' : $delimiter
            ]
        );
    }

    /**
     * Import data from paste
     */
    private function import_paste_data($paste_data_result, $dry_run = true, $column_mapping = array()) {
        if (!isset($paste_data_result['data']) || !isset($paste_data_result['headers'])) {
            return new WP_Error('invalid_data', __('Neplatná data pro import.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        // Check if importer class exists
        if (!class_exists('SPD10_Ordinace_Importer')) {
            return new WP_Error('importer_not_found', __('Importer třída nebyla nalezena.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        // Use existing importer with paste data
        $importer = new SPD10_Ordinace_Importer();

        // Convert paste data to format expected by importer
        $import_data = array();
        foreach ($paste_data_result['data'] as $row_index => $row) {
            $row_data = array();
            foreach ($paste_data_result['headers'] as $index => $header) {
                $row_data[$header] = isset($row[$index]) ? $row[$index] : '';
            }
            $import_data[] = $row_data;
        }

        // Debug: Check converted data
        $converted_debug = [
            'original_headers' => $paste_data_result['headers'],
            'original_data_count' => count($paste_data_result['data']),
            'converted_data_count' => count($import_data),
            'first_row_sample' => !empty($import_data) ? $import_data[0] : 'no data',
            'column_mapping' => $column_mapping,
            'dry_run' => $dry_run
        ];

        // Perform import - process each row individually
        $import_stats = [
            'processed' => 0,
            'created' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => [],
            'dry_run' => $dry_run
        ];

        try {
            $start_time = microtime(true);

            foreach ($import_data as $row_index => $row_data) {
                // Check for timeout every row
                if (microtime(true) - $start_time > 240) { // 4 minutes timeout
                    $import_stats['errors'][] = __('Import byl přerušen kvůli timeout. Zkuste importovat menší množství dat.', SPD10_ORDINACE_TEXT_DOMAIN);
                    break;
                }

                $import_stats['processed']++;

                // Debug: Log raw row data
                $debug_row = [
                    'row_index' => $row_index,
                    'raw_data' => $row_data,
                    'column_mapping' => $column_mapping
                ];

                // Apply column mapping first
                $mapped_data = [];
                foreach ($column_mapping as $csv_column => $internal_field) {
                    if (isset($row_data[$csv_column]) && !empty(trim($row_data[$csv_column]))) {
                        $mapped_data[$internal_field] = trim($row_data[$csv_column]);
                    }
                }

                // Add any unmapped data with automatic mapping
                foreach ($row_data as $key => $value) {
                    if (!isset($column_mapping[$key]) && !empty(trim($value))) {
                        // Try to map common fields
                        $mapped_key = $this->map_common_field($key);
                        if ($mapped_key && !isset($mapped_data[$mapped_key])) {
                            $mapped_data[$mapped_key] = trim($value);
                        }
                    }
                }

                // Ensure we have a unique source_row_id for deduplication
                if (!isset($mapped_data['source_row_id'])) {
                    // Create unique ID based on row index and timestamp to avoid collisions
                    $unique_data = [
                        'row_index' => $row_index,
                        'timestamp' => microtime(true),
                        'data_hash' => md5(serialize($row_data))
                    ];
                    $mapped_data['source_row_id'] = 'paste_import_' . md5(serialize($unique_data));
                }

                $debug_row['mapped_data'] = $mapped_data;

                if ($dry_run) {
                    // For dry run, use the same validation as the real importer
                    $validation = $this->validate_mapped_data_for_import($mapped_data);
                    if (is_wp_error($validation)) {
                        $import_stats['errors'][] = sprintf(
                            __('Řádek %d: %s (Debug: %s)', SPD10_ORDINACE_TEXT_DOMAIN),
                            $row_index + 1,
                            $validation->get_error_message(),
                            json_encode($debug_row)
                        );
                    } else {
                        $import_stats['created']++;
                    }
                } else {
                    // Actual import with error handling
                    try {
                        $result = $importer->import_ordinace_data($mapped_data);
                        if (is_wp_error($result)) {
                            $import_stats['errors'][] = sprintf(
                                __('Řádek %d: %s', SPD10_ORDINACE_TEXT_DOMAIN),
                                $row_index + 1,
                                $result->get_error_message()
                            );
                        } else {
                            if ($result['action'] === 'created') {
                                $import_stats['created']++;
                            } elseif ($result['action'] === 'updated') {
                                $import_stats['updated']++;
                            } else {
                                $import_stats['skipped']++;
                            }
                        }
                    } catch (Exception $e) {
                        $import_stats['errors'][] = sprintf(
                            __('Řádek %d: Chyba při importu - %s', SPD10_ORDINACE_TEXT_DOMAIN),
                            $row_index + 1,
                            $e->getMessage()
                        );
                    }
                }
            }

            // Add debug info to result
            $import_stats['conversion_debug'] = $converted_debug;

            return $import_stats;

        } catch (Exception $e) {
            return new WP_Error('import_failed', sprintf(
                __('Import selhal: %s', SPD10_ORDINACE_TEXT_DOMAIN),
                $e->getMessage()
            ));
        }
    }



    /**
     * Auto-detect column mapping for paste data
     */
    private function auto_detect_column_mapping($headers) {
        $mapping = array();

        foreach ($headers as $header) {
            $header_lower = strtolower(trim($header));

            // Direct mapping
            if (isset($this->column_mapping[$header])) {
                $mapping[$header] = $this->column_mapping[$header];
                continue;
            }

            // Fuzzy matching
            foreach ($this->column_mapping as $csv_col => $internal_field) {
                if (strpos($header_lower, strtolower($csv_col)) !== false) {
                    $mapping[$header] = $internal_field;
                    break;
                }
            }
        }

        return $mapping;
    }

    /**
     * Map common field names to internal fields
     */
    private function map_common_field($field_name) {
        $common_mappings = [
            'kategorie_ordinace' => 'type',
            'jmena_lekaru' => 'doctors_names',
            'organizace' => 'organization',
            'ctvrt' => 'quarter',
            'adresa' => 'address',
            'telefon' => 'phone',
            'email' => 'email',
            'web' => 'website'
        ];

        $field_lower = strtolower(trim($field_name));
        return isset($common_mappings[$field_lower]) ? $common_mappings[$field_lower] : null;
    }

    /**
     * Validate mapped data for import (same logic as main importer)
     */
    private function validate_mapped_data_for_import($mapped_data) {
        // Check required fields - same as in class-importer.php
        if (empty($mapped_data['address'])) {
            return new WP_Error('missing_address', __('Adresa je povinná.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        // At least one of organization or doctors_names must be present
        if (empty($mapped_data['organization']) && empty($mapped_data['doctors_names'])) {
            return new WP_Error('missing_name', __('Musí být zadána organizace nebo jméno lékaře.', SPD10_ORDINACE_TEXT_DOMAIN));
        }

        // Validate type if present
        if (!empty($mapped_data['type']) && !in_array($mapped_data['type'], ['praktik-dospeli', 'pediatr'])) {
            return new WP_Error('invalid_type', sprintf(
                __('Neplatný typ ordinace: %s. Povolené hodnoty: praktik-dospeli, pediatr', SPD10_ORDINACE_TEXT_DOMAIN),
                $mapped_data['type']
            ));
        }

        return true;
    }

    /**
     * Legacy validation method (kept for compatibility)
     */
    private function validate_ordinace_data($data) {
        return $this->validate_mapped_data_for_import($data);
    }
}
