<?php
/**
 * Main Geocoder Class
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main geocoder class with provider management
 */
class SPD10_Ordinace_Geocoder {
    
    /**
     * Cache expiration time in seconds (7 days)
     */
    const CACHE_EXPIRATION = 604800;
    
    /**
     * Rate limiting - max requests per hour
     */
    const RATE_LIMIT_HOURLY = 100;
    
    /**
     * Current geocoding provider
     */
    private $provider;
    
    /**
     * Available providers
     */
    private $providers = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->load_providers();
        $this->set_active_provider();
    }
    
    /**
     * Load available geocoding providers
     */
    private function load_providers() {
        // Load provider classes
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/geocoders/class-nominatim-geocoder.php';
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/geocoders/class-google-geocoder.php';
        require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/geocoders/class-mapbox-geocoder.php';
        
        // Register providers
        $this->providers = array(
            'nominatim' => new SPD10_Ordinace_Nominatim_Geocoder(),
            'google' => new SPD10_Ordinace_Google_Geocoder(),
            'mapbox' => new SPD10_Ordinace_Mapbox_Geocoder(),
        );
    }
    
    /**
     * Set active provider based on configuration
     */
    private function set_active_provider() {
        $provider_name = spd10_ordinace_get_option('geocoder_provider', 'nominatim');
        
        if (isset($this->providers[$provider_name]) && $this->providers[$provider_name]->is_available()) {
            $this->provider = $this->providers[$provider_name];
        } else {
            // Fallback to first available provider
            foreach ($this->providers as $provider) {
                if ($provider->is_available()) {
                    $this->provider = $provider;
                    break;
                }
            }
        }
        
        if (!$this->provider) {
            error_log('SPD10 Ordinace: No geocoding provider available');
        }
    }
    
    /**
     * Geocode an address
     * 
     * @param string $address Address to geocode
     * @param bool $use_cache Whether to use cache
     * @return array|WP_Error Geocoding result or error
     */
    public function geocode($address, $use_cache = true) {
        if (empty($address)) {
            return new WP_Error('empty_address', __('Adresa je prázdná.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        if (!$this->provider) {
            return new WP_Error('no_provider', __('Žádný geocoding provider není dostupný.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Normalize address for caching
        $normalized_address = $this->normalize_address($address);
        $cache_key = 'spd10_ordinace_geocode_' . md5($normalized_address);
        
        // Try cache first
        if ($use_cache) {
            $cached_result = get_transient($cache_key);
            if (false !== $cached_result) {
                return $cached_result;
            }
        }
        
        // Check rate limiting
        if (!$this->check_rate_limit()) {
            return new WP_Error('rate_limit_exceeded', 
                __('Překročen limit geocoding požadavků. Zkuste to později.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Perform geocoding with retry logic
        $result = $this->geocode_with_retry($normalized_address);
        
        // Cache successful results
        if (!is_wp_error($result) && $use_cache) {
            set_transient($cache_key, $result, self::CACHE_EXPIRATION);
        }
        
        // Update rate limiting counter
        $this->update_rate_limit_counter();
        
        return $result;
    }
    
    /**
     * Geocode with retry logic
     * 
     * @param string $address Normalized address
     * @return array|WP_Error Result or error
     */
    private function geocode_with_retry($address) {
        $max_retries = 3;
        $retry_delay = 1; // seconds
        
        for ($attempt = 1; $attempt <= $max_retries; $attempt++) {
            $result = $this->provider->geocode($address);
            
            if (!is_wp_error($result)) {
                return $result;
            }
            
            // Don't retry on certain errors
            $error_code = $result->get_error_code();
            if (in_array($error_code, array('invalid_address', 'no_results', 'quota_exceeded'))) {
                break;
            }
            
            // Wait before retry (exponential backoff)
            if ($attempt < $max_retries) {
                sleep($retry_delay * $attempt);
            }
        }
        
        return $result;
    }
    
    /**
     * Normalize address for consistent caching
     * 
     * @param string $address Raw address
     * @return string Normalized address
     */
    private function normalize_address($address) {
        // Convert to lowercase
        $address = strtolower($address);
        
        // Remove extra whitespace
        $address = preg_replace('/\s+/', ' ', trim($address));
        
        // Add Prague 10 if not present
        if (strpos($address, 'praha') === false && strpos($address, 'prague') === false) {
            $address .= ', Praha 10, Česká republika';
        } elseif (strpos($address, 'česká republika') === false && strpos($address, 'czech republic') === false) {
            $address .= ', Česká republika';
        }
        
        return $address;
    }
    
    /**
     * Check rate limiting
     * 
     * @return bool True if within limits
     */
    private function check_rate_limit() {
        $counter_key = 'spd10_ordinace_geocode_counter_' . date('YmdH');
        $current_count = get_transient($counter_key);
        
        return ($current_count === false || $current_count < self::RATE_LIMIT_HOURLY);
    }
    
    /**
     * Update rate limiting counter
     */
    private function update_rate_limit_counter() {
        $counter_key = 'spd10_ordinace_geocode_counter_' . date('YmdH');
        $current_count = get_transient($counter_key);
        
        if ($current_count === false) {
            set_transient($counter_key, 1, 3600); // 1 hour
        } else {
            set_transient($counter_key, $current_count + 1, 3600);
        }
    }
    
    /**
     * Bulk geocode multiple addresses
     * 
     * @param array $addresses Array of addresses
     * @param callable $progress_callback Optional progress callback
     * @return array Results array
     */
    public function bulk_geocode($addresses, $progress_callback = null) {
        $results = array();
        $total = count($addresses);
        
        foreach ($addresses as $index => $address) {
            $result = $this->geocode($address);
            $results[] = array(
                'address' => $address,
                'result' => $result,
                'success' => !is_wp_error($result),
            );
            
            // Call progress callback if provided
            if ($progress_callback && is_callable($progress_callback)) {
                call_user_func($progress_callback, $index + 1, $total, $result);
            }
            
            // Small delay to be respectful to APIs
            usleep(100000); // 0.1 second
        }
        
        return $results;
    }
    
    /**
     * Get available providers
     * 
     * @return array Available providers
     */
    public function get_available_providers() {
        $available = array();
        
        foreach ($this->providers as $name => $provider) {
            $available[$name] = array(
                'name' => $provider->get_provider_name(),
                'available' => $provider->is_available(),
                'config_requirements' => $provider->get_config_requirements(),
            );
        }
        
        return $available;
    }
    
    /**
     * Get current provider
     * 
     * @return SPD10_Ordinace_Geocoder_Interface|null Current provider
     */
    public function get_current_provider() {
        return $this->provider;
    }
    
    /**
     * Clear geocoding cache
     * 
     * @param string $address Optional specific address to clear
     */
    public function clear_cache($address = null) {
        if ($address) {
            $normalized_address = $this->normalize_address($address);
            $cache_key = 'spd10_ordinace_geocode_' . md5($normalized_address);
            delete_transient($cache_key);
        } else {
            // Clear all geocoding cache (this is a bit brute force)
            global $wpdb;
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_spd10_ordinace_geocode_%'
            ));
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_timeout_spd10_ordinace_geocode_%'
            ));
        }
    }
    
    /**
     * Get geocoding statistics
     * 
     * @return array Statistics
     */
    public function get_statistics() {
        $counter_key = 'spd10_ordinace_geocode_counter_' . date('YmdH');
        $current_hour_count = get_transient($counter_key);
        
        return array(
            'current_provider' => $this->provider ? $this->provider->get_provider_name() : 'None',
            'requests_this_hour' => $current_hour_count ?: 0,
            'hourly_limit' => self::RATE_LIMIT_HOURLY,
            'cache_expiration_days' => self::CACHE_EXPIRATION / 86400,
        );
    }
}
