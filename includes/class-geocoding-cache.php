<?php
/**
 * Geocoding Cache Manager
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for managing geocoding cache and statistics
 */
class SPD10_Ordinace_Geocoding_Cache {
    
    /**
     * Cache table name
     */
    private $table_name;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'spd10_ordinace_geocoding_cache';
    }
    
    /**
     * Create cache table
     */
    public function create_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE {$this->table_name} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            address_hash varchar(32) NOT NULL,
            original_address text NOT NULL,
            normalized_address text NOT NULL,
            lat decimal(10,8) DEFAULT NULL,
            lng decimal(11,8) DEFAULT NULL,
            formatted_address text DEFAULT NULL,
            accuracy varchar(20) DEFAULT NULL,
            provider varchar(50) DEFAULT NULL,
            status varchar(20) NOT NULL DEFAULT 'success',
            error_message text DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            hit_count int(11) NOT NULL DEFAULT 1,
            PRIMARY KEY (id),
            UNIQUE KEY address_hash (address_hash),
            KEY status (status),
            KEY provider (provider),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Get cached result
     * 
     * @param string $address Normalized address
     * @return array|null Cached result or null
     */
    public function get($address) {
        global $wpdb;
        
        $hash = md5($address);
        
        $result = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE address_hash = %s",
            $hash
        ), ARRAY_A);
        
        if (!$result) {
            return null;
        }
        
        // Update hit count
        $wpdb->update(
            $this->table_name,
            array('hit_count' => $result['hit_count'] + 1),
            array('id' => $result['id']),
            array('%d'),
            array('%d')
        );
        
        // Return formatted result
        if ($result['status'] === 'success') {
            return array(
                'lat' => floatval($result['lat']),
                'lng' => floatval($result['lng']),
                'formatted_address' => $result['formatted_address'],
                'accuracy' => $result['accuracy'],
                'provider' => $result['provider'],
                'cached' => true,
                'cache_date' => $result['created_at'],
            );
        } else {
            return new WP_Error('cached_error', $result['error_message']);
        }
    }
    
    /**
     * Store result in cache
     * 
     * @param string $original_address Original address
     * @param string $normalized_address Normalized address
     * @param array|WP_Error $result Geocoding result or error
     */
    public function set($original_address, $normalized_address, $result) {
        global $wpdb;
        
        $hash = md5($normalized_address);
        
        if (is_wp_error($result)) {
            $data = array(
                'address_hash' => $hash,
                'original_address' => $original_address,
                'normalized_address' => $normalized_address,
                'status' => 'error',
                'error_message' => $result->get_error_message(),
            );
        } else {
            $data = array(
                'address_hash' => $hash,
                'original_address' => $original_address,
                'normalized_address' => $normalized_address,
                'lat' => $result['lat'],
                'lng' => $result['lng'],
                'formatted_address' => $result['formatted_address'],
                'accuracy' => $result['accuracy'],
                'provider' => $result['provider'],
                'status' => 'success',
            );
        }
        
        // Use INSERT ... ON DUPLICATE KEY UPDATE
        $wpdb->replace($this->table_name, $data);
    }
    
    /**
     * Clear cache
     * 
     * @param array $options Clear options
     */
    public function clear($options = array()) {
        global $wpdb;
        
        $defaults = array(
            'older_than_days' => null,
            'provider' => null,
            'status' => null,
            'accuracy' => null,
        );
        
        $options = wp_parse_args($options, $defaults);
        
        $where_conditions = array();
        $where_values = array();
        
        if ($options['older_than_days']) {
            $where_conditions[] = 'created_at < DATE_SUB(NOW(), INTERVAL %d DAY)';
            $where_values[] = intval($options['older_than_days']);
        }
        
        if ($options['provider']) {
            $where_conditions[] = 'provider = %s';
            $where_values[] = $options['provider'];
        }
        
        if ($options['status']) {
            $where_conditions[] = 'status = %s';
            $where_values[] = $options['status'];
        }
        
        if ($options['accuracy']) {
            $where_conditions[] = 'accuracy = %s';
            $where_values[] = $options['accuracy'];
        }
        
        if (empty($where_conditions)) {
            // Clear all cache
            $wpdb->query("TRUNCATE TABLE {$this->table_name}");
        } else {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            $sql = "DELETE FROM {$this->table_name} {$where_clause}";
            
            if (!empty($where_values)) {
                $sql = $wpdb->prepare($sql, $where_values);
            }
            
            $wpdb->query($sql);
        }
    }
    
    /**
     * Get cache statistics
     * 
     * @return array Cache statistics
     */
    public function get_statistics() {
        global $wpdb;
        
        $stats = array();
        
        // Total entries
        $stats['total_entries'] = $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");
        
        // Success vs error ratio
        $success_count = $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name} WHERE status = 'success'");
        $error_count = $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name} WHERE status = 'error'");
        
        $stats['success_count'] = intval($success_count);
        $stats['error_count'] = intval($error_count);
        $stats['success_rate'] = $stats['total_entries'] > 0 ? 
            round(($stats['success_count'] / $stats['total_entries']) * 100, 2) : 0;
        
        // Provider breakdown
        $provider_stats = $wpdb->get_results(
            "SELECT provider, COUNT(*) as count FROM {$this->table_name} WHERE status = 'success' GROUP BY provider",
            ARRAY_A
        );
        
        $stats['providers'] = array();
        foreach ($provider_stats as $provider_stat) {
            $stats['providers'][$provider_stat['provider']] = intval($provider_stat['count']);
        }
        
        // Accuracy breakdown
        $accuracy_stats = $wpdb->get_results(
            "SELECT accuracy, COUNT(*) as count FROM {$this->table_name} WHERE status = 'success' GROUP BY accuracy",
            ARRAY_A
        );
        
        $stats['accuracy'] = array();
        foreach ($accuracy_stats as $accuracy_stat) {
            $stats['accuracy'][$accuracy_stat['accuracy']] = intval($accuracy_stat['count']);
        }
        
        // Cache age
        $oldest_entry = $wpdb->get_var("SELECT MIN(created_at) FROM {$this->table_name}");
        $newest_entry = $wpdb->get_var("SELECT MAX(created_at) FROM {$this->table_name}");
        
        $stats['oldest_entry'] = $oldest_entry;
        $stats['newest_entry'] = $newest_entry;
        
        // Most hit addresses
        $top_addresses = $wpdb->get_results(
            "SELECT original_address, hit_count, provider, accuracy FROM {$this->table_name} 
             WHERE status = 'success' ORDER BY hit_count DESC LIMIT 10",
            ARRAY_A
        );
        
        $stats['top_addresses'] = $top_addresses;
        
        // Recent errors
        $recent_errors = $wpdb->get_results(
            "SELECT original_address, error_message, created_at FROM {$this->table_name} 
             WHERE status = 'error' ORDER BY created_at DESC LIMIT 10",
            ARRAY_A
        );
        
        $stats['recent_errors'] = $recent_errors;
        
        return $stats;
    }
    
    /**
     * Get addresses that need geocoding
     * 
     * @param int $limit Number of addresses to return
     * @return array Addresses that need geocoding
     */
    public function get_pending_addresses($limit = 50) {
        global $wpdb;
        
        $ordinace_table = $wpdb->prefix . 'posts';
        $meta_table = $wpdb->prefix . 'postmeta';
        
        $sql = "
            SELECT p.ID, pm_address.meta_value as address
            FROM {$ordinace_table} p
            INNER JOIN {$meta_table} pm_address ON p.ID = pm_address.post_id 
                AND pm_address.meta_key = '_spd10_ordinace_address'
            LEFT JOIN {$meta_table} pm_status ON p.ID = pm_status.post_id 
                AND pm_status.meta_key = '_spd10_ordinace_geocode_status'
            WHERE p.post_type = 'ordinace' 
                AND p.post_status = 'publish'
                AND pm_address.meta_value != ''
                AND (pm_status.meta_value IS NULL OR pm_status.meta_value IN ('PENDING', 'FAILED'))
            ORDER BY p.post_modified DESC
            LIMIT %d
        ";
        
        return $wpdb->get_results($wpdb->prepare($sql, $limit), ARRAY_A);
    }
    
    /**
     * Export cache to CSV
     * 
     * @return string CSV content
     */
    public function export_to_csv() {
        global $wpdb;
        
        $results = $wpdb->get_results(
            "SELECT original_address, normalized_address, lat, lng, formatted_address, 
                    accuracy, provider, status, error_message, created_at, hit_count 
             FROM {$this->table_name} ORDER BY created_at DESC",
            ARRAY_A
        );
        
        $csv = "Original Address,Normalized Address,Latitude,Longitude,Formatted Address,Accuracy,Provider,Status,Error Message,Created At,Hit Count\n";
        
        foreach ($results as $row) {
            $csv .= '"' . str_replace('"', '""', $row['original_address']) . '",';
            $csv .= '"' . str_replace('"', '""', $row['normalized_address']) . '",';
            $csv .= $row['lat'] . ',';
            $csv .= $row['lng'] . ',';
            $csv .= '"' . str_replace('"', '""', $row['formatted_address']) . '",';
            $csv .= $row['accuracy'] . ',';
            $csv .= $row['provider'] . ',';
            $csv .= $row['status'] . ',';
            $csv .= '"' . str_replace('"', '""', $row['error_message']) . '",';
            $csv .= $row['created_at'] . ',';
            $csv .= $row['hit_count'] . "\n";
        }
        
        return $csv;
    }
}
