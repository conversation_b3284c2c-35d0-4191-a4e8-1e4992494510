<?php
/**
 * GeoJSON Cache Management Class
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * GeoJSON cache management class
 */
class SPD10_Ordinace_GeoJSON_Cache {
    
    /**
     * Cache expiration time in seconds (1 hour)
     */
    const CACHE_EXPIRATION = 3600;
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'add_rewrite_rules'));
        add_action('template_redirect', array($this, 'handle_geojson_request'));
        add_action('save_post', array($this, 'invalidate_cache_on_post_save'));
        add_action('delete_post', array($this, 'invalidate_cache_on_post_delete'));
        add_action('set_object_terms', array($this, 'invalidate_cache_on_term_change'), 10, 6);

        // Ensure rewrite rules are flushed when needed
        add_action('wp_loaded', array($this, 'maybe_flush_rewrite_rules'));
    }

    /**
     * Maybe flush rewrite rules if needed
     */
    public function maybe_flush_rewrite_rules() {
        if (get_option('spd10_ordinace_flush_rewrite_rules')) {
            flush_rewrite_rules();
            delete_option('spd10_ordinace_flush_rewrite_rules');
        }
    }
    
    /**
     * Add rewrite rules for GeoJSON endpoint
     */
    public function add_rewrite_rules() {
        add_rewrite_rule(
            '^ordinace-geojson/?$',
            'index.php?spd10_ordinace_geojson=1',
            'top'
        );
        
        add_rewrite_rule(
            '^ordinace-geojson/([^/]+)/?$',
            'index.php?spd10_ordinace_geojson=1&ordinace_type=$matches[1]',
            'top'
        );
        
        // Add query vars
        add_filter('query_vars', array($this, 'add_query_vars'));
    }
    
    /**
     * Add custom query vars
     */
    public function add_query_vars($vars) {
        $vars[] = 'spd10_ordinace_geojson';
        $vars[] = 'ordinace_type';
        return $vars;
    }
    
    /**
     * Handle GeoJSON requests
     */
    public function handle_geojson_request() {
        // Debug logging
        error_log('SPD10 GeoJSON: handle_geojson_request called');
        error_log('SPD10 GeoJSON: query var value: ' . get_query_var('spd10_ordinace_geojson'));

        if (!get_query_var('spd10_ordinace_geojson')) {
            return;
        }

        error_log('SPD10 GeoJSON: Processing request');
        
        // Set headers
        header('Content-Type: application/json');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: Content-Type');
        
        // Get filter parameters
        $type_filter = get_query_var('ordinace_type');
        $quarter_filter = isset($_GET['quarter']) ? sanitize_text_field($_GET['quarter']) : '';
        
        // Generate cache key
        $cache_key = $this->generate_cache_key($type_filter, $quarter_filter);
        
        // Try to get from cache
        $geojson_data = get_transient($cache_key);

        if (false === $geojson_data) {
            // Generate fresh GeoJSON data
            $geojson_data = $this->generate_geojson_data($type_filter, $quarter_filter);

            // Cache the data
            set_transient($cache_key, $geojson_data, self::CACHE_EXPIRATION);
        }
        
        // Output and exit
        echo wp_json_encode($geojson_data);
        exit;
    }
    
    /**
     * Generate cache key
     */
    private function generate_cache_key($type_filter = '', $quarter_filter = '') {
        $key_parts = array(
            'spd10_ordinace_geojson',
            $type_filter ?: 'all',
            $quarter_filter ?: 'all',
        );
        
        return implode('_', $key_parts);
    }
    
    /**
     * Generate GeoJSON data
     */
    private function generate_geojson_data($type_filter = '', $quarter_filter = '') {
        // Query arguments - get all ordinace with coordinates
        $query_args = array(
            'post_type' => 'ordinace',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'meta_query' => array(
                'relation' => 'AND',
                array(
                    'key' => '_spd10_ordinace_lat',
                    'value' => '',
                    'compare' => '!=',
                ),
                array(
                    'key' => '_spd10_ordinace_lng',
                    'value' => '',
                    'compare' => '!=',
                ),
                array(
                    'key' => '_spd10_ordinace_lat',
                    'value' => '0',
                    'compare' => '!=',
                ),
                array(
                    'key' => '_spd10_ordinace_lng',
                    'value' => '0',
                    'compare' => '!=',
                ),
            ),
        );
        
        // Add type filter
        if (!empty($type_filter) && $type_filter !== 'all') {
            $query_args['tax_query'] = array(
                array(
                    'taxonomy' => 'ordinace_typ',
                    'field' => 'slug',
                    'terms' => $type_filter,
                ),
            );
        }
        
        // Add quarter filter
        if (!empty($quarter_filter) && $quarter_filter !== 'all') {
            $query_args['meta_query'][] = array(
                'key' => '_spd10_ordinace_quarter',
                'value' => $quarter_filter,
                'compare' => 'LIKE',
            );
        }
        
        $ordinace_query = new WP_Query($query_args);

        // Debug logging
        error_log('SPD10 GeoJSON Query: ' . print_r($query_args, true));
        error_log('SPD10 GeoJSON Found posts: ' . $ordinace_query->found_posts);

        // Initialize GeoJSON structure
        $geojson = array(
            'type' => 'FeatureCollection',
            'features' => array(),
            'metadata' => array(
                'generated_at' => current_time('c'),
                'total_features' => 0,
                'query_args' => $query_args,
                'found_posts' => $ordinace_query->found_posts,
                'filters' => array(
                    'type' => $type_filter ?: 'all',
                    'quarter' => $quarter_filter ?: 'all',
                ),
            ),
        );
        
        if ($ordinace_query->have_posts()) {
            while ($ordinace_query->have_posts()) {
                $ordinace_query->the_post();
                $post_id = get_the_ID();
                
                // Get coordinates
                $lat = get_post_meta($post_id, '_spd10_ordinace_lat', true);
                $lng = get_post_meta($post_id, '_spd10_ordinace_lng', true);
                
                if (empty($lat) || empty($lng)) {
                    continue;
                }
                
                // Get ordinace type
                $types = wp_get_post_terms($post_id, 'ordinace_typ');
                $type = !empty($types) ? $types[0]->slug : 'unknown';
                
                // Get metadata
                $doctors_names = get_post_meta($post_id, '_spd10_ordinace_doctors_names', true);
                $organization = get_post_meta($post_id, '_spd10_ordinace_organization', true);
                $address = get_post_meta($post_id, '_spd10_ordinace_address', true);
                $quarter = get_post_meta($post_id, '_spd10_ordinace_quarter', true);
                $email = get_post_meta($post_id, '_spd10_ordinace_email', true);
                $phone = get_post_meta($post_id, '_spd10_ordinace_phone', true);
                $website = get_post_meta($post_id, '_spd10_ordinace_website', true);
                
                // Create GeoJSON feature
                $feature = array(
                    'type' => 'Feature',
                    'geometry' => array(
                        'type' => 'Point',
                        'coordinates' => array(floatval($lng), floatval($lat)),
                    ),
                    'properties' => array(
                        'id' => $post_id,
                        'title' => get_the_title(),
                        'type' => $type,
                        'doctors_names' => $doctors_names,
                        'organization' => $organization,
                        'address' => $address,
                        'quarter' => $quarter,
                        'email' => $email,
                        'phone' => $phone,
                        'website' => $website,
                        'permalink' => get_permalink($post_id),
                        'updated_at' => get_the_modified_time('c'),
                    ),
                );
                
                $geojson['features'][] = $feature;
            }
            wp_reset_postdata();
        }
        
        $geojson['metadata']['total_features'] = count($geojson['features']);
        
        return $geojson;
    }
    
    /**
     * Invalidate cache when post is saved
     */
    public function invalidate_cache_on_post_save($post_id) {
        if (get_post_type($post_id) === 'ordinace') {
            $this->invalidate_all_cache();
        }
    }
    
    /**
     * Invalidate cache when post is deleted
     */
    public function invalidate_cache_on_post_delete($post_id) {
        if (get_post_type($post_id) === 'ordinace') {
            $this->invalidate_all_cache();
        }
    }
    
    /**
     * Invalidate cache when terms are changed
     */
    public function invalidate_cache_on_term_change($object_id, $terms, $tt_ids, $taxonomy, $append, $old_tt_ids) {
        if ($taxonomy === 'ordinace_typ' && get_post_type($object_id) === 'ordinace') {
            $this->invalidate_all_cache();
        }
    }
    
    /**
     * Invalidate all GeoJSON cache
     */
    public function invalidate_all_cache() {
        global $wpdb;
        
        // Delete all GeoJSON cache transients
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_spd10_ordinace_geojson_%'
        ));
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_timeout_spd10_ordinace_geojson_%'
        ));
    }
    
    /**
     * Get cache statistics
     */
    public function get_cache_statistics() {
        global $wpdb;
        
        $cache_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_spd10_ordinace_geojson_%'
        ));
        
        return array(
            'cached_endpoints' => intval($cache_count),
            'cache_expiration_minutes' => self::CACHE_EXPIRATION / 60,
        );
    }
    
    /**
     * Get GeoJSON endpoint URL
     */
    public static function get_geojson_url($type_filter = '', $quarter_filter = '') {
        $base_url = home_url('ordinace-geojson');
        
        if (!empty($type_filter)) {
            $base_url .= '/' . urlencode($type_filter);
        }
        
        $params = array();
        if (!empty($quarter_filter)) {
            $params['quarter'] = $quarter_filter;
        }
        
        if (!empty($params)) {
            $base_url .= '?' . http_build_query($params);
        }
        
        return $base_url;
    }
}

// GeoJSON cache is now initialized in main plugin file
