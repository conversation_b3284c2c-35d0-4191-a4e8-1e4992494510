<?php
/**
 * Importer Class
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main importer class with deduplication and validation
 */
class SPD10_Ordinace_Importer {
    
    /**
     * Sheets connector instance
     */
    private $sheets_connector;
    
    /**
     * Column mapper instance
     */
    private $column_mapper;
    
    /**
     * Import log
     */
    private $import_log = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->sheets_connector = new SPD10_Ordinace_Sheets_Connector();
        $this->column_mapper = new SPD10_Ordinace_Column_Mapper();
    }
    
    /**
     * Perform import from Google Sheets
     * 
     * @param array $args Import arguments
     * @return array|WP_Error Import result or error
     */
    public function import($args) {
        $defaults = array(
            'sheet_id' => spd10_ordinace_get_option('google_sheet_id'),
            'tab_name' => '',
            'dry_run' => false,
            'taxonomy_term' => '', // praktik-dospeli or pediatr
            'force_update' => false,
        );
        
        $args = wp_parse_args($args, $defaults);
        
        // Validate arguments
        $validation = $this->validate_import_args($args);
        if (is_wp_error($validation)) {
            return $validation;
        }
        
        // Initialize import log
        $this->import_log = array(
            'start_time' => current_time('mysql'),
            'args' => $args,
            'stats' => array(
                'total_rows' => 0,
                'created' => 0,
                'updated' => 0,
                'skipped' => 0,
                'failed' => 0,
            ),
            'messages' => array(),
            'errors' => array(),
        );
        
        try {
            // Fetch data from Google Sheets
            $this->log_message(__('Načítání dat z Google Sheets...', SPD10_ORDINACE_TEXT_DOMAIN));
            $csv_data = $this->sheets_connector->get_csv_data($args['sheet_id'], $args['tab_name']);
            
            if (is_wp_error($csv_data)) {
                return $csv_data;
            }
            
            $this->import_log['stats']['total_rows'] = $csv_data['total_rows'];
            $this->log_message(sprintf(__('Načteno %d řádků z CSV.', SPD10_ORDINACE_TEXT_DOMAIN), $csv_data['total_rows']));
            
            // Get column mapping
            $mapping = $this->column_mapper->get_mapping($args['sheet_id'], $args['tab_name']);
            
            // Validate mapping
            $mapping_validation = $this->column_mapper->validate_mapping($mapping, $csv_data['header']);
            if (is_wp_error($mapping_validation)) {
                return $mapping_validation;
            }
            
            if (!empty($mapping_validation['warnings'])) {
                foreach ($mapping_validation['warnings'] as $warning) {
                    $this->log_message($warning, 'warning');
                }
            }
            
            // Process each row
            $this->log_message(__('Zpracování řádků...', SPD10_ORDINACE_TEXT_DOMAIN));
            
            foreach ($csv_data['rows'] as $row_index => $row_data) {
                $result = $this->process_row($row_data, $mapping, $args);
                
                if (is_wp_error($result)) {
                    $this->import_log['stats']['failed']++;
                    $this->log_error(sprintf(__('Řádek %d: %s', SPD10_ORDINACE_TEXT_DOMAIN), 
                        $row_index + 1, $result->get_error_message()));
                } else {
                    $this->import_log['stats'][$result['action']]++;
                    $this->log_message(sprintf(__('Řádek %d: %s - %s', SPD10_ORDINACE_TEXT_DOMAIN), 
                        $row_index + 1, $result['action'], $result['title']));
                }
            }
            
            $this->import_log['end_time'] = current_time('mysql');
            $this->import_log['duration'] = time() - strtotime($this->import_log['start_time']);
            
            // Save import log
            $this->save_import_log();
            
            return $this->import_log;
            
        } catch (Exception $e) {
            $this->log_error($e->getMessage());
            return new WP_Error('import_exception', $e->getMessage());
        }
    }
    
    /**
     * Process single row
     * 
     * @param array $row_data Raw row data
     * @param array $mapping Column mapping
     * @param array $args Import arguments
     * @return array|WP_Error Process result
     */
    private function process_row($row_data, $mapping, $args) {
        // Map row data
        $mapped_data = $this->column_mapper->map_row_data($row_data, $mapping);
        
        // Validate required fields
        $validation = $this->validate_row_data($mapped_data);
        if (is_wp_error($validation)) {
            return $validation;
        }
        
        // Check for existing post (deduplication)
        $existing_post = $this->find_existing_post($mapped_data);
        
        if ($existing_post) {
            // Update existing post
            if ($args['force_update'] || $this->should_update_post($existing_post, $mapped_data)) {
                if (!$args['dry_run']) {
                    $result = $this->update_post($existing_post->ID, $mapped_data, $args);
                    if (is_wp_error($result)) {
                        return $result;
                    }
                }
                return array('action' => 'updated', 'title' => $existing_post->post_title, 'post_id' => $existing_post->ID);
            } else {
                return array('action' => 'skipped', 'title' => $existing_post->post_title, 'post_id' => $existing_post->ID);
            }
        } else {
            // Create new post
            if (!$args['dry_run']) {
                $post_id = $this->create_post($mapped_data, $args);
                if (is_wp_error($post_id)) {
                    return $post_id;
                }
            } else {
                $post_id = 0; // Dry run
            }
            
            $title = !empty($mapped_data['organization']) ? $mapped_data['organization'] : $mapped_data['address'];
            return array('action' => 'created', 'title' => $title, 'post_id' => $post_id);
        }
    }
    
    /**
     * Validate import arguments
     * 
     * @param array $args Import arguments
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    private function validate_import_args($args) {
        if (empty($args['sheet_id'])) {
            return new WP_Error('missing_sheet_id', __('ID Google Sheets je povinné.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        return true;
    }
    
    /**
     * Validate row data
     * 
     * @param array $mapped_data Mapped row data
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    private function validate_row_data($mapped_data) {
        // Check required fields
        if (empty($mapped_data['address'])) {
            return new WP_Error('missing_address', __('Adresa je povinná.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // At least one of organization or doctors_names must be present
        if (empty($mapped_data['organization']) && empty($mapped_data['doctors_names'])) {
            return new WP_Error('missing_name', __('Musí být zadána organizace nebo jméno lékaře.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        return true;
    }
    
    /**
     * Find existing post by deduplication logic
     * 
     * @param array $mapped_data Mapped row data
     * @return WP_Post|null Existing post or null
     */
    private function find_existing_post($mapped_data) {
        // First try by source_row_id if available
        if (!empty($mapped_data['source_row_id'])) {
            $posts = get_posts(array(
                'post_type' => 'ordinace',
                'meta_query' => array(
                    array(
                        'key' => '_spd10_ordinace_source_row_id',
                        'value' => $mapped_data['source_row_id'],
                    ),
                ),
                'posts_per_page' => 1,
                'post_status' => array('publish', 'draft'),
            ));

            if (!empty($posts)) {
                return $posts[0];
            }
        }

        // For paste imports, be more strict - only use source_row_id
        if (!empty($mapped_data['source_row_id']) && strpos($mapped_data['source_row_id'], 'paste_import_') === 0) {
            // For paste imports, don't do heuristic matching to avoid false positives
            return null;
        }

        // Fallback: heuristic matching by address + organization/names (only for non-paste imports)
        $search_terms = array();

        if (!empty($mapped_data['address'])) {
            $search_terms[] = $mapped_data['address'];
        }

        if (!empty($mapped_data['organization'])) {
            $search_terms[] = $mapped_data['organization'];
        }

        if (!empty($search_terms)) {
            $posts = get_posts(array(
                'post_type' => 'ordinace',
                'meta_query' => array(
                    'relation' => 'AND', // Changed from OR to AND for stricter matching
                    array(
                        'key' => '_spd10_ordinace_address',
                        'value' => $mapped_data['address'],
                        'compare' => '=', // Changed from LIKE to exact match
                    ),
                    array(
                        'key' => '_spd10_ordinace_organization',
                        'value' => $mapped_data['organization'],
                        'compare' => '=', // Changed from LIKE to exact match
                    ),
                ),
                'posts_per_page' => 1, // Changed from 5 to 1
                'post_status' => array('publish', 'draft'),
            ));

            if (!empty($posts)) {
                return $posts[0];
            }
        }

        return null;
    }
    
    /**
     * Check if post should be updated
     * 
     * @param WP_Post $existing_post Existing post
     * @param array $mapped_data New data
     * @return bool Should update
     */
    private function should_update_post($existing_post, $mapped_data) {
        $last_import = get_post_meta($existing_post->ID, '_spd10_ordinace_updated_from_source_at', true);
        
        // If never imported from source, update
        if (empty($last_import)) {
            return true;
        }
        
        // Update if data is significantly different
        $current_address = get_post_meta($existing_post->ID, '_spd10_ordinace_address', true);
        $current_organization = get_post_meta($existing_post->ID, '_spd10_ordinace_organization', true);
        
        if ($current_address !== $mapped_data['address'] || 
            $current_organization !== $mapped_data['organization']) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Create new post
     * 
     * @param array $mapped_data Mapped data
     * @param array $args Import arguments
     * @return int|WP_Error Post ID or error
     */
    private function create_post($mapped_data, $args) {
        // Use doctors_names as primary title, fallback to organization, then address
        $title = '';
        if (!empty($mapped_data['doctors_names'])) {
            $title = $mapped_data['doctors_names'];
        } elseif (!empty($mapped_data['organization'])) {
            $title = $mapped_data['organization'];
        } else {
            $title = $mapped_data['address'];
        }

        $post_data = array(
            'post_type' => 'ordinace',
            'post_title' => sanitize_text_field($title),
            'post_status' => 'publish',
            'post_content' => '',
        );

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            return $post_id;
        }

        // Save metadata
        $this->save_post_metadata($post_id, $mapped_data);

        // Set taxonomy term - check both args and mapped_data
        $taxonomy_term = null;
        if (!empty($args['taxonomy_term'])) {
            $taxonomy_term = $args['taxonomy_term'];
        } elseif (!empty($mapped_data['ordinace_typ'])) {
            $taxonomy_term = $mapped_data['ordinace_typ'];
        } elseif (!empty($mapped_data['type'])) {
            $taxonomy_term = $mapped_data['type'];
        }

        if ($taxonomy_term) {
            wp_set_object_terms($post_id, $taxonomy_term, 'ordinace_typ');
        }

        return $post_id;
    }
    
    /**
     * Update existing post
     * 
     * @param int $post_id Post ID
     * @param array $mapped_data Mapped data
     * @param array $args Import arguments
     * @return bool|WP_Error Success or error
     */
    private function update_post($post_id, $mapped_data, $args) {
        // Use doctors_names as primary title, fallback to organization, then address
        $title = '';
        if (!empty($mapped_data['doctors_names'])) {
            $title = $mapped_data['doctors_names'];
        } elseif (!empty($mapped_data['organization'])) {
            $title = $mapped_data['organization'];
        } else {
            $title = $mapped_data['address'];
        }
        
        $post_data = array(
            'ID' => $post_id,
            'post_title' => sanitize_text_field($title),
        );
        
        $result = wp_update_post($post_data);
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        // Update metadata
        $this->save_post_metadata($post_id, $mapped_data);
        
        // Update taxonomy term - check both args and mapped_data
        $taxonomy_term = null;
        if (!empty($args['taxonomy_term'])) {
            $taxonomy_term = $args['taxonomy_term'];
        } elseif (!empty($mapped_data['ordinace_typ'])) {
            $taxonomy_term = $mapped_data['ordinace_typ'];
        } elseif (!empty($mapped_data['type'])) {
            $taxonomy_term = $mapped_data['type'];
        }

        if ($taxonomy_term) {
            wp_set_object_terms($post_id, $taxonomy_term, 'ordinace_typ');
        }
        
        return true;
    }
    
    /**
     * Save post metadata
     * 
     * @param int $post_id Post ID
     * @param array $mapped_data Mapped data
     */
    private function save_post_metadata($post_id, $mapped_data) {
        $meta_fields = array(
            'doctors_names', 'organization', 'quarter', 'address', 
            'email', 'phone', 'website', 'source_row_id', 'updated_from_source_at'
        );
        
        foreach ($meta_fields as $field) {
            if (isset($mapped_data[$field])) {
                update_post_meta($post_id, '_spd10_ordinace_' . $field, $mapped_data[$field]);
            }
        }
        
        // Trigger geocoding for new addresses
        if (!empty($mapped_data['address'])) {
            // Set geocode status to pending first
            update_post_meta($post_id, '_spd10_ordinace_geocode_status', 'PENDING');

            // Try to geocode immediately with timeout protection
            if (class_exists('SPD10_Ordinace_Geocoder')) {
                try {
                    // Set a shorter timeout for geocoding during import
                    $geocoder = new SPD10_Ordinace_Geocoder();
                    $geocode_result = $geocoder->geocode($mapped_data['address'], true); // Use cache

                    if (!is_wp_error($geocode_result)) {
                        update_post_meta($post_id, '_spd10_ordinace_lat', $geocode_result['lat']);
                        update_post_meta($post_id, '_spd10_ordinace_lng', $geocode_result['lng']);
                        update_post_meta($post_id, '_spd10_ordinace_geocode_status', 'OK');
                        if (isset($geocode_result['formatted_address'])) {
                            update_post_meta($post_id, '_spd10_ordinace_formatted_address', $geocode_result['formatted_address']);
                        }
                    } else {
                        // Keep as PENDING if geocoding fails - will be retried later
                        update_post_meta($post_id, '_spd10_ordinace_geocode_status', 'PENDING');
                        update_post_meta($post_id, '_spd10_ordinace_geocode_queued_at', current_time('mysql'));
                    }
                } catch (Exception $e) {
                    // If geocoding throws exception, keep as PENDING
                    update_post_meta($post_id, '_spd10_ordinace_geocode_status', 'PENDING');
                    update_post_meta($post_id, '_spd10_ordinace_geocode_queued_at', current_time('mysql'));
                }
            } else {
                // Geocoder not available, keep as PENDING
                update_post_meta($post_id, '_spd10_ordinace_geocode_status', 'PENDING');
                update_post_meta($post_id, '_spd10_ordinace_geocode_queued_at', current_time('mysql'));
            }
        }
    }
    
    /**
     * Log message
     * 
     * @param string $message Message
     * @param string $type Message type
     */
    private function log_message($message, $type = 'info') {
        $this->import_log['messages'][] = array(
            'time' => current_time('mysql'),
            'type' => $type,
            'message' => $message,
        );
    }
    
    /**
     * Log error
     * 
     * @param string $error Error message
     */
    private function log_error($error) {
        $this->import_log['errors'][] = array(
            'time' => current_time('mysql'),
            'error' => $error,
        );
    }
    
    /**
     * Save import log
     */
    private function save_import_log() {
        $logs = get_option('spd10_ordinace_import_logs', array());
        
        // Keep only last 10 logs
        if (count($logs) >= 10) {
            $logs = array_slice($logs, -9);
        }
        
        $logs[] = $this->import_log;
        update_option('spd10_ordinace_import_logs', $logs);
    }
    
    /**
     * Get import logs
     *
     * @return array Import logs
     */
    public function get_import_logs() {
        return get_option('spd10_ordinace_import_logs', array());
    }

    /**
     * Clear import logs
     */
    public function clear_import_logs() {
        delete_option('spd10_ordinace_import_logs');
    }

    /**
     * Get import statistics
     *
     * @return array Statistics
     */
    public function get_import_statistics() {
        $logs = $this->get_import_logs();

        $stats = array(
            'total_imports' => count($logs),
            'last_import' => null,
            'total_created' => 0,
            'total_updated' => 0,
            'total_failed' => 0,
        );

        if (!empty($logs)) {
            $stats['last_import'] = end($logs);

            foreach ($logs as $log) {
                if (isset($log['stats'])) {
                    $stats['total_created'] += $log['stats']['created'];
                    $stats['total_updated'] += $log['stats']['updated'];
                    $stats['total_failed'] += $log['stats']['failed'];
                }
            }
        }

        return $stats;
    }

    /**
     * Import single ordinace data (used by CSV importer)
     *
     * @param array $mapped_data Mapped ordinace data
     * @return array|WP_Error Import result
     */
    public function import_ordinace_data($mapped_data) {
        // Validate required fields
        $validation = $this->validate_row_data($mapped_data);
        if (is_wp_error($validation)) {
            return $validation;
        }

        // Check for existing post (deduplication)
        $existing_post = $this->find_existing_ordinace($mapped_data);

        if ($existing_post) {
            // Update existing post
            if ($this->should_update_post($existing_post, $mapped_data)) {
                $result = $this->update_post($existing_post->ID, $mapped_data, array('force_update' => true));
                if (is_wp_error($result)) {
                    return $result;
                }
                return array('action' => 'updated', 'title' => $existing_post->post_title, 'post_id' => $existing_post->ID);
            } else {
                return array('action' => 'skipped', 'title' => $existing_post->post_title, 'post_id' => $existing_post->ID);
            }
        } else {
            // Create new post
            $post_id = $this->create_post($mapped_data, array());
            if (is_wp_error($post_id)) {
                return $post_id;
            }

            // Use doctors_names as primary title, fallback to organization, then address
            $title = '';
            if (!empty($mapped_data['doctors_names'])) {
                $title = $mapped_data['doctors_names'];
            } elseif (!empty($mapped_data['organization'])) {
                $title = $mapped_data['organization'];
            } else {
                $title = $mapped_data['address'];
            }

            return array('action' => 'created', 'title' => $title, 'post_id' => $post_id);
        }
    }

    /**
     * Find existing ordinace (public method for CSV importer)
     *
     * @param array $mapped_data Mapped ordinace data
     * @return WP_Post|null Existing post or null
     */
    public function find_existing_ordinace($mapped_data) {
        return $this->find_existing_post($mapped_data);
    }
}
