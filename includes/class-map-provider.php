<?php
/**
 * Map Provider Management Class
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Map provider management class
 */
class SPD10_Ordinace_Map_Provider {
    
    /**
     * Available map providers
     */
    private $providers = array();
    
    /**
     * Current active provider
     */
    private $active_provider;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_providers();
        $this->set_active_provider();
    }
    
    /**
     * Initialize available providers
     */
    private function init_providers() {
        $this->providers = array(
            'leaflet' => array(
                'name' => __('Leaflet + OpenStreetMap', SPD10_ORDINACE_TEXT_DOMAIN),
                'description' => __('Bezplatný mapový provider s OpenStreetMap daty', SPD10_ORDINACE_TEXT_DOMAIN),
                'requires_api_key' => false,
                'config_requirements' => array(),
                'js_library' => 'leaflet',
                'css_library' => 'leaflet',
                'default_zoom' => 13,
                'max_zoom' => 18,
                'attribution' => '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            ),
            'google' => array(
                'name' => __('Google Maps', SPD10_ORDINACE_TEXT_DOMAIN),
                'description' => __('Google Maps s pokročilými funkcemi', SPD10_ORDINACE_TEXT_DOMAIN),
                'requires_api_key' => true,
                'config_requirements' => array('google_maps_api_key'),
                'js_library' => 'google-maps',
                'css_library' => null,
                'default_zoom' => 13,
                'max_zoom' => 20,
                'attribution' => '&copy; Google',
            ),
            'mapbox' => array(
                'name' => __('Mapbox', SPD10_ORDINACE_TEXT_DOMAIN),
                'description' => __('Mapbox s vlastními styly map', SPD10_ORDINACE_TEXT_DOMAIN),
                'requires_api_key' => true,
                'config_requirements' => array('mapbox_api_key'),
                'js_library' => 'mapbox',
                'css_library' => 'mapbox',
                'default_zoom' => 13,
                'max_zoom' => 18,
                'attribution' => '&copy; <a href="https://www.mapbox.com/">Mapbox</a> &copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
            ),
        );
    }
    
    /**
     * Set active provider based on configuration
     */
    private function set_active_provider() {
        $provider_name = spd10_ordinace_get_option('map_provider', 'leaflet');
        
        if (isset($this->providers[$provider_name]) && $this->is_provider_available($provider_name)) {
            $this->active_provider = $provider_name;
        } else {
            // Fallback to first available provider
            foreach ($this->providers as $name => $config) {
                if ($this->is_provider_available($name)) {
                    $this->active_provider = $name;
                    break;
                }
            }
        }
        
        // Default to leaflet if nothing else works
        if (!$this->active_provider) {
            $this->active_provider = 'leaflet';
        }
    }
    
    /**
     * Check if provider is available (has required configuration)
     * 
     * @param string $provider_name Provider name
     * @return bool True if available
     */
    public function is_provider_available($provider_name) {
        if (!isset($this->providers[$provider_name])) {
            return false;
        }
        
        $provider = $this->providers[$provider_name];
        
        // Check if API key is required and available
        if ($provider['requires_api_key']) {
            foreach ($provider['config_requirements'] as $requirement) {
                $option_value = spd10_ordinace_get_option($requirement);
                if (empty($option_value)) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Get current active provider
     * 
     * @return string Active provider name
     */
    public function get_active_provider() {
        return $this->active_provider;
    }
    
    /**
     * Get provider configuration
     * 
     * @param string $provider_name Optional provider name, defaults to active
     * @return array|null Provider configuration
     */
    public function get_provider_config($provider_name = null) {
        if (!$provider_name) {
            $provider_name = $this->active_provider;
        }
        
        return isset($this->providers[$provider_name]) ? $this->providers[$provider_name] : null;
    }
    
    /**
     * Get all available providers
     * 
     * @return array All providers with availability status
     */
    public function get_all_providers() {
        $providers_with_status = array();
        
        foreach ($this->providers as $name => $config) {
            $providers_with_status[$name] = array_merge($config, array(
                'available' => $this->is_provider_available($name),
                'active' => ($name === $this->active_provider),
            ));
        }
        
        return $providers_with_status;
    }
    
    /**
     * Get default map center for Prague 10
     * 
     * @return array Lat/lng coordinates
     */
    public function get_default_center() {
        return array(
            'lat' => 50.0755,  // Prague 10 center
            'lng' => 14.4378,
        );
    }
    
    /**
     * Get default map bounds for Prague 10
     * 
     * @return array Bounds array
     */
    public function get_default_bounds() {
        return array(
            'north' => 50.1200,
            'south' => 50.0300,
            'east' => 14.5200,
            'west' => 14.3500,
        );
    }
    
    /**
     * Enqueue map scripts and styles
     *
     * @param bool $admin Whether this is for admin area
     */
    public function enqueue_map_assets($admin = false) {
        $provider_config = $this->get_provider_config();

        if (!$provider_config) {
            return;
        }

        $suffix = $admin ? '-admin' : '';

        switch ($this->active_provider) {
            case 'leaflet':
                wp_enqueue_style(
                    'leaflet-css',
                    'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css',
                    array(),
                    '1.9.4'
                );
                wp_enqueue_script(
                    'leaflet-js',
                    'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js',
                    array(),
                    '1.9.4',
                    true
                );

                // Add Leaflet.markercluster for better marker grouping
                wp_enqueue_style(
                    'leaflet-markercluster-css',
                    'https://unpkg.com/leaflet.markercluster@1.5.3/dist/MarkerCluster.css',
                    array('leaflet-css'),
                    '1.5.3'
                );
                wp_enqueue_style(
                    'leaflet-markercluster-default-css',
                    'https://unpkg.com/leaflet.markercluster@1.5.3/dist/MarkerCluster.Default.css',
                    array('leaflet-markercluster-css'),
                    '1.5.3'
                );
                wp_enqueue_script(
                    'leaflet-markercluster-js',
                    'https://unpkg.com/leaflet.markercluster@1.5.3/dist/leaflet.markercluster.js',
                    array('leaflet-js'),
                    '1.5.3',
                    true
                );
                break;

            case 'google':
                $api_key = spd10_ordinace_get_option('google_maps_api_key');
                if ($api_key) {
                    wp_enqueue_script(
                        'google-maps-js',
                        'https://maps.googleapis.com/maps/api/js?key=' . esc_attr($api_key) . '&libraries=geometry',
                        array(),
                        null,
                        true
                    );
                }
                break;

            case 'mapbox':
                $api_key = spd10_ordinace_get_option('mapbox_api_key');
                if ($api_key) {
                    wp_enqueue_style(
                        'mapbox-css',
                        'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css',
                        array(),
                        '2.15.0'
                    );
                    wp_enqueue_script(
                        'mapbox-js',
                        'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js',
                        array(),
                        '2.15.0',
                        true
                    );
                }
                break;
        }

        // Enqueue our custom map script
        wp_enqueue_script(
            'spd10-ordinace-map' . $suffix,
            SPD10_ORDINACE_PLUGIN_URL . 'assets/js/spd10-ordinace-map' . $suffix . '.js',
            array('jquery'),
            SPD10_ORDINACE_VERSION,
            true
        );

        // Get GeoJSON URL safely
        $geojson_url = home_url('ordinace-geojson');
        if (class_exists('SPD10_Ordinace_GeoJSON_Cache')) {
            $geojson_url = SPD10_Ordinace_GeoJSON_Cache::get_geojson_url();
        }

        // Localize script with map configuration
        wp_localize_script(
            'spd10-ordinace-map' . $suffix,
            'spd10OrdinaceMap',
            array(
                'provider' => $this->active_provider,
                'config' => $provider_config,
                'center' => $this->get_default_center(),
                'bounds' => $this->get_default_bounds(),
                'apiKeys' => $this->get_api_keys(),
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'homeUrl' => home_url(),
                'geojsonUrl' => $geojson_url,
                'markerColors' => $this->get_marker_colors(),
                'nonce' => wp_create_nonce('spd10_ordinace_map_nonce'),
                'strings' => array(
                    'loading' => __('Načítání mapy...', SPD10_ORDINACE_TEXT_DOMAIN),
                    'error' => __('Chyba při načítání mapy', SPD10_ORDINACE_TEXT_DOMAIN),
                    'noResults' => __('Žádné výsledky', SPD10_ORDINACE_TEXT_DOMAIN),
                ),
            )
        );
    }
    
    /**
     * Get API keys for current provider
     *
     * @return array API keys
     */
    private function get_api_keys() {
        $keys = array();

        switch ($this->active_provider) {
            case 'google':
                $keys['google'] = spd10_ordinace_get_option('google_maps_api_key');
                break;
            case 'mapbox':
                $keys['mapbox'] = spd10_ordinace_get_option('mapbox_api_key');
                break;
        }

        return $keys;
    }

    /**
     * Get marker colors configuration
     *
     * @return array Marker colors
     */
    private function get_marker_colors() {
        return array(
            'pediatr' => spd10_ordinace_get_option('marker_color_pediatr', '#4CAF50'),
            'praktik-dospeli' => spd10_ordinace_get_option('marker_color_praktik', '#2196F3'),
        );
    }
    
    /**
     * Generate map HTML container
     * 
     * @param array $attributes Map attributes
     * @return string HTML
     */
    public function generate_map_html($attributes = array()) {
        $defaults = array(
            'id' => 'spd10-ordinace-map',
            'class' => 'spd10-ordinace-map',
            'height' => '400px',
            'width' => '100%',
        );
        
        $attributes = wp_parse_args($attributes, $defaults);
        
        $html = sprintf(
            '<div id="%s" class="%s" style="height: %s; width: %s;" data-provider="%s"></div>',
            esc_attr($attributes['id']),
            esc_attr($attributes['class']),
            esc_attr($attributes['height']),
            esc_attr($attributes['width']),
            esc_attr($this->active_provider)
        );
        
        return $html;
    }
}
