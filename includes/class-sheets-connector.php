<?php
/**
 * Google Sheets Connector Class
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for connecting to Google Sheets via CSV export
 */
class SPD10_Ordinace_Sheets_Connector {
    
    /**
     * Cache expiration time in seconds (1 hour)
     */
    const CACHE_EXPIRATION = 3600;
    
    /**
     * Get CSV data from Google Sheets
     * 
     * @param string $sheet_id Google Sheets ID
     * @param string $tab_name Tab name (gid will be resolved)
     * @return array|WP_Error Array of rows or WP_Error on failure
     */
    public function get_csv_data($sheet_id, $tab_name = '') {
        if (empty($sheet_id)) {
            return new WP_Error('missing_sheet_id', __('ID Google Sheets je povinné.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        // Generate cache key
        $cache_key = 'spd10_ordinace_csv_' . md5($sheet_id . '_' . $tab_name);
        
        // Try to get from cache first
        $cached_data = get_transient($cache_key);
        if (false !== $cached_data) {
            return $cached_data;
        }
        
        // Get GID for tab name
        $gid = $this->get_tab_gid($sheet_id, $tab_name);
        if (is_wp_error($gid)) {
            return $gid;
        }
        
        // Build CSV export URL
        $csv_url = $this->build_csv_url($sheet_id, $gid);
        
        // Fetch CSV data
        $csv_data = $this->fetch_csv_data($csv_url);
        if (is_wp_error($csv_data)) {
            return $csv_data;
        }
        
        // Parse CSV
        $parsed_data = $this->parse_csv_data($csv_data);
        if (is_wp_error($parsed_data)) {
            return $parsed_data;
        }
        
        // Cache the result
        set_transient($cache_key, $parsed_data, self::CACHE_EXPIRATION);
        
        return $parsed_data;
    }
    
    /**
     * Get tab GID from sheet ID and tab name
     * 
     * @param string $sheet_id Google Sheets ID
     * @param string $tab_name Tab name
     * @return string|WP_Error GID or WP_Error
     */
    private function get_tab_gid($sheet_id, $tab_name) {
        // If no tab name specified, use default (gid=0)
        if (empty($tab_name)) {
            return '0';
        }
        
        // Try to get GID from cached mapping
        $gid_cache_key = 'spd10_ordinace_gid_' . md5($sheet_id);
        $gid_mapping = get_transient($gid_cache_key);
        
        if (false !== $gid_mapping && isset($gid_mapping[$tab_name])) {
            return $gid_mapping[$tab_name];
        }
        
        // Fetch sheet metadata to get GID
        $metadata_url = "https://docs.google.com/spreadsheets/d/{$sheet_id}/edit#gid=0";
        
        $response = wp_remote_get($metadata_url, array(
            'timeout' => 30,
            'user-agent' => 'SPD10-Ordinace-Plugin/' . SPD10_ORDINACE_VERSION,
        ));
        
        if (is_wp_error($response)) {
            return new WP_Error('sheet_fetch_error', 
                sprintf(__('Nepodařilo se načíst metadata Google Sheets: %s', SPD10_ORDINACE_TEXT_DOMAIN), 
                $response->get_error_message()));
        }
        
        $body = wp_remote_retrieve_body($response);
        
        // Simple regex to find GID for tab name
        // This is a basic implementation - in production, you might want to use Google Sheets API
        if (preg_match('/gid=(\d+).*?"' . preg_quote($tab_name, '/') . '"/', $body, $matches)) {
            $gid = $matches[1];
            
            // Cache the GID mapping
            if (false === $gid_mapping) {
                $gid_mapping = array();
            }
            $gid_mapping[$tab_name] = $gid;
            set_transient($gid_cache_key, $gid_mapping, self::CACHE_EXPIRATION);
            
            return $gid;
        }
        
        // Fallback: try common tab names to GID mapping
        $common_gids = array(
            'Praktičtí lékaři' => '0',
            'Pediatři' => '1',
            'prakticni_lekari' => '0',
            'pediatri' => '1',
        );
        
        if (isset($common_gids[$tab_name])) {
            return $common_gids[$tab_name];
        }
        
        return new WP_Error('tab_not_found', 
            sprintf(__('Záložka "%s" nebyla nalezena v Google Sheets.', SPD10_ORDINACE_TEXT_DOMAIN), $tab_name));
    }
    
    /**
     * Build CSV export URL
     * 
     * @param string $sheet_id Google Sheets ID
     * @param string $gid Tab GID
     * @return string CSV URL
     */
    private function build_csv_url($sheet_id, $gid) {
        return "https://docs.google.com/spreadsheets/d/{$sheet_id}/export?format=csv&gid={$gid}";
    }
    
    /**
     * Fetch CSV data from URL
     * 
     * @param string $csv_url CSV export URL
     * @return string|WP_Error CSV data or WP_Error
     */
    private function fetch_csv_data($csv_url) {
        $response = wp_remote_get($csv_url, array(
            'timeout' => 60,
            'user-agent' => 'SPD10-Ordinace-Plugin/' . SPD10_ORDINACE_VERSION,
        ));
        
        if (is_wp_error($response)) {
            return new WP_Error('csv_fetch_error', 
                sprintf(__('Nepodařilo se načíst CSV data: %s', SPD10_ORDINACE_TEXT_DOMAIN), 
                $response->get_error_message()));
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return new WP_Error('csv_fetch_error', 
                sprintf(__('Chyba při načítání CSV: HTTP %d', SPD10_ORDINACE_TEXT_DOMAIN), $response_code));
        }
        
        $body = wp_remote_retrieve_body($response);
        
        if (empty($body)) {
            return new WP_Error('empty_csv', __('CSV soubor je prázdný.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        return $body;
    }
    
    /**
     * Parse CSV data into array
     * 
     * @param string $csv_data Raw CSV data
     * @return array|WP_Error Parsed data or WP_Error
     */
    private function parse_csv_data($csv_data) {
        $lines = str_getcsv($csv_data, "\n");
        
        if (empty($lines)) {
            return new WP_Error('invalid_csv', __('CSV data nelze parsovat.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $parsed_rows = array();
        $header = null;
        
        foreach ($lines as $line_num => $line) {
            $row = str_getcsv($line);
            
            // Skip empty rows
            if (empty(array_filter($row))) {
                continue;
            }
            
            if ($header === null) {
                // First non-empty row is header
                $header = array_map('trim', $row);
                continue;
            }
            
            // Create associative array with header as keys
            $row_data = array();
            foreach ($header as $col_index => $col_name) {
                $row_data[$col_name] = isset($row[$col_index]) ? trim($row[$col_index]) : '';
            }
            
            // Add row number for tracking
            $row_data['_row_number'] = $line_num + 1;
            
            $parsed_rows[] = $row_data;
        }
        
        if (empty($parsed_rows)) {
            return new WP_Error('no_data_rows', __('CSV neobsahuje žádné datové řádky.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        return array(
            'header' => $header,
            'rows' => $parsed_rows,
            'total_rows' => count($parsed_rows),
        );
    }
    
    /**
     * Clear cache for specific sheet
     * 
     * @param string $sheet_id Google Sheets ID
     * @param string $tab_name Optional tab name
     */
    public function clear_cache($sheet_id, $tab_name = '') {
        $cache_key = 'spd10_ordinace_csv_' . md5($sheet_id . '_' . $tab_name);
        delete_transient($cache_key);
        
        $gid_cache_key = 'spd10_ordinace_gid_' . md5($sheet_id);
        delete_transient($gid_cache_key);
    }
    
    /**
     * Test connection to Google Sheets
     * 
     * @param string $sheet_id Google Sheets ID
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public function test_connection($sheet_id) {
        $test_url = "https://docs.google.com/spreadsheets/d/{$sheet_id}/export?format=csv&gid=0";
        
        $response = wp_remote_head($test_url, array(
            'timeout' => 15,
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return new WP_Error('connection_failed', 
                sprintf(__('Nepodařilo se připojit k Google Sheets (HTTP %d)', SPD10_ORDINACE_TEXT_DOMAIN), $response_code));
        }
        
        return true;
    }
}
