<?php
/**
 * Template Loader for Ordinace Plugin
 *
 * @package SPD10_Ordinace
 */

if (!defined('ABSPATH')) {
    exit;
}

class SPD10_Ordinace_Template_Loader {
    /**
     * Initialize hooks
     */
    public function init() {
        add_filter('template_include', array($this, 'maybe_use_plugin_template'), 99);
        add_filter('theme_page_templates', array($this, 'register_page_templates'));
        add_action('pre_get_posts', array($this, 'adjust_archive_query'));
    }

    /**
     * Locate template in theme or fallback to plugin template
     */
    private function locate_template($template_name) {
        // Allow theme override
        $theme_path = locate_template(array('ordinace/' . $template_name, $template_name));
        if ($theme_path) {
            return $theme_path;
        }
        // Plugin fallback
        $plugin_path = trailingslashit(SPD10_ORDINACE_PLUGIN_DIR) . 'templates/' . $template_name;
        return file_exists($plugin_path) ? $plugin_path : '';
    }

    /**
     * Use plugin's single/archive templates if theme doesn't provide them
     */
    public function maybe_use_plugin_template($template) {
        // Debug: log template loading attempts
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('SPD10 Template Loader: Checking template for ' . get_query_var('post_type'));
        }

        if (is_singular('ordinace')) {
            $plugin_template = $this->locate_template('single-ordinace.php');
            if ($plugin_template) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('SPD10 Template Loader: Using single template: ' . $plugin_template);
                }
                return $plugin_template;
            }
        }

        if (is_post_type_archive('ordinace')) {
            $plugin_template = $this->locate_template('archive-ordinace.php');
            if ($plugin_template) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('SPD10 Template Loader: Using archive template: ' . $plugin_template);
                }
                return $plugin_template;
            }
        }

        return $template;
    }

    /**
     * Register custom page templates (landing)
     */
    public function register_page_templates($post_templates) {
        $post_templates['page-ordinace-landing.php'] = __('Ordinace: Úvodní stránka', SPD10_ORDINACE_TEXT_DOMAIN);
        return $post_templates;
    }

    /**
     * Load landing page template
     */
    public function load_page_template($template) {
        if (is_page()) {
            $page_template = get_page_template_slug(get_queried_object_id());
            if ($page_template === 'page-ordinace-landing.php') {
                $plugin_template = $this->locate_template('page-ordinace-landing.php');
                if ($plugin_template) {
                    return $plugin_template;
                }
            }
        }
        return $template;
    }

    /**
     * Adjust archive query for per_page setting
     */
    public function adjust_archive_query($query) {
        if (!is_admin() && $query->is_main_query() && $query->is_post_type_archive('ordinace')) {
            $per_page = intval(spd10_ordinace_get_option('posts_per_page', 10));
            if ($per_page > 0) {
                $query->set('posts_per_page', $per_page);
            }
        }
    }
}

// Bootstrap will be handled in main plugin file
