<?php
/**
 * Google Geocoder Provider
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Google Geocoding API provider
 */
class SPD10_Ordinace_Google_Geocoder implements SPD10_Ordinace_Geocoder_Interface {
    
    /**
     * Google Geocoding API endpoint
     */
    const API_ENDPOINT = 'https://maps.googleapis.com/maps/api/geocode/json';
    
    /**
     * API key
     */
    private $api_key;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->api_key = spd10_ordinace_get_option('google_maps_api_key', '');
    }
    
    /**
     * Geocode an address
     * 
     * @param string $address Address to geocode
     * @return array|WP_Error Geocoding result or error
     */
    public function geocode($address) {
        if (empty($this->api_key)) {
            return new WP_Error('missing_api_key', 
                __('Google Maps API klíč není nastaven.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $url = add_query_arg(array(
            'address' => $address,
            'key' => $this->api_key,
            'region' => 'cz', // Bias results to Czech Republic
            'language' => 'cs', // Czech language
        ), self::API_ENDPOINT);
        
        $response = wp_remote_get($url, array(
            'timeout' => 10,
            'user-agent' => 'SPD10-Ordinace-Plugin/' . SPD10_ORDINACE_VERSION,
        ));
        
        if (is_wp_error($response)) {
            return new WP_Error('google_request_failed', 
                sprintf(__('Google Geocoding požadavek selhal: %s', SPD10_ORDINACE_TEXT_DOMAIN), 
                $response->get_error_message()));
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return new WP_Error('google_http_error', 
                sprintf(__('Google Geocoding HTTP chyba: %d', SPD10_ORDINACE_TEXT_DOMAIN), $response_code));
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('google_json_error', 
                __('Chyba při parsování Google odpovědi.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        if (!isset($data['status'])) {
            return new WP_Error('google_invalid_response', 
                __('Neplatná odpověď od Google Geocoding API.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        switch ($data['status']) {
            case 'OK':
                break;
            case 'ZERO_RESULTS':
                return new WP_Error('no_results', 
                    __('Pro zadanou adresu nebyly nalezeny žádné výsledky.', SPD10_ORDINACE_TEXT_DOMAIN));
            case 'OVER_QUERY_LIMIT':
                return new WP_Error('quota_exceeded', 
                    __('Překročen denní limit Google Geocoding API.', SPD10_ORDINACE_TEXT_DOMAIN));
            case 'REQUEST_DENIED':
                return new WP_Error('request_denied', 
                    __('Google Geocoding API požadavek byl zamítnut.', SPD10_ORDINACE_TEXT_DOMAIN));
            case 'INVALID_REQUEST':
                return new WP_Error('invalid_request', 
                    __('Neplatný požadavek na Google Geocoding API.', SPD10_ORDINACE_TEXT_DOMAIN));
            default:
                return new WP_Error('google_unknown_error', 
                    sprintf(__('Neznámá chyba Google Geocoding API: %s', SPD10_ORDINACE_TEXT_DOMAIN), $data['status']));
        }
        
        if (empty($data['results'])) {
            return new WP_Error('no_results', 
                __('Žádné výsledky nenalezeny.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $result = $data['results'][0];
        $location = $result['geometry']['location'];
        
        $lat = floatval($location['lat']);
        $lng = floatval($location['lng']);
        
        // Basic validation for Prague area
        if ($lat < 49.9 || $lat > 50.2 || $lng < 14.2 || $lng > 14.7) {
            return new WP_Error('coordinates_out_of_range', 
                __('Souřadnice jsou mimo očekávanou oblast Prahy.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        return array(
            'lat' => $lat,
            'lng' => $lng,
            'formatted_address' => $result['formatted_address'],
            'accuracy' => $this->determine_accuracy($result),
            'provider' => 'google',
            'raw_response' => $result,
        );
    }
    
    /**
     * Determine accuracy level based on result type
     * 
     * @param array $result Google result
     * @return string Accuracy level
     */
    private function determine_accuracy($result) {
        $types = isset($result['types']) ? $result['types'] : array();
        
        // High accuracy for specific addresses
        if (in_array('street_address', $types) || in_array('premise', $types)) {
            return 'high';
        }
        
        // Medium accuracy for streets and intersections
        if (in_array('route', $types) || in_array('intersection', $types)) {
            return 'medium';
        }
        
        // Low accuracy for general areas
        return 'low';
    }
    
    /**
     * Get provider name
     * 
     * @return string Provider name
     */
    public function get_provider_name() {
        return __('Google Geocoding API', SPD10_ORDINACE_TEXT_DOMAIN);
    }
    
    /**
     * Check if provider is available
     * 
     * @return bool True if available
     */
    public function is_available() {
        return !empty($this->api_key);
    }
    
    /**
     * Get provider configuration requirements
     * 
     * @return array Configuration requirements
     */
    public function get_config_requirements() {
        return array(
            'api_key_required' => true,
            'api_key_field' => 'google_maps_api_key',
            'description' => __('Vysoce přesná služba Google. Vyžaduje API klíč.', SPD10_ORDINACE_TEXT_DOMAIN),
            'rate_limits' => __('Závisí na vašem Google Cloud plánu', SPD10_ORDINACE_TEXT_DOMAIN),
            'accuracy' => __('Velmi vysoká pro celý svět', SPD10_ORDINACE_TEXT_DOMAIN),
            'setup_url' => 'https://developers.google.com/maps/documentation/geocoding/get-api-key',
        );
    }
}
