<?php
/**
 * Mapbox Geocoder Provider
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Mapbox Geocoding API provider
 */
class SPD10_Ordinace_Mapbox_Geocoder implements SPD10_Ordinace_Geocoder_Interface {
    
    /**
     * Mapbox Geocoding API endpoint
     */
    const API_ENDPOINT = 'https://api.mapbox.com/geocoding/v5/mapbox.places/';
    
    /**
     * API key
     */
    private $api_key;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->api_key = spd10_ordinace_get_option('mapbox_api_key', '');
    }
    
    /**
     * Geocode an address
     * 
     * @param string $address Address to geocode
     * @return array|WP_Error Geocoding result or error
     */
    public function geocode($address) {
        if (empty($this->api_key)) {
            return new WP_Error('missing_api_key', 
                __('Mapbox API klíč není nastaven.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $url = self::API_ENDPOINT . urlencode($address) . '.json';
        $url = add_query_arg(array(
            'access_token' => $this->api_key,
            'country' => 'cz', // Limit to Czech Republic
            'language' => 'cs', // Czech language
            'limit' => 1,
        ), $url);
        
        $response = wp_remote_get($url, array(
            'timeout' => 10,
            'user-agent' => 'SPD10-Ordinace-Plugin/' . SPD10_ORDINACE_VERSION,
        ));
        
        if (is_wp_error($response)) {
            return new WP_Error('mapbox_request_failed', 
                sprintf(__('Mapbox požadavek selhal: %s', SPD10_ORDINACE_TEXT_DOMAIN), 
                $response->get_error_message()));
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return new WP_Error('mapbox_http_error', 
                sprintf(__('Mapbox HTTP chyba: %d', SPD10_ORDINACE_TEXT_DOMAIN), $response_code));
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('mapbox_json_error', 
                __('Chyba při parsování Mapbox odpovědi.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        if (!isset($data['features']) || empty($data['features'])) {
            return new WP_Error('no_results', 
                __('Pro zadanou adresu nebyly nalezeny žádné výsledky.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $result = $data['features'][0];
        
        if (!isset($result['geometry']['coordinates'])) {
            return new WP_Error('invalid_coordinates', 
                __('Neplatné souřadnice v odpovědi.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $coordinates = $result['geometry']['coordinates'];
        $lng = floatval($coordinates[0]); // Mapbox uses [lng, lat] format
        $lat = floatval($coordinates[1]);
        
        // Basic validation for Prague area
        if ($lat < 49.9 || $lat > 50.2 || $lng < 14.2 || $lng > 14.7) {
            return new WP_Error('coordinates_out_of_range', 
                __('Souřadnice jsou mimo očekávanou oblast Prahy.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        return array(
            'lat' => $lat,
            'lng' => $lng,
            'formatted_address' => isset($result['place_name']) ? $result['place_name'] : $address,
            'accuracy' => $this->determine_accuracy($result),
            'provider' => 'mapbox',
            'raw_response' => $result,
        );
    }
    
    /**
     * Determine accuracy level based on result type
     * 
     * @param array $result Mapbox result
     * @return string Accuracy level
     */
    private function determine_accuracy($result) {
        $place_type = isset($result['place_type']) ? $result['place_type'] : array();
        
        // High accuracy for specific addresses
        if (in_array('address', $place_type) || in_array('poi', $place_type)) {
            return 'high';
        }
        
        // Medium accuracy for streets
        if (in_array('street', $place_type)) {
            return 'medium';
        }
        
        // Low accuracy for general areas
        return 'low';
    }
    
    /**
     * Get provider name
     * 
     * @return string Provider name
     */
    public function get_provider_name() {
        return __('Mapbox Geocoding API', SPD10_ORDINACE_TEXT_DOMAIN);
    }
    
    /**
     * Check if provider is available
     * 
     * @return bool True if available
     */
    public function is_available() {
        return !empty($this->api_key);
    }
    
    /**
     * Get provider configuration requirements
     * 
     * @return array Configuration requirements
     */
    public function get_config_requirements() {
        return array(
            'api_key_required' => true,
            'api_key_field' => 'mapbox_api_key',
            'description' => __('Rychlá a přesná služba Mapbox. Vyžaduje API klíč.', SPD10_ORDINACE_TEXT_DOMAIN),
            'rate_limits' => __('100,000 požadavků zdarma měsíčně', SPD10_ORDINACE_TEXT_DOMAIN),
            'accuracy' => __('Vysoká pro celý svět', SPD10_ORDINACE_TEXT_DOMAIN),
            'setup_url' => 'https://docs.mapbox.com/api/search/geocoding/',
        );
    }
}
