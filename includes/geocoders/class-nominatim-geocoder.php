<?php
/**
 * Nominatim Geocoder Provider
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Nominatim (OpenStreetMap) geocoding provider
 */
class SPD10_Ordinace_Nominatim_Geocoder implements SPD10_Ordinace_Geocoder_Interface {
    
    /**
     * Nominatim API endpoint
     */
    const API_ENDPOINT = 'https://nominatim.openstreetmap.org/search';
    
    /**
     * User agent for requests
     */
    private $user_agent;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->user_agent = 'SPD10-Ordinace-Plugin/' . SPD10_ORDINACE_VERSION . ' (WordPress)';
    }
    
    /**
     * Geocode an address
     * 
     * @param string $address Address to geocode
     * @return array|WP_Error Geocoding result or error
     */
    public function geocode($address) {
        $url = add_query_arg(array(
            'q' => $address,
            'format' => 'json',
            'limit' => 1,
            'addressdetails' => 1,
            'countrycodes' => 'cz', // Limit to Czech Republic
        ), self::API_ENDPOINT);
        
        $response = wp_remote_get($url, array(
            'timeout' => 10,
            'user-agent' => $this->user_agent,
            'headers' => array(
                'Accept' => 'application/json',
            ),
        ));
        
        if (is_wp_error($response)) {
            return new WP_Error('nominatim_request_failed', 
                sprintf(__('Nominatim požadavek selhal: %s', SPD10_ORDINACE_TEXT_DOMAIN), 
                $response->get_error_message()));
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return new WP_Error('nominatim_http_error', 
                sprintf(__('Nominatim HTTP chyba: %d', SPD10_ORDINACE_TEXT_DOMAIN), $response_code));
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('nominatim_json_error', 
                __('Chyba při parsování Nominatim odpovědi.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        if (empty($data)) {
            return new WP_Error('no_results', 
                __('Pro zadanou adresu nebyly nalezeny žádné výsledky.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $result = $data[0];
        
        // Validate coordinates
        if (!isset($result['lat']) || !isset($result['lon'])) {
            return new WP_Error('invalid_coordinates', 
                __('Neplatné souřadnice v odpovědi.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        $lat = floatval($result['lat']);
        $lng = floatval($result['lon']);
        
        // Basic validation for Prague area
        if ($lat < 49.9 || $lat > 50.2 || $lng < 14.2 || $lng > 14.7) {
            return new WP_Error('coordinates_out_of_range', 
                __('Souřadnice jsou mimo očekávanou oblast Prahy.', SPD10_ORDINACE_TEXT_DOMAIN));
        }
        
        return array(
            'lat' => $lat,
            'lng' => $lng,
            'formatted_address' => isset($result['display_name']) ? $result['display_name'] : $address,
            'accuracy' => $this->determine_accuracy($result),
            'provider' => 'nominatim',
            'raw_response' => $result,
        );
    }
    
    /**
     * Determine accuracy level based on result type
     * 
     * @param array $result Nominatim result
     * @return string Accuracy level
     */
    private function determine_accuracy($result) {
        $type = isset($result['type']) ? $result['type'] : '';
        $class = isset($result['class']) ? $result['class'] : '';
        
        // High accuracy for specific addresses
        if (in_array($type, array('house', 'building', 'house_number'))) {
            return 'high';
        }
        
        // Medium accuracy for streets
        if ($class === 'highway' || $type === 'residential') {
            return 'medium';
        }
        
        // Low accuracy for general areas
        return 'low';
    }
    
    /**
     * Get provider name
     * 
     * @return string Provider name
     */
    public function get_provider_name() {
        return __('Nominatim (OpenStreetMap)', SPD10_ORDINACE_TEXT_DOMAIN);
    }
    
    /**
     * Check if provider is available
     * 
     * @return bool True if available
     */
    public function is_available() {
        // Nominatim is always available (no API key required)
        return true;
    }
    
    /**
     * Get provider configuration requirements
     * 
     * @return array Configuration requirements
     */
    public function get_config_requirements() {
        return array(
            'api_key_required' => false,
            'description' => __('Bezplatná služba OpenStreetMap. Žádná konfigurace není potřeba.', SPD10_ORDINACE_TEXT_DOMAIN),
            'rate_limits' => __('1 požadavek za sekundu, max 100 za hodinu', SPD10_ORDINACE_TEXT_DOMAIN),
            'accuracy' => __('Dobrá pro českou republiku', SPD10_ORDINACE_TEXT_DOMAIN),
        );
    }
}
