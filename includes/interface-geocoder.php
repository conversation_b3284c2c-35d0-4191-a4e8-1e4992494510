<?php
/**
 * Geocoder Interface
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Interface for geocoding providers
 */
interface SPD10_Ordinace_Geocoder_Interface {
    
    /**
     * Geocode an address
     * 
     * @param string $address Address to geocode
     * @return array|WP_Error Geocoding result or error
     */
    public function geocode($address);
    
    /**
     * Get provider name
     * 
     * @return string Provider name
     */
    public function get_provider_name();
    
    /**
     * Check if provider is available
     * 
     * @return bool True if available
     */
    public function is_available();
    
    /**
     * Get provider configuration requirements
     * 
     * @return array Configuration requirements
     */
    public function get_config_requirements();
}
