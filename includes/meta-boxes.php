<?php
/**
 * Meta Boxes for Ordinace Post Type
 *
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add meta boxes for ordinace post type
 */
function spd10_ordinace_add_meta_boxes() {
    add_meta_box(
        'spd10_ordinace_details',
        __('Detaily ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'spd10_ordinace_render_details_meta_box',
        'ordinace',
        'normal',
        'high'
    );
}

/**
 * Render ordinace details meta box
 */
function spd10_ordinace_render_details_meta_box($post) {
    // Add nonce for security
    wp_nonce_field('spd10_ordinace_meta_box_nonce', 'spd10_ordinace_meta_box_nonce');

    // Get current values
    $doctors_names = get_post_meta($post->ID, '_spd10_ordinace_doctors_names', true);
    $organization = get_post_meta($post->ID, '_spd10_ordinace_organization', true);
    $quarter = get_post_meta($post->ID, '_spd10_ordinace_quarter', true);
    $address = get_post_meta($post->ID, '_spd10_ordinace_address', true);
    $email = get_post_meta($post->ID, '_spd10_ordinace_email', true);
    $phone = get_post_meta($post->ID, '_spd10_ordinace_phone', true);
    $website = get_post_meta($post->ID, '_spd10_ordinace_website', true);
    $lat = get_post_meta($post->ID, '_spd10_ordinace_lat', true);
    $lng = get_post_meta($post->ID, '_spd10_ordinace_lng', true);
    $geocode_status = get_post_meta($post->ID, '_spd10_ordinace_geocode_status', true);
    $source_row_id = get_post_meta($post->ID, '_spd10_ordinace_source_row_id', true);
    $updated_from_source_at = get_post_meta($post->ID, '_spd10_ordinace_updated_from_source_at', true);

    // Include template for meta box form
    include SPD10_ORDINACE_PLUGIN_DIR . 'templates/metabox-ordinace-details.php';
}

/**
 * Save meta box data
 */
function spd10_ordinace_save_meta_boxes($post_id) {
    // Check if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Check if user has permission to edit post
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Check if this is our post type
    if (get_post_type($post_id) !== 'ordinace') {
        return;
    }

    // Verify nonce
    if (!isset($_POST['spd10_ordinace_meta_box_nonce']) ||
        !wp_verify_nonce($_POST['spd10_ordinace_meta_box_nonce'], 'spd10_ordinace_meta_box_nonce')) {
        return;
    }

    // Define meta fields to save
    $meta_fields = array(
        'spd10_ordinace_doctors_names' => 'textarea',
        'spd10_ordinace_organization' => 'text',
        'spd10_ordinace_quarter' => 'text',
        'spd10_ordinace_address' => 'text',
        'spd10_ordinace_email' => 'email',
        'spd10_ordinace_phone' => 'text',
        'spd10_ordinace_website' => 'url',
        'spd10_ordinace_lat' => 'float',
        'spd10_ordinace_lng' => 'float',
        'spd10_ordinace_geocode_status' => 'text',
        'spd10_ordinace_source_row_id' => 'text',
        'spd10_ordinace_updated_from_source_at' => 'text',
    );

    // Save meta fields
    foreach ($meta_fields as $field => $type) {
        if (isset($_POST[$field])) {
            $value = $_POST[$field];

            // Sanitize based on field type
            switch ($type) {
                case 'email':
                    $value = sanitize_email($value);
                    break;
                case 'url':
                    $value = esc_url_raw($value);
                    break;
                case 'textarea':
                    $value = sanitize_textarea_field($value);
                    break;
                case 'float':
                    $value = floatval($value);
                    break;
                default:
                    $value = sanitize_text_field($value);
            }

            update_post_meta($post_id, '_' . $field, $value);
        }
    }
}

// Hook meta box functions
add_action('add_meta_boxes', 'spd10_ordinace_add_meta_boxes');
add_action('save_post', 'spd10_ordinace_save_meta_boxes');
