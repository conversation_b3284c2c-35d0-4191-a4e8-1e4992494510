<?php
/**
 * Post Types and Taxonomies Registration
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register custom post types and taxonomies
 */
function spd10_ordinace_register_post_types() {
    // Register ordinace_typ taxonomy first
    spd10_ordinace_register_taxonomy();
    
    // Register ordinace post type
    spd10_ordinace_register_ordinace_post_type();
    
    // Add default terms
    spd10_ordinace_add_default_terms();
}

/**
 * Register ordinace_typ taxonomy
 */
function spd10_ordinace_register_taxonomy() {
    $labels = array(
        'name'              => __('Typy ordinací', SPD10_ORDINACE_TEXT_DOMAIN),
        'singular_name'     => __('Typ ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'menu_name'         => __('Typy ordinací', SPD10_ORDINACE_TEXT_DOMAIN),
        'all_items'         => __('Všechny typy', SPD10_ORDINACE_TEXT_DOMAIN),
        'edit_item'         => __('Upravit typ', SPD10_ORDINACE_TEXT_DOMAIN),
        'view_item'         => __('Zobrazit typ', SPD10_ORDINACE_TEXT_DOMAIN),
        'update_item'       => __('Aktualizovat typ', SPD10_ORDINACE_TEXT_DOMAIN),
        'add_new_item'      => __('Přidat nový typ', SPD10_ORDINACE_TEXT_DOMAIN),
        'new_item_name'     => __('Název nového typu', SPD10_ORDINACE_TEXT_DOMAIN),
        'search_items'      => __('Hledat typy', SPD10_ORDINACE_TEXT_DOMAIN),
        'not_found'         => __('Žádné typy nenalezeny', SPD10_ORDINACE_TEXT_DOMAIN),
    );

    $args = array(
        'labels'            => $labels,
        'public'            => true,
        'publicly_queryable' => true,
        'show_ui'           => true,
        'show_in_menu'      => true,
        'show_in_nav_menus' => true,
        'show_tagcloud'     => false,
        'show_in_quick_edit' => true,
        'show_admin_column' => true,
        'show_in_rest'      => true,
        'hierarchical'      => false,
        'query_var'         => true,
        'rewrite'           => array(
            'slug'         => spd10_ordinace_get_option('taxonomy_slug', 'typ-ordinace'),
            'with_front'   => false,
            'hierarchical' => false,
        ),
        'capabilities'      => array(
            'manage_terms' => 'manage_categories',
            'edit_terms'   => 'manage_categories',
            'delete_terms' => 'manage_categories',
            'assign_terms' => 'edit_posts',
        ),
    );

    register_taxonomy('ordinace_typ', array('ordinace'), $args);
}

/**
 * Register ordinace post type
 */
function spd10_ordinace_register_ordinace_post_type() {
    $labels = array(
        'name'               => __('Ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'singular_name'      => __('Ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'menu_name'          => __('Ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'name_admin_bar'     => __('Ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'add_new'            => __('Přidat novou', SPD10_ORDINACE_TEXT_DOMAIN),
        'add_new_item'       => __('Přidat novou ordinaci', SPD10_ORDINACE_TEXT_DOMAIN),
        'new_item'           => __('Nová ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'edit_item'          => __('Upravit ordinaci', SPD10_ORDINACE_TEXT_DOMAIN),
        'view_item'          => __('Zobrazit ordinaci', SPD10_ORDINACE_TEXT_DOMAIN),
        'view_items'         => __('Zobrazit ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'all_items'          => __('Všechny ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'search_items'       => __('Hledat ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'parent_item_colon'  => __('Nadřazená ordinace:', SPD10_ORDINACE_TEXT_DOMAIN),
        'not_found'          => __('Žádné ordinace nenalezeny.', SPD10_ORDINACE_TEXT_DOMAIN),
        'not_found_in_trash' => __('Žádné ordinace v koši.', SPD10_ORDINACE_TEXT_DOMAIN),
        'featured_image'     => __('Obrázek ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'set_featured_image' => __('Nastavit obrázek ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'remove_featured_image' => __('Odstranit obrázek ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'use_featured_image' => __('Použít jako obrázek ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'archives'           => __('Archiv ordinací', SPD10_ORDINACE_TEXT_DOMAIN),
        'insert_into_item'   => __('Vložit do ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
        'uploaded_to_this_item' => __('Nahráno k této ordinaci', SPD10_ORDINACE_TEXT_DOMAIN),
        'filter_items_list'  => __('Filtrovat seznam ordinací', SPD10_ORDINACE_TEXT_DOMAIN),
        'items_list_navigation' => __('Navigace seznamu ordinací', SPD10_ORDINACE_TEXT_DOMAIN),
        'items_list'         => __('Seznam ordinací', SPD10_ORDINACE_TEXT_DOMAIN),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'show_in_nav_menus'  => true,
        'show_in_admin_bar'  => true,
        'show_in_rest'       => true,
        'query_var'          => true,
        'rewrite'            => array(
            'slug'       => get_option('spd10_ordinace_post_type_slug', spd10_ordinace_get_option('post_type_slug', 'ordinace')),
            'with_front' => false,
        ),
        'capability_type'    => 'post',
        'map_meta_cap'       => true,
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 25,
        'menu_icon'          => 'dashicons-building',
        'supports'           => array(
            'title',
            'editor',
            'thumbnail',
            'excerpt',
            'custom-fields',
            'revisions',
        ),
        'taxonomies'         => array('ordinace_typ'),
        'can_export'         => true,
        'delete_with_user'   => false,
    );

    register_post_type('ordinace', $args);
}

/**
 * Add default taxonomy terms
 */
function spd10_ordinace_add_default_terms() {
    // Only add terms if they don't exist
    $terms = array(
        'praktik-dospeli' => __('Praktičtí lékaři pro dospělé', SPD10_ORDINACE_TEXT_DOMAIN),
        'pediatr'         => __('Praktičtí lékaři pro děti a dorost', SPD10_ORDINACE_TEXT_DOMAIN),
    );

    foreach ($terms as $slug => $name) {
        if (!term_exists($slug, 'ordinace_typ')) {
            wp_insert_term($name, 'ordinace_typ', array(
                'slug' => $slug,
            ));
        }
    }
}

/**
 * Flush rewrite rules on plugin activation
 */
function spd10_ordinace_flush_rewrite_rules() {
    spd10_ordinace_register_post_types();
    flush_rewrite_rules();
}
