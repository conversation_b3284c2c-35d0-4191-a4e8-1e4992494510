<?php
/**
 * Post Types Registration for Dětské skupiny plugin
 * 
 * This file contains the post type registration functions for the original
 * Dětské skupiny plugin to prevent fatal errors.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register post types for Dětské skupiny plugin
 */
function ds_register_post_types() {
    // Register provozovatel post type
    ds_register_provozovatel_post_type();
    
    // Register ds (dětské skupiny) post type
    ds_register_ds_post_type();
    
    // Register taxonomies
    ds_register_taxonomies();
}

/**
 * Register provozovatel post type
 */
function ds_register_provozovatel_post_type() {
    $labels = array(
        'name'               => 'Provozovatelé',
        'singular_name'      => 'Provozovatel',
        'menu_name'          => 'Provozovatelé',
        'name_admin_bar'     => 'Provozovatel',
        'add_new'            => 'Přidat nového',
        'add_new_item'       => 'Přidat nového provozovatele',
        'new_item'           => 'Nový provozovatel',
        'edit_item'          => 'Upravit provozovatele',
        'view_item'          => 'Zobrazit provozovatele',
        'all_items'          => 'Všichni provozovatelé',
        'search_items'       => 'Hledat provozovatele',
        'not_found'          => 'Žádní provozovatelé nenalezeni.',
        'not_found_in_trash' => 'Žádní provozovatelé v koši.',
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'provozovatel'),
        'capability_type'    => array('provozovatel', 'provozovatels'),
        'map_meta_cap'       => true,
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 20,
        'menu_icon'          => 'dashicons-businessman',
        'supports'           => array('title', 'editor', 'thumbnail'),
    );

    register_post_type('provozovatel', $args);
}

/**
 * Register ds (dětské skupiny) post type
 */
function ds_register_ds_post_type() {
    $labels = array(
        'name'               => 'Dětské skupiny',
        'singular_name'      => 'Dětská skupina',
        'menu_name'          => 'Dětské skupiny',
        'name_admin_bar'     => 'Dětská skupina',
        'add_new'            => 'Přidat novou',
        'add_new_item'       => 'Přidat novou dětskou skupinu',
        'new_item'           => 'Nová dětská skupina',
        'edit_item'          => 'Upravit dětskou skupinu',
        'view_item'          => 'Zobrazit dětskou skupinu',
        'all_items'          => 'Všechny dětské skupiny',
        'search_items'       => 'Hledat dětské skupiny',
        'not_found'          => 'Žádné dětské skupiny nenalezeny.',
        'not_found_in_trash' => 'Žádné dětské skupiny v koši.',
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'detska-skupina'),
        'capability_type'    => array('ds', 'dss'),
        'map_meta_cap'       => true,
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 21,
        'menu_icon'          => 'dashicons-groups',
        'supports'           => array('title', 'editor', 'thumbnail'),
    );

    register_post_type('ds', $args);
}

/**
 * Register taxonomies for ds post type
 */
function ds_register_taxonomies() {
    // Register čtvrť taxonomy
    $labels = array(
        'name'              => 'Čtvrti',
        'singular_name'     => 'Čtvrť',
        'search_items'      => 'Hledat čtvrti',
        'all_items'         => 'Všechny čtvrti',
        'edit_item'         => 'Upravit čtvrť',
        'update_item'       => 'Aktualizovat čtvrť',
        'add_new_item'      => 'Přidat novou čtvrť',
        'new_item_name'     => 'Název nové čtvrti',
        'menu_name'         => 'Čtvrti',
    );

    $args = array(
        'hierarchical'      => false,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'ctvrt'),
    );

    register_taxonomy('ds_ctvrt', array('ds'), $args);
}
