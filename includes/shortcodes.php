<?php
/**
 * Shortcodes for Ordinace Plugin
 *
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get illustration for ordinace type (shortcode version)
 */
function spd10_ordinace_get_illustration($type) {
    $illustrations = array(
        'pediatr' => 'data:image/svg+xml;base64,' . base64_encode('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#e0f2fe"/><stop offset="100%" style="stop-color:#b3e5fc"/></linearGradient></defs><rect width="200" height="200" fill="url(#bg)"/><circle cx="100" cy="80" r="25" fill="#ffb74d"/><rect x="85" y="105" width="30" height="40" rx="15" fill="#4fc3f7"/><rect x="75" y="125" width="15" height="30" rx="7" fill="#ffb74d"/><rect x="110" y="125" width="15" height="30" rx="7" fill="#ffb74d"/><circle cx="92" cy="75" r="3" fill="#333"/><circle cx="108" cy="75" r="3" fill="#333"/><path d="M95 85 Q100 90 105 85" stroke="#333" stroke-width="2" fill="none"/><rect x="40" y="160" width="120" height="8" rx="4" fill="#4caf50"/><text x="100" y="185" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Pediatrie</text></svg>'),
        'prakticky-lekar' => 'data:image/svg+xml;base64,' . base64_encode('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#f3e5f5"/><stop offset="100%" style="stop-color:#e1bee7"/></linearGradient></defs><rect width="200" height="200" fill="url(#bg)"/><circle cx="100" cy="70" r="25" fill="#ffb74d"/><rect x="85" y="95" width="30" height="50" rx="15" fill="#2196f3"/><rect x="75" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><rect x="110" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><circle cx="92" cy="65" r="3" fill="#333"/><circle cx="108" cy="65" r="3" fill="#333"/><path d="M95 75 Q100 80 105 75" stroke="#333" stroke-width="2" fill="none"/><rect x="90" y="50" width="20" height="4" rx="2" fill="#fff"/><rect x="40" y="170" width="120" height="8" rx="4" fill="#9c27b0"/><text x="100" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Praktický lékař</text></svg>'),
        'general' => 'data:image/svg+xml;base64,' . base64_encode('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#e8f5e8"/><stop offset="100%" style="stop-color:#c8e6c9"/></linearGradient></defs><rect width="200" height="200" fill="url(#bg)"/><circle cx="100" cy="70" r="25" fill="#ffb74d"/><rect x="85" y="95" width="30" height="50" rx="15" fill="#4caf50"/><rect x="75" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><rect x="110" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><circle cx="92" cy="65" r="3" fill="#333"/><circle cx="108" cy="65" r="3" fill="#333"/><path d="M95 75 Q100 80 105 75" stroke="#333" stroke-width="2" fill="none"/><rect x="95" y="105" width="10" height="2" fill="#fff"/><rect x="99" y="101" width="2" height="10" fill="#fff"/><rect x="40" y="170" width="120" height="8" rx="4" fill="#4caf50"/><text x="100" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Ordinace</text></svg>')
    );

    return isset($illustrations[$type]) ? $illustrations[$type] : $illustrations['general'];
}

/**
 * Professional Ordinace list shortcode
 * Usage: [ordinace_list typ="pediatr" per_page="10" style="cards" show_filters="true" search=""]
 */
function spd10_ordinace_list_shortcode($atts) {
    $atts = shortcode_atts(array(
        'typ' => '',
        'per_page' => spd10_ordinace_get_option('posts_per_page', 10),
        'ctvrt' => '',
        'style' => 'cards', // cards or simple
        'show_filters' => 'false', // true or false
        'search' => '', // text search
    ), $atts);

    // Process filters from URL if show_filters is enabled
    if ($atts['show_filters'] === 'true') {
        $url_typ = isset($_GET['typ']) ? sanitize_text_field($_GET['typ']) : $atts['typ'];
        $url_ctvrt = isset($_GET['ctvrt']) ? sanitize_text_field($_GET['ctvrt']) : $atts['ctvrt'];
        $url_search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : $atts['search'];
    } else {
        $url_typ = $atts['typ'];
        $url_ctvrt = $atts['ctvrt'];
        $url_search = $atts['search'];
    }

    $args = array(
        'post_type' => 'ordinace',
        'posts_per_page' => intval($atts['per_page']),
        'post_status' => 'publish',
    );

    // Build meta_query array for custom field searches
    $meta_query = array();

    // Add text search with custom fields and title (similar to archive)
    if (!empty($url_search)) {
        // Add custom search functionality
        add_filter('posts_where', function($where, $wp_query) use ($url_search) {
            global $wpdb;
            if ($wp_query->get('post_type') === 'ordinace' && !empty($url_search)) {
                $search_term = '%' . $wpdb->esc_like($url_search) . '%';
                $where .= $wpdb->prepare(" OR {$wpdb->posts}.post_title LIKE %s", $search_term);
            }
            return $where;
        }, 10, 2);

        // Search in custom fields
        $meta_query[] = array(
            'relation' => 'OR',
            array(
                'key' => '_spd10_ordinace_doctors_names',
                'value' => $url_search,
                'compare' => 'LIKE'
            ),
            array(
                'key' => '_spd10_ordinace_address',
                'value' => $url_search,
                'compare' => 'LIKE'
            ),
            array(
                'key' => '_spd10_ordinace_organization',
                'value' => $url_search,
                'compare' => 'LIKE'
            ),
            array(
                'key' => '_spd10_ordinace_quarter',
                'value' => $url_search,
                'compare' => 'LIKE'
            )
        );
    }

    // Add quarter filter
    if (!empty($url_ctvrt)) {
        $meta_query[] = array(
            'key' => '_spd10_ordinace_quarter',
            'value' => $url_ctvrt,
            'compare' => 'LIKE',
        );
    }

    // Set meta_query if we have any conditions
    if (!empty($meta_query)) {
        if (count($meta_query) > 1) {
            $meta_query['relation'] = 'AND';
        }
        $args['meta_query'] = $meta_query;
    }

    // Add taxonomy filter
    if (!empty($url_typ)) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'ordinace_typ',
                'field' => 'slug',
                'terms' => $url_typ,
            ),
        );
    }

    $query = new WP_Query($args);

    // Remove the search filter to avoid affecting other queries
    if (!empty($url_search)) {
        remove_all_filters('posts_where');
    }

    ob_start();

    // Show filters if enabled
    if ($atts['show_filters'] === 'true') {
        spd10_ordinace_render_shortcode_filters($url_typ, $url_ctvrt, $url_search);
    }

    if ($query->have_posts()) {
        $container_class = $atts['style'] === 'cards' ? 'spd10-ordinace-grid' : 'spd10-ordinace-list';
        echo '<div class="' . esc_attr($container_class) . '">';

        $index = 0;
        while ($query->have_posts()) {
            $query->the_post();
            $index++;

            // Get meta data
            $doctors_names = get_post_meta(get_the_ID(), '_spd10_ordinace_doctors_names', true);
            $organization = get_post_meta(get_the_ID(), '_spd10_ordinace_organization', true);
            $quarter = get_post_meta(get_the_ID(), '_spd10_ordinace_quarter', true);
            $address = get_post_meta(get_the_ID(), '_spd10_ordinace_address', true);
            $phone = get_post_meta(get_the_ID(), '_spd10_ordinace_phone', true);
            $email = get_post_meta(get_the_ID(), '_spd10_ordinace_email', true);

            // Get ordinace type for illustration
            $terms = get_the_terms(get_the_ID(), 'ordinace_typ');
            $ordinace_type = $terms && !is_wp_error($terms) ? $terms[0]->slug : 'general';

            if ($atts['style'] === 'cards') {
                // Professional card style
                echo '<article class="spd10-ordinace-card-item" style="animation-delay: ' . ($index * 0.1) . 's">';
                echo '<div class="spd10-ordinace-card-image">';
                echo '<img src="' . spd10_ordinace_get_illustration($ordinace_type) . '" alt="' . esc_attr($ordinace_type) . '" class="spd10-ordinace-card-illustration">';
                if ($terms && !is_wp_error($terms)) {
                    echo '<div class="spd10-ordinace-card-badge">' . esc_html($terms[0]->name) . '</div>';
                }
                echo '</div>';

                echo '<div class="spd10-ordinace-card-content">';
                echo '<h2 class="spd10-ordinace-card-title"><a href="' . get_permalink() . '">' . get_the_title() . '</a></h2>';

                if (!empty($quarter)) {
                    echo '<div class="spd10-ordinace-card-location">';
                    echo '<svg class="spd10-ordinace-card-icon" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>';
                    echo esc_html($quarter);
                    echo '</div>';
                }

                if (!empty($doctors_names)) {
                    echo '<div class="spd10-ordinace-card-doctors">';
                    echo '<svg class="spd10-ordinace-card-icon" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z"/></svg>';
                    echo '<div class="spd10-ordinace-card-doctors-list">';
                    $doctors = array_filter(array_map('trim', explode("\n", $doctors_names)));
                    $first_two = array_slice($doctors, 0, 2);
                    foreach ($first_two as $name) {
                        echo '<span class="spd10-ordinace-card-doctor">' . esc_html($name) . '</span>';
                    }
                    if (count($doctors) > 2) {
                        echo '<span class="spd10-ordinace-card-more">+' . (count($doctors) - 2) . ' dalších</span>';
                    }
                    echo '</div></div>';
                }

                echo '<div class="spd10-ordinace-card-contact">';
                if (!empty($phone)) {
                    echo '<div class="spd10-ordinace-card-contact-item">';
                    echo '<svg class="spd10-ordinace-card-icon" viewBox="0 0 24 24" fill="currentColor"><path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/></svg>';
                    echo '<a href="tel:' . esc_attr(preg_replace('/\s+/', '', $phone)) . '">' . esc_html($phone) . '</a>';
                    echo '</div>';
                }
                if (!empty($email)) {
                    echo '<div class="spd10-ordinace-card-contact-item">';
                    echo '<svg class="spd10-ordinace-card-icon" viewBox="0 0 24 24" fill="currentColor"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/></svg>';
                    echo '<a href="mailto:' . esc_attr($email) . '">' . esc_html($email) . '</a>';
                    echo '</div>';
                }
                echo '</div>';

                echo '<div class="spd10-ordinace-card-footer">';
                echo '<a href="' . get_permalink() . '" class="spd10-ordinace-card-button">';
                echo __('Více informací', SPD10_ORDINACE_TEXT_DOMAIN);
                echo '<svg class="spd10-ordinace-card-arrow" viewBox="0 0 24 24" fill="currentColor"><path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/></svg>';
                echo '</a>';
                echo '</div>';

                echo '</div></article>';
            } else {
                // Simple list style (original)
                echo '<div class="spd10-ordinace-item">';
                echo '<div class="spd10-ordinace-item-content">';
                echo '<h3><a href="' . get_permalink() . '">' . get_the_title() . '</a></h3>';

                if (!empty($doctors_names)) {
                    $names_array = explode("\n", $doctors_names);
                    echo '<div class="spd10-ordinace-doctors">';
                    foreach ($names_array as $name) {
                        if (trim($name)) {
                            echo '<div class="spd10-ordinace-doctor-name">' . esc_html(trim($name)) . '</div>';
                        }
                    }
                    echo '</div>';
                }

                if (!empty($quarter)) {
                    echo '<p class="spd10-ordinace-quarter"><strong>' . __('Čtvrť:', SPD10_ORDINACE_TEXT_DOMAIN) . '</strong> ' . esc_html($quarter) . '</p>';
                }

                if (!empty($organization)) {
                    echo '<p class="spd10-ordinace-organization"><strong>' . __('Organizace:', SPD10_ORDINACE_TEXT_DOMAIN) . '</strong> ' . esc_html($organization) . '</p>';
                }

                echo '<a href="' . get_permalink() . '" class="spd10-ordinace-more-info">' . __('Více informací', SPD10_ORDINACE_TEXT_DOMAIN) . '</a>';
                echo '</div></div>';
            }
        }
        echo '</div>';
    } else {
        echo '<div class="spd10-ordinace-no-results">';
        echo '<div class="spd10-ordinace-no-results-icon">';
        echo '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>';
        echo '</div>';
        echo '<h3>' . __('Žádné ordinace nebyly nalezeny', SPD10_ORDINACE_TEXT_DOMAIN) . '</h3>';
        echo '<p>' . __('Zkuste změnit filtry nebo vyhledávací kritéria.', SPD10_ORDINACE_TEXT_DOMAIN) . '</p>';
        echo '</div>';
    }

    wp_reset_postdata();

    return ob_get_clean();
}

/**
 * Basic ordinace map shortcode (placeholder for milestone 5)
 * Usage: [ordinace_map typ="*"]
 */
function spd10_ordinace_map_shortcode($atts) {
    $atts = shortcode_atts(array(
        'typ' => 'all',
        'ctvrt' => 'all',
        'height' => '400px',
        'show_filters' => 'true',
        'show_legend' => 'true',
        'zoom' => '',
    ), $atts);

    // Load map provider
    require_once SPD10_ORDINACE_PLUGIN_DIR . 'includes/class-map-provider.php';
    $map_provider = new SPD10_Ordinace_Map_Provider();

    // Enqueue map assets
    $map_provider->enqueue_map_assets(false);

    // Generate unique map ID
    static $map_counter = 0;
    $map_counter++;
    $map_id = 'spd10-ordinace-map-' . $map_counter;

    // Get marker colors for legend
    $marker_colors = array(
        'pediatr' => spd10_ordinace_get_option('marker_color_pediatr', '#4CAF50'),
        'praktik-dospeli' => spd10_ordinace_get_option('marker_color_praktik', '#2196F3'),
    );

    // Start output buffering
    ob_start();
    ?>

    <div class="spd10-ordinace-map-container" style="height: <?php echo esc_attr($atts['height']); ?>;" id="<?php echo esc_attr($map_id); ?>-container">
        <?php if ($atts['show_filters'] === 'true'): ?>
        <div class="spd10-ordinace-map-controls">
            <div class="spd10-ordinace-map-filters">
                <div class="spd10-ordinace-map-filter-group">
                    <label for="<?php echo esc_attr($map_id); ?>-type-filter"><?php _e('Typ ordinace:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
                    <select id="<?php echo esc_attr($map_id); ?>-type-filter" class="map-type-filter">
                        <option value="all" <?php selected($atts['typ'], 'all'); ?>><?php _e('Všechny typy', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                        <option value="pediatr" <?php selected($atts['typ'], 'pediatr'); ?>><?php _e('Pediatři', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                        <option value="praktik-dospeli" <?php selected($atts['typ'], 'praktik-dospeli'); ?>><?php _e('Praktičtí lékaři', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                    </select>
                </div>

                <div class="spd10-ordinace-map-filter-group">
                    <label for="<?php echo esc_attr($map_id); ?>-quarter-filter"><?php _e('Čtvrť:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
                    <select id="<?php echo esc_attr($map_id); ?>-quarter-filter" class="map-quarter-filter">
                        <option value="all" <?php selected($atts['ctvrt'], 'all'); ?>><?php _e('Všechny čtvrti', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                        <?php
                        // Get unique quarters from ordinace posts
                        $quarters = spd10_ordinace_get_unique_quarters();
                        foreach ($quarters as $quarter) {
                            if (!empty($quarter)) {
                                printf(
                                    '<option value="%s" %s>%s</option>',
                                    esc_attr($quarter),
                                    selected($atts['ctvrt'], $quarter, false),
                                    esc_html($quarter)
                                );
                            }
                        }
                        ?>
                    </select>
                </div>

                <button type="button" class="map-reset-filters button"><?php _e('Resetovat filtry', SPD10_ORDINACE_TEXT_DOMAIN); ?></button>
            </div>

            <?php if ($atts['show_legend'] === 'true'): ?>
            <div class="spd10-ordinace-map-legend">
                <div class="spd10-ordinace-legend-item">
                    <div class="spd10-ordinace-legend-marker" style="background-color: <?php echo esc_attr($marker_colors['pediatr']); ?>"></div>
                    <span><?php _e('Pediatři', SPD10_ORDINACE_TEXT_DOMAIN); ?></span>
                </div>
                <div class="spd10-ordinace-legend-item">
                    <div class="spd10-ordinace-legend-marker" style="background-color: <?php echo esc_attr($marker_colors['praktik-dospeli']); ?>"></div>
                    <span><?php _e('Praktičtí lékaři', SPD10_ORDINACE_TEXT_DOMAIN); ?></span>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <?php echo $map_provider->generate_map_html(array(
            'id' => $map_id,
            'height' => '100%', // Map fills the container
            'class' => 'spd10-ordinace-map shortcode-map',
        )); ?>
    </div>

    <script>
    // Fullscreen functionality
    function toggleMapFullscreen(mapId) {
        var mapContainer = document.getElementById(mapId).closest('.spd10-ordinace-map-container');
        var isFullscreen = mapContainer.classList.contains('spd10-ordinace-map-fullscreen');

        if (isFullscreen) {
            exitMapFullscreen(mapContainer);
        } else {
            enterMapFullscreen(mapContainer);
        }
    }

    function enterMapFullscreen(mapContainer) {
        mapContainer.classList.add('spd10-ordinace-map-fullscreen');
        document.body.classList.add('spd10-map-fullscreen-active');

        // Add exit button
        var exitBtn = document.createElement('button');
        exitBtn.className = 'spd10-ordinace-map-exit-fullscreen-btn';
        exitBtn.innerHTML = '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"/></svg><?php _e('Ukončit celou obrazovku', SPD10_ORDINACE_TEXT_DOMAIN); ?>';
        exitBtn.onclick = function() { exitMapFullscreen(mapContainer); };

        var controls = mapContainer.querySelector('.spd10-ordinace-map-fullscreen-controls');
        controls.appendChild(exitBtn);

        // Hide fullscreen button
        var fullscreenBtn = controls.querySelector('.spd10-ordinace-map-fullscreen-btn');
        if (fullscreenBtn) fullscreenBtn.style.display = 'none';

        // Trigger map resize
        setTimeout(function() {
            if (window.mapInstance && window.mapInstance.invalidateSize) {
                window.mapInstance.invalidateSize();
            } else if (window.mapInstance && window.mapInstance.getDiv) {
                google.maps.event.trigger(window.mapInstance, 'resize');
            } else if (window.mapInstance && window.mapInstance.resize) {
                window.mapInstance.resize();
            }
        }, 100);
    }

    function exitMapFullscreen(mapContainer) {
        mapContainer.classList.remove('spd10-ordinace-map-fullscreen');
        document.body.classList.remove('spd10-map-fullscreen-active');

        // Remove exit button
        var exitBtn = mapContainer.querySelector('.spd10-ordinace-map-exit-fullscreen-btn');
        if (exitBtn) exitBtn.remove();

        // Show fullscreen button
        var fullscreenBtn = mapContainer.querySelector('.spd10-ordinace-map-fullscreen-btn');
        if (fullscreenBtn) fullscreenBtn.style.display = 'flex';

        // Trigger map resize
        setTimeout(function() {
            if (window.mapInstance && window.mapInstance.invalidateSize) {
                window.mapInstance.invalidateSize();
            } else if (window.mapInstance && window.mapInstance.getDiv) {
                google.maps.event.trigger(window.mapInstance, 'resize');
            } else if (window.mapInstance && window.mapInstance.resize) {
                window.mapInstance.resize();
            }
        }, 100);
    }

    jQuery(document).ready(function($) {
        var mapId = '<?php echo esc_js($map_id); ?>';
        var currentTypeFilter = '<?php echo esc_js($atts['typ']); ?>';
        var currentQuarterFilter = '<?php echo esc_js($atts['ctvrt']); ?>';

        // Height is now set via CSS on container

        // Initialize map with filters - wait for map to be ready
        setTimeout(function() {
            if (typeof loadOrdinaceData === 'function') {
                loadOrdinaceData(currentTypeFilter, currentQuarterFilter);
            } else if (window.spd10OrdinaceMapFilters && window.spd10OrdinaceMapFilters.loadOrdinaceData) {
                window.spd10OrdinaceMapFilters.loadOrdinaceData(currentTypeFilter, currentQuarterFilter);
            }
        }, 500);

        // Handle filter changes
        $('#' + mapId + '-type-filter').on('change', function() {
            currentTypeFilter = $(this).val();
            console.log('Type filter changed to:', currentTypeFilter);
            if (window.spd10OrdinaceMapFilters) {
                window.spd10OrdinaceMapFilters.setFilters(currentTypeFilter, currentQuarterFilter);
            } else {
                console.error('spd10OrdinaceMapFilters not available');
            }
        });

        $('#' + mapId + '-quarter-filter').on('change', function() {
            currentQuarterFilter = $(this).val();
            console.log('Quarter filter changed to:', currentQuarterFilter);
            if (window.spd10OrdinaceMapFilters) {
                window.spd10OrdinaceMapFilters.setFilters(currentTypeFilter, currentQuarterFilter);
            } else {
                console.error('spd10OrdinaceMapFilters not available');
            }
        });

        // Handle reset filters
        $('.map-reset-filters').on('click', function() {
            $('#' + mapId + '-type-filter').val('all');
            $('#' + mapId + '-quarter-filter').val('all');
            currentTypeFilter = 'all';
            currentQuarterFilter = 'all';
            if (window.spd10OrdinaceMapFilters) {
                window.spd10OrdinaceMapFilters.resetFilters();
            }
        });
    });
    </script>

    <?php
    return ob_get_clean();
}

/**
 * Get unique quarters from ordinace posts
 */
function spd10_ordinace_get_unique_quarters() {
    global $wpdb;

    $quarters = $wpdb->get_col($wpdb->prepare(
        "SELECT DISTINCT meta_value
         FROM {$wpdb->postmeta} pm
         INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
         WHERE pm.meta_key = %s
         AND p.post_type = %s
         AND p.post_status = %s
         AND pm.meta_value != ''
         ORDER BY pm.meta_value ASC",
        '_spd10_ordinace_quarter',
        'ordinace',
        'publish'
    ));

    return $quarters ?: array();
}

/**
 * Render filters for shortcode
 */
function spd10_ordinace_render_shortcode_filters($typ, $ctvrt, $search) {
    // Get all unique quarters for filter dropdown
    $quarters = spd10_ordinace_get_unique_quarters();

    ?>
    <!-- Shortcode Filters with embedded styles -->
    <style>
    .spd10-ordinace-filters-container {
        margin-bottom: 40px;
    }
    .spd10-ordinace-filters {
        background: white;
        border-radius: 16px;
        padding: 30px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid rgba(0,0,0,0.05);
    }
    .spd10-ordinace-filter-group {
        display: grid;
        grid-template-columns: 1fr 1fr auto;
        gap: 25px;
        align-items: end;
    }
    .spd10-ordinace-filter-item label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }
    .spd10-ordinace-filter-icon {
        width: 18px;
        height: 18px;
        color: #667eea;
    }
    .spd10-ordinace-select,
    .spd10-ordinace-input {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }
    .spd10-ordinace-select:focus,
    .spd10-ordinace-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    .spd10-ordinace-filter-actions {
        display: flex;
        gap: 12px;
        flex-direction: column;
    }
    .spd10-ordinace-filter-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.9rem;
    }
    .spd10-ordinace-filter-button-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }
    .spd10-ordinace-filter-button-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    .spd10-ordinace-filter-button-secondary {
        background: #f7fafc;
        color: #4a5568;
        border: 2px solid #e2e8f0;
    }
    .spd10-ordinace-filter-button-secondary:hover {
        background: #edf2f7;
        border-color: #cbd5e0;
    }
    .spd10-ordinace-button-icon {
        width: 16px;
        height: 16px;
    }

    /* Responsive shortcode filters */
    @media (max-width: 1024px) {
        .spd10-ordinace-filter-group {
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .spd10-ordinace-filter-actions {
            grid-column: 1 / -1;
            flex-direction: row;
            justify-content: center;
            margin-top: 15px;
        }
    }
    @media (max-width: 767px) {
        .spd10-ordinace-filter-group {
            grid-template-columns: 1fr;
            gap: 15px;
        }
        .spd10-ordinace-filters {
            padding: 20px;
        }
        .spd10-ordinace-filter-actions {
            flex-direction: column;
            gap: 10px;
        }
    }
    </style>

    <div class="spd10-ordinace-filters-container">
        <form method="get" class="spd10-ordinace-filters">
            <div class="spd10-ordinace-filter-group">
                <div class="spd10-ordinace-filter-item">
                    <label for="shortcode-search-filter">
                        <svg class="spd10-ordinace-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                        <?php _e('Vyhledávání', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </label>
                    <input type="text" name="search" id="shortcode-search-filter" value="<?php echo esc_attr($search); ?>" placeholder="<?php _e('Hledat podle názvu, lékaře, adresy...', SPD10_ORDINACE_TEXT_DOMAIN); ?>" class="spd10-ordinace-input" />
                </div>

                <div class="spd10-ordinace-filter-item">
                    <label for="shortcode-typ-filter">
                        <svg class="spd10-ordinace-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z"/>
                        </svg>
                        <?php _e('Typ ordinace', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </label>
                    <select name="typ" id="shortcode-typ-filter" class="spd10-ordinace-select">
                        <option value=""><?php _e('Všechny typy', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                        <option value="pediatr" <?php selected($typ, 'pediatr'); ?>><?php _e('Pediatr', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                        <option value="praktik-dospeli" <?php selected($typ, 'praktik-dospeli'); ?>><?php _e('Praktický lékař', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                    </select>
                </div>

                <div class="spd10-ordinace-filter-item">
                    <label for="shortcode-ctvrt-filter">
                        <svg class="spd10-ordinace-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <?php _e('Čtvrť', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </label>
                    <select name="ctvrt" id="shortcode-ctvrt-filter" class="spd10-ordinace-select">
                        <option value=""><?php _e('Všechny čtvrti', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                        <?php foreach ($quarters as $quarter) : ?>
                            <option value="<?php echo esc_attr($quarter); ?>" <?php selected($ctvrt, $quarter); ?>>
                                <?php echo esc_html($quarter); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="spd10-ordinace-filter-actions">
                    <button type="submit" class="spd10-ordinace-filter-button spd10-ordinace-filter-button-primary">
                        <svg class="spd10-ordinace-button-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                        <?php _e('Filtrovat', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </button>
                    <?php if (!empty($typ) || !empty($ctvrt) || !empty($search)) : ?>
                        <a href="<?php echo esc_url(remove_query_arg(array('typ', 'ctvrt', 'search'))); ?>" class="spd10-ordinace-filter-button spd10-ordinace-filter-button-secondary">
                            <svg class="spd10-ordinace-button-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                            <?php _e('Zrušit filtry', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>
    <?php
}

// Register shortcodes
add_shortcode('ordinace_list', 'spd10_ordinace_list_shortcode');
add_shortcode('ordinace_map', 'spd10_ordinace_map_shortcode');
