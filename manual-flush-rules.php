<?php
/**
 * Manual flush rewrite rules script
 * 
 * This script manually flushes WordPress rewrite rules and registers
 * the GeoJSON endpoint for the ordinace plugin.
 */

// Try to find WordPress
$wp_paths = [
    __DIR__ . '/wp-config.php',
    __DIR__ . '/../wp-config.php',
    __DIR__ . '/../../wp-config.php',
    __DIR__ . '/../../../wp-config.php',
];

$wp_found = false;
foreach ($wp_paths as $path) {
    if (file_exists($path)) {
        define('WP_USE_THEMES', false);
        require_once dirname($path) . '/wp-load.php';
        $wp_found = true;
        break;
    }
}

if (!$wp_found) {
    die("WordPress not found. Please run this script from the WordPress root directory.\n");
}

echo "=== SPD10 Ordinace Plugin - Manual Rewrite Rules Flush ===\n\n";

// Check if plugin is active
if (!function_exists('spd10_ordinace_get_option')) {
    die("SPD10 Ordinace plugin is not active!\n");
}

echo "✓ Plugin is active\n";

// Load GeoJSON cache class
if (!class_exists('SPD10_Ordinace_GeoJSON_Cache')) {
    require_once __DIR__ . '/includes/class-geojson-cache.php';
}

echo "✓ GeoJSON cache class loaded\n";

// Create instance and register rewrite rules
$geojson_cache = new SPD10_Ordinace_GeoJSON_Cache();
$geojson_cache->add_rewrite_rules();

echo "✓ Rewrite rules registered\n";

// Flush rewrite rules
flush_rewrite_rules(true);

echo "✓ Rewrite rules flushed\n";

// Test the rules
global $wp_rewrite;
$rules = $wp_rewrite->wp_rewrite_rules();

$found_rules = [];
foreach ($rules as $pattern => $replacement) {
    if (strpos($pattern, 'ordinace-geojson') !== false) {
        $found_rules[] = "$pattern → $replacement";
    }
}

if (!empty($found_rules)) {
    echo "✓ GeoJSON rewrite rules found:\n";
    foreach ($found_rules as $rule) {
        echo "  - $rule\n";
    }
} else {
    echo "✗ No GeoJSON rewrite rules found!\n";
}

// Test query vars
global $wp;
$query_vars = $wp->public_query_vars;

if (in_array('spd10_ordinace_geojson', $query_vars)) {
    echo "✓ spd10_ordinace_geojson query var registered\n";
} else {
    echo "✗ spd10_ordinace_geojson query var NOT registered\n";
}

if (in_array('ordinace_type', $query_vars)) {
    echo "✓ ordinace_type query var registered\n";
} else {
    echo "✗ ordinace_type query var NOT registered\n";
}

// Test endpoint URL
$test_url = home_url('ordinace-geojson');
echo "\nTest URL: $test_url\n";

echo "\n=== Manual flush completed ===\n";
echo "Now try accessing: $test_url\n";
?>
