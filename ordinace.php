<?php
/**
 * Plugin Name: Ordinace - Praktičtí lékaři Praha 10
 * Plugin URI: https://strategieprodesitku.cz/lekari
 * Description: Plugin pro správu databáze ordinací praktických lékařů a pediatrů na Praze 10. Kompatibilní s pluginem Dětské skupiny.
 * Version: 2.0.0
 * Author: Strategie pro Desítku
 * Text Domain: spd10-ordinace
 * Domain Path: /languages
 * Requires at least: 6.4
 * Requires PHP: 8.1
 * Network: false
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SPD10_ORDINACE_VERSION', '2.0.0');
define('SPD10_ORDINACE_PLUGIN_FILE', __FILE__);
define('SPD10_ORDINACE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('SPD10_ORDINACE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SPD10_ORDINACE_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('SPD10_ORDINACE_TEXT_DOMAIN', 'spd10-ordinace');

/**
 * Main plugin class
 */
class SPD10_Ordinace_Plugin {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->spd10_ordinace_init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function spd10_ordinace_init_hooks() {
        // Activation and deactivation hooks
        register_activation_hook(SPD10_ORDINACE_PLUGIN_FILE, array($this, 'spd10_ordinace_activate'));
        register_deactivation_hook(SPD10_ORDINACE_PLUGIN_FILE, array($this, 'spd10_ordinace_deactivate'));
        
        // Initialize plugin
        add_action('init', array($this, 'spd10_ordinace_init'));
        add_action('plugins_loaded', array($this, 'spd10_ordinace_load_textdomain'));
        
        // Admin initialization
        if (is_admin()) {
            add_action('admin_init', array($this, 'spd10_ordinace_admin_init'));
            add_action('admin_enqueue_scripts', array($this, 'spd10_ordinace_enqueue_admin_scripts'));

            // Add CSV Import menu
            add_action('admin_menu', array($this, 'add_csv_import_menu'), 999);
        }

        // Frontend styles
        add_action('wp_enqueue_scripts', array($this, 'spd10_ordinace_enqueue_frontend_styles'));
    }
    
    /**
     * Plugin activation
     */
    public function spd10_ordinace_activate() {
        // Load required files for activation
        $this->spd10_ordinace_load_dependencies();

        // Register post types and taxonomies
        if (function_exists('spd10_ordinace_register_post_types')) {
            spd10_ordinace_register_post_types();
        }

        // Initialize GeoJSON cache to register rewrite rules
        if (class_exists('SPD10_Ordinace_GeoJSON_Cache')) {
            $geojson_cache = new SPD10_Ordinace_GeoJSON_Cache();
            $geojson_cache->add_rewrite_rules();
        }

        // Set flag to flush rewrite rules on next load
        update_option('spd10_ordinace_flush_rewrite_rules', true);

        // Set default options
        $this->spd10_ordinace_set_default_options();

        // Create geocoding cache table
        if (class_exists('SPD10_Ordinace_Geocoding_Cache')) {
            $cache = new SPD10_Ordinace_Geocoding_Cache();
            $cache->create_table();
        }

        // Log activation
        error_log('SPD10 Ordinace Plugin activated');
    }
    
    /**
     * Plugin deactivation
     */
    public function spd10_ordinace_deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Log deactivation
        error_log('SPD10 Ordinace Plugin deactivated');
    }
    
    /**
     * Initialize plugin
     */
    public function spd10_ordinace_init() {
        // Load dependencies
        $this->spd10_ordinace_load_dependencies();

        // Initialize post types and taxonomies
        if (function_exists('spd10_ordinace_register_post_types')) {
            spd10_ordinace_register_post_types();
        }

        // Initialize GeoJSON cache (must be early for rewrite rules)
        if (class_exists('SPD10_Ordinace_GeoJSON_Cache')) {
            if (!isset($GLOBALS['spd10_ordinace_geojson_cache'])) {
                $GLOBALS['spd10_ordinace_geojson_cache'] = new SPD10_Ordinace_GeoJSON_Cache();
            }
        }

        // Initialize cron scheduler
        if (class_exists('SPD10_Ordinace_Cron_Scheduler')) {
            new SPD10_Ordinace_Cron_Scheduler();
        }

        // Initialize template loader
        if (class_exists('SPD10_Ordinace_Template_Loader')) {
            $template_loader = new SPD10_Ordinace_Template_Loader();
            $template_loader->init();
        }

        // Initialize CSV importer
        if (class_exists('SPD10_Ordinace_CSV_Importer')) {
            new SPD10_Ordinace_CSV_Importer();
        }

        // Initialize AJAX handlers
        if (class_exists('SPD10_Ordinace_Ajax_Handlers')) {
            new SPD10_Ordinace_Ajax_Handlers();
        }

        // Load test data functionality in development
        if (defined('WP_DEBUG') && WP_DEBUG) {
            require_once SPD10_ORDINACE_PLUGIN_DIR . 'test-data.php';
        }
    }
    
    /**
     * Load text domain for translations
     */
    public function spd10_ordinace_load_textdomain() {
        load_plugin_textdomain(
            SPD10_ORDINACE_TEXT_DOMAIN,
            false,
            dirname(SPD10_ORDINACE_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Admin initialization
     */
    public function spd10_ordinace_admin_init() {
        // Load admin-specific dependencies
        $this->spd10_ordinace_load_admin_dependencies();
    }
    
    /**
     * Load plugin dependencies
     */
    private function spd10_ordinace_load_dependencies() {
        // Core includes
        $includes = array(
            'includes/ordinace-post-types.php',
            'includes/meta-boxes.php',
            'includes/shortcodes.php',
            'includes/class-sheets-connector.php',
            'includes/class-column-mapper.php',
            'includes/class-importer.php',
            'includes/class-csv-importer.php',
            'includes/class-cron-scheduler.php',
            'includes/class-cli-commands.php',
            'includes/interface-geocoder.php',
            'includes/class-geocoder.php',
            'includes/class-geocoding-cache.php',
            'includes/class-map-provider.php',
            'includes/class-geojson-cache.php',
            'includes/class-ajax-handlers.php',
            'includes/class-template-loader.php',
        );
        
        foreach ($includes as $file) {
            $filepath = SPD10_ORDINACE_PLUGIN_DIR . $file;
            if (file_exists($filepath)) {
                require_once $filepath;
            }
        }
    }
    
    /**
     * Load admin dependencies
     */
    private function spd10_ordinace_load_admin_dependencies() {
        // Admin includes
        $admin_includes = array(
            'includes/admin-columns.php',
            'includes/admin-settings.php',
        );

        foreach ($admin_includes as $file) {
            $filepath = SPD10_ORDINACE_PLUGIN_DIR . $file;
            if (file_exists($filepath)) {
                require_once $filepath;
            }
        }
    }
    
    /**
     * Enqueue frontend styles
     */
    public function spd10_ordinace_enqueue_frontend_styles() {
        $should_load_assets = false;

        // Always load on ordinace pages
        if (is_singular('ordinace') || is_post_type_archive('ordinace')) {
            $should_load_assets = true;
        } else {
            // Check for shortcodes on other pages
            $post = get_post();
            if ($post && (
                has_shortcode($post->post_content, 'ordinace_list') ||
                has_shortcode($post->post_content, 'ordinace_map')
            )) {
                $should_load_assets = true;
            }
        }

        if ($should_load_assets) {
            // Load frontend CSS
            wp_enqueue_style(
                'spd10-ordinace-frontend',
                SPD10_ORDINACE_PLUGIN_URL . 'assets/css/spd10-ordinace-frontend.css',
                array(),
                SPD10_ORDINACE_VERSION
            );

            // Map assets are enqueued by shortcode itself to avoid conflicts
        }
    }

    /**
     * Enqueue map assets
     */
    private function spd10_ordinace_enqueue_map_assets() {
        $map_provider = new SPD10_Ordinace_Map_Provider();
        $map_provider->enqueue_map_assets(false);
    }

    /**
     * Enqueue admin scripts
     */
    public function spd10_ordinace_enqueue_admin_scripts($hook) {
        // Only load on ordinace edit pages
        if ($hook === 'post.php' || $hook === 'post-new.php') {
            global $post;
            if ($post && $post->post_type === 'ordinace') {
                $map_provider = new SPD10_Ordinace_Map_Provider();
                $map_provider->enqueue_map_assets(true);
            }
        }
    }

    /**
     * Set default plugin options
     */
    private function spd10_ordinace_set_default_options() {
        $defaults = array(
            'spd10_ordinace_google_sheet_id' => '',
            'spd10_ordinace_sheet_tab_praktici' => 'Praktičtí lékaři',
            'spd10_ordinace_sheet_tab_pediatri' => 'Pediatři',
            'spd10_ordinace_posts_per_page' => 10,
            'spd10_ordinace_geocoder_provider' => 'nominatim',
            'spd10_ordinace_map_provider' => 'leaflet',
            'spd10_ordinace_map_center_lat' => 50.0755,
            'spd10_ordinace_map_center_lng' => 14.4378,
            'spd10_ordinace_map_zoom' => 12,
            'spd10_ordinace_marker_color_pediatr' => '#2196F3',
            'spd10_ordinace_marker_color_praktik' => '#4CAF50',
        );

        foreach ($defaults as $option_name => $default_value) {
            if (false === get_option($option_name)) {
                add_option($option_name, $default_value);
            }
        }
    }

    /**
     * Add CSV Import menu
     */
    public function add_csv_import_menu() {
        // Try to add under Ordinace post type first
        if (post_type_exists('ordinace')) {
            add_submenu_page(
                'edit.php?post_type=ordinace',
                __('CSV Import', SPD10_ORDINACE_TEXT_DOMAIN),
                __('CSV Import', SPD10_ORDINACE_TEXT_DOMAIN),
                'manage_options',
                'spd10-ordinace-csv-import',
                array($this, 'render_csv_import_page')
            );
        } else {
            // Fallback: add under Tools menu
            add_submenu_page(
                'tools.php',
                __('CSV Import Ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
                __('CSV Import Ordinace', SPD10_ORDINACE_TEXT_DOMAIN),
                'manage_options',
                'spd10-ordinace-csv-import',
                array($this, 'render_csv_import_page')
            );
        }
    }

    /**
     * Render CSV Import page
     */
    public function render_csv_import_page() {
        // Load the CSV import page function from admin-settings.php
        if (function_exists('spd10_ordinace_render_csv_import_page')) {
            spd10_ordinace_render_csv_import_page();
        } else {
            echo '<div class="wrap">';
            echo '<h1>' . __('CSV Import', SPD10_ORDINACE_TEXT_DOMAIN) . '</h1>';
            echo '<p>' . __('CSV Import funkcionalita se načítá...', SPD10_ORDINACE_TEXT_DOMAIN) . '</p>';
            echo '<p><em>' . __('Pokud se stránka nenačte, zkontrolujte, zda je plugin správně aktivován.', SPD10_ORDINACE_TEXT_DOMAIN) . '</em></p>';
            echo '</div>';
        }
    }
}

/**
 * Initialize the plugin
 */
function spd10_ordinace_init_plugin() {
    return SPD10_Ordinace_Plugin::get_instance();
}

// Start the plugin
spd10_ordinace_init_plugin();

/**
 * Helper function to get plugin option with default
 */
function spd10_ordinace_get_option($option_name, $default = '') {
    return get_option('spd10_ordinace_' . $option_name, $default);
}

/**
 * Helper function to update plugin option
 */
function spd10_ordinace_update_option($option_name, $value) {
    return update_option('spd10_ordinace_' . $option_name, $value);
}

/**
 * Plugin activation hook
 */
function spd10_ordinace_activate() {
    // Set flag to flush rewrite rules on next load
    update_option('spd10_ordinace_flush_rewrite_rules', true);
}
register_activation_hook(__FILE__, 'spd10_ordinace_activate');

/**
 * Plugin deactivation hook
 */
function spd10_ordinace_deactivate() {
    // Flush rewrite rules on deactivation
    flush_rewrite_rules();
}
register_deactivation_hook(__FILE__, 'spd10_ordinace_deactivate');
