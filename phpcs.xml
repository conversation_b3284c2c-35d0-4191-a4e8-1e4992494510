<?xml version="1.0"?>
<ruleset name="SPD10 Ordinace Plugin">
    <description>WordPress Coding Standards for SPD10 Ordinace Plugin</description>

    <!-- What to scan -->
    <file>.</file>
    
    <!-- Exclude patterns -->
    <exclude-pattern>/vendor/</exclude-pattern>
    <exclude-pattern>/node_modules/</exclude-pattern>
    <exclude-pattern>/tests/</exclude-pattern>
    <exclude-pattern>/assets/</exclude-pattern>
    <exclude-pattern>/languages/</exclude-pattern>
    <exclude-pattern>*.min.js</exclude-pattern>
    <exclude-pattern>*.min.css</exclude-pattern>

    <!-- How to scan -->
    <arg value="sp"/> <!-- Show sniff and progress -->
    <arg name="basepath" value="./"/><!-- Strip the file paths down to the relevant bit -->
    <arg name="colors"/>
    <arg name="extensions" value="php"/>
    <arg name="parallel" value="8"/><!-- Enables parallel processing when available for faster results -->

    <!-- Rules: Check PHP version compatibility -->
    <config name="testVersion" value="8.1-"/>
    
    <!-- Rules: WordPress Coding Standards -->
    <rule ref="WordPress-Core">
        <!-- Allow short array syntax -->
        <exclude name="Generic.Arrays.DisallowShortArraySyntax"/>
        
        <!-- Allow multiple assignments -->
        <exclude name="Squiz.PHP.DisallowMultipleAssignments"/>
        
        <!-- Allow yoda conditions to be optional -->
        <exclude name="WordPress.PHP.YodaConditions.NotYoda"/>
    </rule>

    <rule ref="WordPress-Docs">
        <!-- Don't require documentation for every function -->
        <exclude name="Squiz.Commenting.FunctionComment.Missing"/>
        <exclude name="Squiz.Commenting.ClassComment.Missing"/>
        <exclude name="Squiz.Commenting.VariableComment.Missing"/>
    </rule>

    <rule ref="WordPress-Extra">
        <!-- Allow short ternary operator -->
        <exclude name="WordPress.PHP.DisallowShortTernary"/>
    </rule>

    <!-- Rules: Security -->
    <rule ref="WordPress.Security"/>

    <!-- Rules: Sanitization -->
    <rule ref="WordPress.Security.EscapeOutput"/>
    <rule ref="WordPress.Security.NonceVerification"/>
    <rule ref="WordPress.Security.ValidatedSanitizedInput"/>

    <!-- Rules: Database -->
    <rule ref="WordPress.DB.DirectDatabaseQuery">
        <properties>
            <property name="customCacheDeleteFunctions" type="array">
                <element value="wp_cache_delete"/>
                <element value="wp_cache_flush"/>
                <element value="delete_transient"/>
                <element value="delete_site_transient"/>
            </property>
        </properties>
    </rule>

    <!-- Rules: Performance -->
    <rule ref="WordPress.Performance.SlowDBQuery"/>

    <!-- Rules: Internationalization -->
    <rule ref="WordPress.WP.I18n">
        <properties>
            <property name="text_domain" type="array">
                <element value="spd10-ordinace"/>
            </property>
        </properties>
    </rule>

    <!-- Rules: Prefix -->
    <rule ref="WordPress.NamingConventions.PrefixAllGlobals">
        <properties>
            <property name="prefixes" type="array">
                <element value="spd10_ordinace"/>
                <element value="SPD10_Ordinace"/>
            </property>
        </properties>
    </rule>

    <!-- Rules: File naming -->
    <rule ref="WordPress.Files.FileName">
        <properties>
            <property name="strict_class_file_names" value="false"/>
        </properties>
    </rule>

    <!-- Custom rules for our plugin -->
    <rule ref="Generic.CodeAnalysis.UnusedFunctionParameter"/>
    <rule ref="Generic.Commenting.Todo"/>
    <rule ref="Generic.PHP.DeprecatedFunctions"/>
    <rule ref="Generic.PHP.ForbiddenFunctions"/>
    <rule ref="Generic.Functions.FunctionCallArgumentSpacing"/>
    <rule ref="Generic.NamingConventions.ConstructorName"/>
    <rule ref="Generic.PHP.LowerCaseConstant"/>
    <rule ref="Generic.PHP.LowerCaseKeyword"/>
    <rule ref="Generic.Strings.UnnecessaryStringConcat"/>
    <rule ref="Generic.WhiteSpace.IncrementDecrementSpacing"/>

    <!-- Minimum supported WordPress version -->
    <config name="minimum_supported_wp_version" value="6.4"/>
</ruleset>
