<?php
/**
 * PHPStan bootstrap file for SPD10 Ordinace Plugin
 */

// Define WordPress constants if not defined
if (!defined('ABSPATH')) {
    define('ABSPATH', '/tmp/wordpress/');
}

if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', ABSPATH . 'wp-content');
}

if (!defined('WP_PLUGIN_DIR')) {
    define('WP_PLUGIN_DIR', WP_CONTENT_DIR . '/plugins');
}

if (!defined('WPINC')) {
    define('WPINC', 'wp-includes');
}

// Plugin constants
if (!defined('SPD10_ORDINACE_VERSION')) {
    define('SPD10_ORDINACE_VERSION', '2.0.0');
}

if (!defined('SPD10_ORDINACE_PLUGIN_FILE')) {
    define('SPD10_ORDINACE_PLUGIN_FILE', __DIR__ . '/ordinace.php');
}

if (!defined('SPD10_ORDINACE_PLUGIN_DIR')) {
    define('SPD10_ORDINACE_PLUGIN_DIR', __DIR__ . '/');
}

if (!defined('SPD10_ORDINACE_PLUGIN_URL')) {
    define('SPD10_ORDINACE_PLUGIN_URL', 'http://example.com/wp-content/plugins/ordinace/');
}

if (!defined('SPD10_ORDINACE_PLUGIN_BASENAME')) {
    define('SPD10_ORDINACE_PLUGIN_BASENAME', 'ordinace/ordinace.php');
}

if (!defined('SPD10_ORDINACE_TEXT_DOMAIN')) {
    define('SPD10_ORDINACE_TEXT_DOMAIN', 'spd10-ordinace');
}

// WordPress time constants
if (!defined('MINUTE_IN_SECONDS')) {
    define('MINUTE_IN_SECONDS', 60);
}

if (!defined('HOUR_IN_SECONDS')) {
    define('HOUR_IN_SECONDS', 3600);
}

if (!defined('DAY_IN_SECONDS')) {
    define('DAY_IN_SECONDS', 86400);
}

if (!defined('WEEK_IN_SECONDS')) {
    define('WEEK_IN_SECONDS', 604800);
}

if (!defined('MONTH_IN_SECONDS')) {
    define('MONTH_IN_SECONDS', 2592000);
}

if (!defined('YEAR_IN_SECONDS')) {
    define('YEAR_IN_SECONDS', 31536000);
}

// WordPress upload constants
if (!defined('UPLOAD_ERR_OK')) {
    define('UPLOAD_ERR_OK', 0);
}

// Mock WordPress functions for static analysis
if (!function_exists('wp_verify_nonce')) {
    function wp_verify_nonce($nonce, $action = -1) { return true; }
}

if (!function_exists('current_user_can')) {
    function current_user_can($capability) { return true; }
}

if (!function_exists('wp_send_json_error')) {
    function wp_send_json_error($data = null) { return; }
}

if (!function_exists('wp_send_json_success')) {
    function wp_send_json_success($data = null) { return; }
}

if (!function_exists('sanitize_text_field')) {
    function sanitize_text_field($str) { return $str; }
}

if (!function_exists('sanitize_textarea_field')) {
    function sanitize_textarea_field($str) { return $str; }
}

if (!function_exists('get_transient')) {
    function get_transient($transient) { return false; }
}

if (!function_exists('set_transient')) {
    function set_transient($transient, $value, $expiration = 0) { return true; }
}

if (!function_exists('delete_transient')) {
    function delete_transient($transient) { return true; }
}

if (!function_exists('wp_create_nonce')) {
    function wp_create_nonce($action = -1) { return 'test_nonce'; }
}

if (!function_exists('wp_upload_dir')) {
    function wp_upload_dir() { 
        return array(
            'basedir' => '/tmp/uploads',
            'baseurl' => 'http://example.com/uploads',
        ); 
    }
}

if (!function_exists('wp_mkdir_p')) {
    function wp_mkdir_p($target) { return true; }
}

if (!function_exists('wp_generate_uuid4')) {
    function wp_generate_uuid4() { return 'test-uuid-' . uniqid(); }
}

if (!function_exists('size_format')) {
    function size_format($bytes, $decimals = 0) { return $bytes . ' bytes'; }
}

if (!function_exists('__')) {
    function __($text, $domain = 'default') { return $text; }
}

if (!function_exists('_e')) {
    function _e($text, $domain = 'default') { echo $text; }
}

if (!function_exists('esc_attr_e')) {
    function esc_attr_e($text, $domain = 'default') { echo esc_attr($text); }
}

if (!function_exists('esc_attr')) {
    function esc_attr($text) { return htmlspecialchars($text, ENT_QUOTES); }
}

// Mock WP_Error class
if (!class_exists('WP_Error')) {
    class WP_Error {
        public function __construct($code = '', $message = '', $data = '') {}
        public function get_error_code() { return 'error'; }
        public function get_error_message() { return 'Error message'; }
    }
}

// Mock global variables
global $wpdb;
if (!isset($wpdb)) {
    $wpdb = new stdClass();
    $wpdb->options = 'wp_options';
}
