parameters:
    level: 6
    paths:
        - ordinace.php
        - includes/
    
    excludePaths:
        - tests/
        - vendor/
        - node_modules/
    
    bootstrapFiles:
        - phpstan-bootstrap.php
    
    ignoreErrors:
        # WordPress specific ignores
        - '#Function wp_[a-zA-Z_]+ not found#'
        - '#Function get_[a-zA-Z_]+ not found#'
        - '#Function add_[a-zA-Z_]+ not found#'
        - '#Function apply_filters not found#'
        - '#Function do_action not found#'
        - '#Constant ABSPATH not found#'
        - '#Variable \$wpdb might not be defined#'
        
        # Plugin specific ignores
        - '#Access to an undefined property WP_Error::\$[a-zA-Z_]+#'
        - '#Call to method [a-zA-Z_]+ on an unknown class WP_[a-zA-Z_]+#'
        
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    
    wordpress:
        constantsFile: vendor/php-stubs/wordpress-stubs/wordpress-stubs.php
