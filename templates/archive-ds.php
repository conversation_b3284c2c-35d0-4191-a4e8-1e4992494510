<?php get_header(); ?>


<div class="container my-5">
    <h1 class="mb-4"><PERSON><PERSON><PERSON><PERSON> skupiny</h1>

    <div class="row">
    <div class="col-md-8">
        <?php
        $meta_query = array('relation' => 'AND');

        if (isset($_GET['ds_ctvrt']) && $_GET['ds_ctvrt'] !== '') {
            $meta_query[] = array(
                'key' => '_ds_ctvrt',
                'value' => sanitize_text_field($_GET['ds_ctvrt']),
                'compare' => 'LIKE'
            );
        }

        if (isset($_GET['ds_kapacita']) && $_GET['ds_kapacita'] !== '') {
            $meta_query[] = array(
                'key' => '_ds_kapacita',
                'value' => intval($_GET['ds_kapacita']),
                'type' => 'NUMERIC',
                'compare' => '>='
            );
        } else {
            $meta_query[] = array(
                'key' => '_ds_kapacita',
                'compare' => 'EXISTS'
            );
        }

        $args = array(
            'post_type' => 'ds',
            'posts_per_page' => -1,
            'meta_query' => $meta_query,
        );
        $query = new WP_Query($args);

        if ($query->have_posts()) : ?>
            <div class="row">
                <?php while ($query->have_posts()) : $query->the_post(); ?>
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <?php if (has_post_thumbnail()) : ?>
                                <img src="<?php the_post_thumbnail_url('medium'); ?>" class="card-img-top" alt="<?php the_title_attribute(); ?>">
                            <?php else: ?>
                                <?php
                                $default_image_id = get_option('ds_default_image_id');
                                $default_image_url = $default_image_id ? wp_get_attachment_url($default_image_id) : esc_url(plugin_dir_url(dirname(__FILE__)) . 'assets/images/default-ds-image.jpg');
                                ?>
                                <img src="<?php echo esc_url($default_image_url); ?>" class="card-img-top" alt="Default Image">
                            <?php endif; ?>
                            <div class="card-body">
                                <h5 class="card-title"><?php the_title(); ?></h5>
                                <?php
                                $ctvrt = get_post_meta(get_the_ID(), '_ds_ctvrt', true);
                                if (!empty($ctvrt)) :
                                ?>
                                <p class="card-text"><strong>Čtvrť:</strong> <?php echo esc_html($ctvrt); ?></p>
                                <?php endif; ?>
                                <?php
                                $kapacita = get_post_meta(get_the_ID(), '_ds_kapacita', true);
                                if (!empty($kapacita)) :
                                ?>
                                <p class="card-text"><strong>Kapacita:</strong> <?php echo esc_html($kapacita); ?></p>
                                <?php endif; ?>
                                <a href="<?php the_permalink(); ?>" class="btn btn-primary">Více informací</a>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
            <?php the_posts_pagination(); ?>
        <?php else : ?>
            <p>Žádné dětské skupiny nebyly nalezeny.</p>
        <?php endif; ?>
        <?php wp_reset_postdata(); ?>
    </div>
    <div class="col-md-4">
        <div class="bg-white p-4 shadow-sm rounded mb-4">
            <h2 class="h4 mb-3">Filtrovat dětské skupiny</h2>
            <form action="<?php echo esc_url(get_post_type_archive_link('ds')); ?>" method="get">
                <div class="mb-3">
                    <label for="ds_ctvrt" class="form-label">Čtvrť</label>
                    <select name="ds_ctvrt" id="ds_ctvrt" class="form-select">
                        <option value="">Všechny čtvrti</option>
                        <?php
                        global $wpdb;
                        $ctvrti = $wpdb->get_col("SELECT DISTINCT meta_value FROM {$wpdb->postmeta} WHERE meta_key = '_ds_ctvrt' AND meta_value <> ''");
                        if (!empty($ctvrti)) {
                            foreach ($ctvrti as $ctvrt) {
                                $selected = (isset($_GET['ds_ctvrt']) && $_GET['ds_ctvrt'] == $ctvrt) ? 'selected' : '';
                                echo '<option value="' . esc_attr($ctvrt) . '" ' . $selected . '>' . esc_html($ctvrt) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="ds_kapacita" class="form-label">Minimální kapacita</label>
                    <input type="number" name="ds_kapacita" id="ds_kapacita" class="form-control" min="1" value="<?php echo isset($_GET['ds_kapacita']) ? esc_attr($_GET['ds_kapacita']) : ''; ?>">
                </div>
                <button type="submit" class="btn btn-primary">Filtrovat</button>
                <button type="button" class="btn btn-secondary" onclick="window.location.href='<?php echo esc_url(get_post_type_archive_link('ds')); ?>'">Reset filtru</button>
            </form>
        </div>
        <div class="bg-white p-4 shadow-sm rounded">
            <h2 class="h4 mb-3">Mapa dětských skupin</h2>
            <div id="ds-map" style="height: 300px;"></div>
        </div>
        <div class="bg-white p-4 shadow-sm rounded mt-4">
            <p class="has-small-font-size"><em>Za aktuálnost údajů odpovídá vždy daný provozovatel. Pro konkrétnější informace kontaktujte prosím provozovatele dětských skupin. Tento rozcestník vznikl v rámci aktivit naplňování <a href="https://strategieprodesitku.cz/rodinna-politika-v-akci/">Koncepce rodinné politiky v Praze 10</a>.</em></p>
        </div>
    </div>
</div>
</div>

<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<script>
document.addEventListener('DOMContentLoaded', function() {
    var map = L.map('ds-map').setView([50.0755, 14.4378], 12);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    <?php
$meta_query = array('relation' => 'AND');

if (isset($_GET['ds_ctvrt']) && $_GET['ds_ctvrt'] !== '') {
    $meta_query[] = array(
        'key' => '_ds_ctvrt',
        'value' => sanitize_text_field($_GET['ds_ctvrt']),
        'compare' => 'LIKE'
    );
}

if (isset($_GET['ds_kapacita']) && $_GET['ds_kapacita'] !== '') {
    $meta_query[] = array(
        'key' => '_ds_kapacita',
        'value' => intval($_GET['ds_kapacita']),
        'type' => 'NUMERIC',
        'compare' => '>='
    );
} else {
    $meta_query[] = array(
        'key' => '_ds_kapacita',
        'compare' => 'EXISTS'
    );
}

$args = array(
    'post_type' => 'ds',
    'posts_per_page' => -1,
    'meta_query' => $meta_query,
);
$query = new WP_Query($args);
while ($query->have_posts()) : $query->the_post();
    $lat = get_post_meta(get_the_ID(), '_ds_lat', true);
    $lon = get_post_meta(get_the_ID(), '_ds_lon', true);
    if ($lat && $lon) :
?>
    L.marker([<?php echo esc_js($lat); ?>, <?php echo esc_js($lon); ?>]).addTo(map)
        .bindPopup('<a href="<?php the_permalink(); ?>"><?php echo esc_js(get_the_title()); ?></a>');
<?php
    endif;
endwhile;
wp_reset_postdata();
?>
});
</script>

<?php get_footer(); ?>
