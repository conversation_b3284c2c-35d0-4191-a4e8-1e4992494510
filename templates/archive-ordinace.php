<?php
/**
 * Archive Template: Ordinace - Professional Design
 * Theme override path: yourtheme/ordinace/archive-ordinace.php
 *
 * @package SPD10_Ordinace
 */

if (!defined('ABSPATH')) { exit; }

// Function to get illustration based on type
function get_ordinace_illustration_archive($type) {
    $illustrations = array(
        'pediatr' => 'data:image/svg+xml;base64,' . base64_encode('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#e0f2fe"/><stop offset="100%" style="stop-color:#b3e5fc"/></linearGradient></defs><rect width="200" height="200" fill="url(#bg)"/><circle cx="100" cy="80" r="25" fill="#ffb74d"/><rect x="85" y="105" width="30" height="40" rx="15" fill="#4fc3f7"/><rect x="75" y="125" width="15" height="30" rx="7" fill="#ffb74d"/><rect x="110" y="125" width="15" height="30" rx="7" fill="#ffb74d"/><circle cx="92" cy="75" r="3" fill="#333"/><circle cx="108" cy="75" r="3" fill="#333"/><path d="M95 85 Q100 90 105 85" stroke="#333" stroke-width="2" fill="none"/><rect x="40" y="160" width="120" height="8" rx="4" fill="#4caf50"/><text x="100" y="185" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Pediatrie</text></svg>'),
        'prakticky-lekar' => 'data:image/svg+xml;base64,' . base64_encode('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#f3e5f5"/><stop offset="100%" style="stop-color:#e1bee7"/></linearGradient></defs><rect width="200" height="200" fill="url(#bg)"/><circle cx="100" cy="70" r="25" fill="#ffb74d"/><rect x="85" y="95" width="30" height="50" rx="15" fill="#2196f3"/><rect x="75" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><rect x="110" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><circle cx="92" cy="65" r="3" fill="#333"/><circle cx="108" cy="65" r="3" fill="#333"/><path d="M95 75 Q100 80 105 75" stroke="#333" stroke-width="2" fill="none"/><rect x="90" y="50" width="20" height="4" rx="2" fill="#fff"/><rect x="40" y="170" width="120" height="8" rx="4" fill="#9c27b0"/><text x="100" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Praktický lékař</text></svg>'),
        'general' => 'data:image/svg+xml;base64,' . base64_encode('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#e8f5e8"/><stop offset="100%" style="stop-color:#c8e6c9"/></linearGradient></defs><rect width="200" height="200" fill="url(#bg)"/><circle cx="100" cy="70" r="25" fill="#ffb74d"/><rect x="85" y="95" width="30" height="50" rx="15" fill="#4caf50"/><rect x="75" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><rect x="110" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><circle cx="92" cy="65" r="3" fill="#333"/><circle cx="108" cy="65" r="3" fill="#333"/><path d="M95 75 Q100 80 105 75" stroke="#333" stroke-width="2" fill="none"/><rect x="95" y="105" width="10" height="2" fill="#fff"/><rect x="99" y="101" width="2" height="10" fill="#fff"/><rect x="40" y="170" width="120" height="8" rx="4" fill="#4caf50"/><text x="100" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Ordinace</text></svg>')
    );

    return isset($illustrations[$type]) ? $illustrations[$type] : $illustrations['general'];
}

get_header();

$typ = isset($_GET['typ']) ? sanitize_text_field($_GET['typ']) : '';
$ctvrt = isset($_GET['ctvrt']) ? sanitize_text_field($_GET['ctvrt']) : '';
$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$per_page = intval(spd10_ordinace_get_option('posts_per_page', 12));

// Build filter query
$args = array(
    'post_type' => 'ordinace',
    'post_status' => 'publish',
    'paged' => max(1, get_query_var('paged')),
    'posts_per_page' => $per_page,
);

// Build meta_query array for custom field searches
$meta_query = array();

// Add text search with custom fields and title
if (!empty($search)) {
    // Add custom search functionality
    add_filter('posts_where', function($where, $wp_query) use ($search) {
        global $wpdb;
        if ($wp_query->get('post_type') === 'ordinace' && !empty($search)) {
            $search_term = '%' . $wpdb->esc_like($search) . '%';
            $where .= $wpdb->prepare(" OR {$wpdb->posts}.post_title LIKE %s", $search_term);
        }
        return $where;
    }, 10, 2);

    // Search in custom fields
    $meta_query[] = array(
        'relation' => 'OR',
        array(
            'key' => '_spd10_ordinace_doctors_names',
            'value' => $search,
            'compare' => 'LIKE'
        ),
        array(
            'key' => '_spd10_ordinace_address',
            'value' => $search,
            'compare' => 'LIKE'
        ),
        array(
            'key' => '_spd10_ordinace_organization',
            'value' => $search,
            'compare' => 'LIKE'
        ),
        array(
            'key' => '_spd10_ordinace_quarter',
            'value' => $search,
            'compare' => 'LIKE'
        )
    );
}

// Add quarter filter
if (!empty($ctvrt)) {
    $meta_query[] = array(
        'key' => '_spd10_ordinace_quarter',
        'value' => $ctvrt,
        'compare' => 'LIKE',
    );
}

// Set meta_query if we have any conditions
if (!empty($meta_query)) {
    if (count($meta_query) > 1) {
        $meta_query['relation'] = 'AND';
    }
    $args['meta_query'] = $meta_query;
}

// Add taxonomy filter
if (!empty($typ)) {
    $args['tax_query'] = array(
        array(
            'taxonomy' => 'ordinace_typ',
            'field' => 'slug',
            'terms' => $typ,
        ),
    );
}

$query = new WP_Query($args);

// Remove the search filter to avoid affecting other queries
if (!empty($search)) {
    remove_all_filters('posts_where');
}

// Get all unique quarters for filter dropdown
global $wpdb;
$quarters = $wpdb->get_col("
    SELECT DISTINCT meta_value
    FROM {$wpdb->postmeta}
    WHERE meta_key = '_spd10_ordinace_quarter'
    AND meta_value != ''
    ORDER BY meta_value ASC
");
?>

<div class="spd10-ordinace-archive-container">
    <div class="spd10-ordinace-archive-hero">
        <div class="spd10-ordinace-archive-hero-content">
            <h1 class="spd10-ordinace-archive-title"><?php post_type_archive_title(); ?></h1>
            <p class="spd10-ordinace-archive-subtitle"><?php _e('Seznam ordinací na Praze 10', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
            <div class="spd10-ordinace-archive-stats">
                <?php
                $total = $query->found_posts;
                printf(_n('Nalezena %d ordinace', 'Nalezeno %d ordinací', $total, SPD10_ORDINACE_TEXT_DOMAIN), $total);
                ?>
            </div>
        </div>
    </div>

    <div class="spd10-ordinace-filters-container">
        <form method="get" class="spd10-ordinace-filters">
            <div class="spd10-ordinace-filter-group">
                <div class="spd10-ordinace-filter-item">
                    <label for="search-filter">
                        <svg class="spd10-ordinace-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                        <?php _e('Vyhledávání', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </label>
                    <input type="text" name="search" id="search-filter" value="<?php echo esc_attr($search); ?>" placeholder="<?php _e('Hledat podle názvu, lékaře, adresy...', SPD10_ORDINACE_TEXT_DOMAIN); ?>" class="spd10-ordinace-input" />
                </div>

                <div class="spd10-ordinace-filter-item">
                    <label for="typ-filter">
                        <svg class="spd10-ordinace-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z"/>
                        </svg>
                        <?php _e('Typ ordinace', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </label>
                    <select name="typ" id="typ-filter" class="spd10-ordinace-select">
                        <option value=""><?php _e('Všechny typy', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                        <option value="pediatr" <?php selected($typ, 'pediatr'); ?>><?php _e('Pediatr', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                        <option value="praktik-dospeli" <?php selected($typ, 'praktik-dospeli'); ?>><?php _e('Praktický lékař', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                    </select>
                </div>

                <div class="spd10-ordinace-filter-item">
                    <label for="ctvrt-filter">
                        <svg class="spd10-ordinace-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                        </svg>
                        <?php _e('Čtvrť', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </label>
                    <select name="ctvrt" id="ctvrt-filter" class="spd10-ordinace-select">
                        <option value=""><?php _e('Všechny čtvrti', SPD10_ORDINACE_TEXT_DOMAIN); ?></option>
                        <?php foreach ($quarters as $quarter) : ?>
                            <option value="<?php echo esc_attr($quarter); ?>" <?php selected($ctvrt, $quarter); ?>>
                                <?php echo esc_html($quarter); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="spd10-ordinace-filter-actions">
                    <button type="submit" class="spd10-ordinace-filter-button spd10-ordinace-filter-button-primary">
                        <svg class="spd10-ordinace-button-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                        <?php _e('Filtrovat', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    </button>
                    <?php if (!empty($typ) || !empty($ctvrt) || !empty($search)) : ?>
                        <a href="<?php echo get_post_type_archive_link('ordinace'); ?>" class="spd10-ordinace-filter-button spd10-ordinace-filter-button-secondary">
                            <svg class="spd10-ordinace-button-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                            <?php _e('Zrušit filtry', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>

    <?php if ($query->have_posts()) : ?>
        <div class="spd10-ordinace-grid">
            <?php $index = 0; while ($query->have_posts()) : $query->the_post(); $index++;
                // Get meta data
                $doctors_names = get_post_meta(get_the_ID(), '_spd10_ordinace_doctors_names', true);
                $organization = get_post_meta(get_the_ID(), '_spd10_ordinace_organization', true);
                $quarter = get_post_meta(get_the_ID(), '_spd10_ordinace_quarter', true);
                $address = get_post_meta(get_the_ID(), '_spd10_ordinace_address', true);
                $phone = get_post_meta(get_the_ID(), '_spd10_ordinace_phone', true);
                $email = get_post_meta(get_the_ID(), '_spd10_ordinace_email', true);

                // Get ordinace type for illustration
                $terms = get_the_terms(get_the_ID(), 'ordinace_typ');
                $ordinace_type = $terms && !is_wp_error($terms) ? $terms[0]->slug : 'general';
                ?>
                <article class="spd10-ordinace-card-item" style="animation-delay: <?php echo $index * 0.1; ?>s">
                    <div class="spd10-ordinace-card-image">
                        <img src="<?php echo get_ordinace_illustration_archive($ordinace_type); ?>" alt="<?php echo esc_attr($ordinace_type); ?>" class="spd10-ordinace-card-illustration">
                        <?php if ($terms && !is_wp_error($terms)) : ?>
                            <div class="spd10-ordinace-card-badge">
                                <?php echo esc_html($terms[0]->name); ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="spd10-ordinace-card-content">
                        <h2 class="spd10-ordinace-card-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h2>

                        <?php if (!empty($quarter)) : ?>
                            <div class="spd10-ordinace-card-location">
                                <svg class="spd10-ordinace-card-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                                </svg>
                                <?php echo esc_html($quarter); ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($doctors_names)) : ?>
                            <div class="spd10-ordinace-card-doctors">
                                <svg class="spd10-ordinace-card-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z"/>
                                </svg>
                                <div class="spd10-ordinace-card-doctors-list">
                                    <?php
                                    $doctors = array_filter(array_map('trim', explode("\n", $doctors_names)));
                                    $first_two = array_slice($doctors, 0, 2);
                                    foreach ($first_two as $name) : ?>
                                        <span class="spd10-ordinace-card-doctor"><?php echo esc_html($name); ?></span>
                                    <?php endforeach;
                                    if (count($doctors) > 2) : ?>
                                        <span class="spd10-ordinace-card-more">+<?php echo count($doctors) - 2; ?> dalších</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="spd10-ordinace-card-contact">
                            <?php if (!empty($phone)) : ?>
                                <div class="spd10-ordinace-card-contact-item">
                                    <svg class="spd10-ordinace-card-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                                    </svg>
                                    <a href="tel:<?php echo esc_attr(preg_replace('/\s+/', '', $phone)); ?>"><?php echo esc_html($phone); ?></a>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($email)) : ?>
                                <div class="spd10-ordinace-card-contact-item">
                                    <svg class="spd10-ordinace-card-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                    </svg>
                                    <a href="mailto:<?php echo esc_attr($email); ?>"><?php echo esc_html($email); ?></a>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="spd10-ordinace-card-footer">
                            <a href="<?php the_permalink(); ?>" class="spd10-ordinace-card-button">
                                <?php _e('Více informací', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                                <svg class="spd10-ordinace-card-arrow" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </article>
            <?php endwhile; ?>
        </div>

        <div class="spd10-ordinace-pagination">
            <?php
            $current_page = max(1, get_query_var('paged'));
            $total_pages = $query->max_num_pages;

            if ($total_pages > 1) {
                $pagination_args = array(
                    'total' => $total_pages,
                    'current' => $current_page,
                    'prev_text' => '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/></svg>' . __('Předchozí', SPD10_ORDINACE_TEXT_DOMAIN),
                    'next_text' => __('Další', SPD10_ORDINACE_TEXT_DOMAIN) . '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/></svg>',
                    'type' => 'plain',
                    'mid_size' => 2,
                    'end_size' => 1,
                    'show_all' => false,
                );

                // Add current filter parameters to pagination links
                if (!empty($typ) || !empty($ctvrt) || !empty($search)) {
                    $pagination_args['add_args'] = array();
                    if (!empty($typ)) {
                        $pagination_args['add_args']['typ'] = $typ;
                    }
                    if (!empty($ctvrt)) {
                        $pagination_args['add_args']['ctvrt'] = $ctvrt;
                    }
                    if (!empty($search)) {
                        $pagination_args['add_args']['search'] = $search;
                    }
                }

                echo paginate_links($pagination_args);
            }
            ?>
        </div>
    <?php else : ?>
        <div class="spd10-ordinace-no-results">
            <div class="spd10-ordinace-no-results-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
            </div>
            <h3><?php _e('Žádné ordinace nebyly nalezeny', SPD10_ORDINACE_TEXT_DOMAIN); ?></h3>
            <p><?php _e('Zkuste změnit filtry nebo vyhledávací kritéria.', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
            <a href="<?php echo get_post_type_archive_link('ordinace'); ?>" class="spd10-ordinace-card-button">
                <?php _e('Zobrazit všechny ordinace', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </a>
        </div>
    <?php endif; ?>
</div>

<style>
/* Professional Ordinace Archive Styles */
.spd10-ordinace-archive-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.spd10-ordinace-archive-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 60px 40px;
    margin-bottom: 40px;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.spd10-ordinace-archive-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
}

.spd10-ordinace-archive-hero-content {
    position: relative;
    z-index: 1;
}

.spd10-ordinace-archive-title {
    font-size: 3rem;
    font-weight: 700;
    margin: 0 0 15px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.spd10-ordinace-archive-subtitle {
    font-size: 1.2rem;
    margin: 0 0 20px 0;
    opacity: 0.9;
}

.spd10-ordinace-archive-stats {
    font-size: 1rem;
    background: rgba(255,255,255,0.2);
    padding: 10px 20px;
    border-radius: 25px;
    display: inline-block;
    backdrop-filter: blur(10px);
}

.spd10-ordinace-filters-container {
    margin-bottom: 40px;
}

.spd10-ordinace-filters {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
}

.spd10-ordinace-filter-group {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 25px;
    align-items: end;
}

.spd10-ordinace-filter-item label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.spd10-ordinace-filter-icon {
    width: 18px;
    height: 18px;
    color: #667eea;
}

.spd10-ordinace-select,
.spd10-ordinace-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.spd10-ordinace-select:focus,
.spd10-ordinace-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.spd10-ordinace-filter-actions {
    display: flex;
    gap: 12px;
}

.spd10-ordinace-filter-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
}

.spd10-ordinace-filter-button-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.spd10-ordinace-filter-button-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.spd10-ordinace-filter-button-secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
}

.spd10-ordinace-filter-button-secondary:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.spd10-ordinace-button-icon {
    width: 18px;
    height: 18px;
}

.spd10-ordinace-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 50px;
}

.spd10-ordinace-card-item {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.4s ease;
    animation: slideUp 0.6s ease-out;
}

.spd10-ordinace-card-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.spd10-ordinace-card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.spd10-ordinace-card-illustration {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.spd10-ordinace-card-item:hover .spd10-ordinace-card-illustration {
    transform: scale(1.05);
}

.spd10-ordinace-card-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255,255,255,0.95);
    color: #2d3748;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-width: calc(100% - 30px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-transform: none;
}

.spd10-ordinace-card-content {
    padding: 25px;
}

.spd10-ordinace-card-title {
    margin: 0 0 15px 0;
    font-size: 1.3rem;
    font-weight: 700;
}

.spd10-ordinace-card-title a {
    color: #2d3748;
    text-decoration: none;
    transition: color 0.3s ease;
}

.spd10-ordinace-card-title a:hover {
    color: #667eea;
}

.spd10-ordinace-card-location,
.spd10-ordinace-card-doctors {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 15px;
    color: #4a5568;
    font-size: 0.9rem;
}

.spd10-ordinace-card-icon {
    width: 18px;
    height: 18px;
    color: #667eea;
    flex-shrink: 0;
    margin-top: 1px;
}

.spd10-ordinace-card-doctors-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.spd10-ordinace-card-doctor {
    font-weight: 500;
}

.spd10-ordinace-card-more {
    color: #667eea;
    font-size: 0.8rem;
    font-weight: 600;
}

.spd10-ordinace-card-contact {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.spd10-ordinace-card-contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
}

.spd10-ordinace-card-contact-item a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.spd10-ordinace-card-contact-item a:hover {
    color: #764ba2;
    text-decoration: underline;
}

.spd10-ordinace-card-footer {
    border-top: 1px solid #f0f0f0;
    padding-top: 20px;
}

.spd10-ordinace-card-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-sizing: border-box;
}

.spd10-ordinace-card-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

.spd10-ordinace-card-arrow {
    width: 18px;
    height: 18px;
    transition: transform 0.3s ease;
}

.spd10-ordinace-card-button:hover .spd10-ordinace-card-arrow {
    transform: translateX(4px);
}

.spd10-ordinace-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 50px;
    gap: 8px;
    flex-wrap: wrap;
}

.spd10-ordinace-pagination .page-numbers {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 10px 14px;
    min-width: 44px;
    background: white;
    color: #4a5568;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    border: 2px solid #e2e8f0;
    box-sizing: border-box;
}

.spd10-ordinace-pagination .page-numbers:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.spd10-ordinace-pagination .page-numbers.current {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    cursor: default;
}

.spd10-ordinace-pagination .page-numbers.current:hover {
    transform: none;
}

.spd10-ordinace-pagination .page-numbers.dots {
    border: none;
    background: transparent;
    color: #a0aec0;
    cursor: default;
    padding: 10px 8px;
}

.spd10-ordinace-pagination .page-numbers.dots:hover {
    background: transparent;
    color: #a0aec0;
    transform: none;
    box-shadow: none;
}

.spd10-ordinace-pagination .page-numbers svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.spd10-ordinace-pagination .page-numbers.prev,
.spd10-ordinace-pagination .page-numbers.next {
    padding: 10px 16px;
}

.spd10-ordinace-no-results {
    text-align: center;
    padding: 80px 20px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.spd10-ordinace-no-results-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    color: #cbd5e0;
}

.spd10-ordinace-no-results-icon svg {
    width: 100%;
    height: 100%;
}

.spd10-ordinace-no-results h3 {
    font-size: 1.5rem;
    color: #2d3748;
    margin: 0 0 10px 0;
}

.spd10-ordinace-no-results p {
    color: #4a5568;
    margin: 0 0 30px 0;
}

/* Responsive Design - 3-2-1 Layout */
@media (max-width: 1200px) {
    .spd10-ordinace-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .spd10-ordinace-card-badge {
        font-size: 0.75rem;
        padding: 5px 10px;
    }
}

@media (max-width: 900px) {
    .spd10-ordinace-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .spd10-ordinace-card-image {
        height: 180px;
    }

    .spd10-ordinace-card-badge {
        top: 12px;
        right: 12px;
        font-size: 0.7rem;
        padding: 4px 8px;
    }
}

@media (max-width: 768px) {
    .spd10-ordinace-archive-container {
        padding: 15px;
    }

    .spd10-ordinace-archive-hero {
        padding: 40px 25px;
    }

    .spd10-ordinace-archive-title {
        font-size: 2rem;
    }

    .spd10-ordinace-filter-group {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .spd10-ordinace-filter-actions {
        justify-content: center;
    }

    .spd10-ordinace-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .spd10-ordinace-filters {
        padding: 20px;
    }

    /* Adjust card elements for mobile */
    .spd10-ordinace-card-content {
        padding: 20px;
    }

    .spd10-ordinace-card-badge {
        top: 10px;
        right: 10px;
        font-size: 0.75rem;
        padding: 4px 8px;
    }

    .spd10-ordinace-card-image {
        height: 160px;
    }
}

/* Loading animations */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Staggered animation for cards */
.spd10-ordinace-card-item:nth-child(1) { animation-delay: 0.1s; }
.spd10-ordinace-card-item:nth-child(2) { animation-delay: 0.2s; }
.spd10-ordinace-card-item:nth-child(3) { animation-delay: 0.3s; }
.spd10-ordinace-card-item:nth-child(4) { animation-delay: 0.4s; }
.spd10-ordinace-card-item:nth-child(5) { animation-delay: 0.5s; }
.spd10-ordinace-card-item:nth-child(6) { animation-delay: 0.6s; }
</style>

<?php
wp_reset_postdata();
get_footer();

