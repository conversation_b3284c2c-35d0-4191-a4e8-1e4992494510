<?php
$adresa = get_post_meta($post->ID, '_ds_adresa', true);
$kontaktni_osoba = get_post_meta($post->ID, '_ds_kontaktni_osoba', true);
$email = get_post_meta($post->ID, '_ds_email', true);
$telefon = get_post_meta($post->ID, '_ds_telefon', true);
$kapacita = get_post_meta($post->ID, '_ds_kapacita', true);
$vek_deti = get_post_meta($post->ID, '_ds_vek_deti', true);
$zapis = get_post_meta($post->ID, '_ds_zapis', true);
$registrace_mpsv = get_post_meta($post->ID, '_ds_registrace_mpsv', true);
$webove_stranky = get_post_meta($post->ID, '_ds_webove_stranky', true);
$specificke_informace = get_post_meta($post->ID, '_ds_specificke_informace', true);
$cena = get_post_meta($post->ID, '_ds_cena', true);
$oteviraci_doba = get_post_meta($post->ID, '_ds_oteviraci_doba', true);
$provoz_celocelocni = get_post_meta($post->ID, '_ds_provoz_celocelocni', true);
$provoz_od = get_post_meta($post->ID, '_ds_provoz_od', true);
$verejna_pro_zamestnance = get_post_meta($post->ID, '_ds_verejna_pro_zamestnance', true);
$email = get_post_meta($post->ID, '_ds_email', true);

// Přidání nonce pole pro zabezpečení formuláře
wp_nonce_field('ds_save_details', 'ds_details_nonce');
?>
<style>
    .ds-form-row {
        display: flex;
        margin-bottom: 10px;
    }
    .ds-form-row label {
        flex: 0 0 30%;
        text-align: right;
        padding-right: 10px;
        line-height: 30px;
    }
    .ds-form-row input,
    .ds-form-row select,
    .ds-form-row textarea {
        flex: 0 0 70%;
        width: 100%;
    }
    .ds-form-row textarea {
        height: 100px;
    }
</style>

<div class="ds-form-row">
    <label for="ds_provozovatel_id">Provozovatel:</label>
    <select id="ds_provozovatel_id" name="ds_provozovatel_id">
        <?php
        $provozovatele = get_posts(array('post_type' => 'provozovatel', 'numberposts' => -1));
        $current_provozovatel_id = get_post_meta($post->ID, '_ds_provozovatel_id', true);
        foreach ($provozovatele as $provozovatel) {
            echo '<option value="' . $provozovatel->ID . '"' . selected($current_provozovatel_id, $provozovatel->ID, false) . '>' . $provozovatel->post_title . '</option>';
        }
        ?>
    </select>
</div>

<div class="ds-form-row">
    <label for="ds_kontaktni_osoba">Kontaktní osoba:</label>
    <input type="text" id="ds_kontaktni_osoba" name="ds_kontaktni_osoba" value="<?php echo esc_attr($kontaktni_osoba); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_kontakt_na_ko">Kontakt na KO:</label>
    <input type="text" id="ds_kontakt_na_ko" name="ds_kontakt_na_ko" value="<?php echo esc_attr(get_post_meta($post->ID, '_ds_kontakt_na_ko', true)); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_ctvrt">Čtvrť:</label>
    <input type="text" id="ds_ctvrt" name="ds_ctvrt" value="<?php echo esc_attr(get_post_meta($post->ID, '_ds_ctvrt', true)); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_adresa">Adresa:</label>
    <div style="display: flex; width: 70%;">
        <input type="text" id="ds_adresa" name="ds_adresa" value="<?php echo esc_attr($adresa); ?>" style="flex-grow: 1; margin-right: 10px;" />
        <button type="button" id="ds_geocode" style="flex-shrink: 0;">Geokódovat</button>
    </div>
</div>
<div class="ds-form-row">
    <label for="ds_lat">Zeměpisná šířka:</label>
    <input type="text" id="ds_lat" name="ds_lat" value="<?php echo esc_attr(get_post_meta($post->ID, '_ds_lat', true)); ?>" />
</div>
<div class="ds-form-row">
    <label for="ds_lon">Zeměpisná délka:</label>
    <input type="text" id="ds_lon" name="ds_lon" value="<?php echo esc_attr(get_post_meta($post->ID, '_ds_lon', true)); ?>" />
</div>
<div class="ds-form-row">
    <label>Zobrazení v mapě:</label>
    <div id="ds_map" style="height: 300px; width: 100%;"></div>
</div>

<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

<script>
document.addEventListener('DOMContentLoaded', function() {
    var map = L.map('ds_map').setView([50.0755, 14.4378], 12);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    var marker;
    var lat = document.getElementById('ds_lat').value;
    var lon = document.getElementById('ds_lon').value;
    if (lat && lon) {
        marker = L.marker([lat, lon]).addTo(map);
        map.setView([lat, lon], 15);
    }

    document.getElementById('ds_geocode').addEventListener('click', function() {
        var address = document.getElementById('ds_adresa').value;
        if (!address) {
            alert('Prosím, zadejte adresu.');
            return;
        }
        fetch('https://nominatim.openstreetmap.org/search?format=json&q=' + encodeURIComponent(address))
            .then(response => response.json())
            .then(data => {
                if (data.length > 0) {
                    var lat = parseFloat(data[0].lat);
                    var lon = parseFloat(data[0].lon);
                    document.getElementById('ds_lat').value = lat.toFixed(6);
                    document.getElementById('ds_lon').value = lon.toFixed(6);
                    if (marker) {
                        marker.setLatLng([lat, lon]);
                    } else {
                        marker = L.marker([lat, lon]).addTo(map);
                    }
                    map.setView([lat, lon], 15);
                } else {
                    alert('Adresa nebyla nalezena. Zkuste prosím zadat přesnější adresu.');
                }
            })
            .catch(error => {
                console.error('Chyba při geokódování:', error);
                alert('Nastala chyba při geokódování. Zkuste to prosím znovu.');
            });
    });

    // Aktualizace mapy při načtení stránky
    if (lat && lon) {
        map.setView([lat, lon], 15);
        if (!marker) {
            marker = L.marker([lat, lon]).addTo(map);
        } else {
            marker.setLatLng([lat, lon]);
        }
    }
});
</script>

<div class="ds-form-row">
    <label for="ds_kapacita">Kapacita:</label>
    <input type="number" id="ds_kapacita" name="ds_kapacita" value="<?php echo esc_attr($kapacita); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_vek_deti">Věk dětí:</label>
    <input type="text" id="ds_vek_deti" name="ds_vek_deti" value="<?php echo esc_attr($vek_deti); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_zapis">Zápis:</label>
    <input type="text" id="ds_zapis" name="ds_zapis" value="<?php echo esc_attr($zapis); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_registrace_mpsv">Registrace MPSV:</label>
    <input type="text" id="ds_registrace_mpsv" name="ds_registrace_mpsv" value="<?php echo esc_attr($registrace_mpsv); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_webove_stranky">Webové stránky:</label>
    <input type="url" id="ds_webove_stranky" name="ds_webove_stranky" value="<?php echo esc_attr($webove_stranky); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_specificke_informace">Specifické informace:</label>
    <textarea id="ds_specificke_informace" name="ds_specificke_informace"><?php echo esc_textarea($specificke_informace); ?></textarea>
</div>

<div class="ds-form-row">
    <label for="ds_cena">Cena za celotýdenní docházku:</label>
    <input type="text" id="ds_cena" name="ds_cena" value="<?php echo esc_attr($cena); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_galerie">Fotogalerie:</label>
    <input type="hidden" id="ds_galerie" name="ds_galerie" value="<?php echo esc_attr(get_post_meta($post->ID, '_ds_galerie', true)); ?>">
    <button type="button" class="button" id="ds_galerie_button">Přidat/Upravit fotografie</button>
</div>
<div id="ds_galerie_preview" class="gallery"></div>

<script>
jQuery(document).ready(function($) {
    var ds_galerie_frame;
    var ds_galerie_preview = $('#ds_galerie_preview');
    var ds_galerie_input = $('#ds_galerie');

    $('#ds_galerie_button').on('click', function(e) {
        e.preventDefault();

        if (ds_galerie_frame) {
            ds_galerie_frame.open();
            return;
        }

        ds_galerie_frame = wp.media({
            title: 'Vyberte fotografie pro galerii',
            button: {
                text: 'Použít vybrané fotografie'
            },
            multiple: true
        });

        ds_galerie_frame.on('select', function() {
            var attachment = ds_galerie_frame.state().get('selection').toJSON();
            var attachmentIds = ds_galerie_input.val() ? ds_galerie_input.val().split(',') : [];

            $.each(attachment, function(index, value) {
                if (!attachmentIds.includes(value.id.toString())) {
                    attachmentIds.push(value.id);
                    addImageToPreview(value);
                }
            });

            ds_galerie_input.val(attachmentIds.join(','));
        });

        ds_galerie_frame.open();
    });

    function addImageToPreview(image) {
        var imageContainer = $('<div class="image-container"></div>');
        var removeButton = $('<button type="button" class="remove-image">&times;</button>');
        var img = $('<img src="' + image.sizes.thumbnail.url + '" alt="' + image.alt + '" title="' + image.title + '" data-id="' + image.id + '" />');

        imageContainer.append(removeButton);
        imageContainer.append(img);
        ds_galerie_preview.append(imageContainer);

        removeButton.on('click', function() {
            removeImage(image.id);
        });
    }

    function removeImage(imageId) {
        var attachmentIds = ds_galerie_input.val().split(',');
        var index = attachmentIds.indexOf(imageId.toString());
        if (index > -1) {
            attachmentIds.splice(index, 1);
        }
        ds_galerie_input.val(attachmentIds.join(','));
        ds_galerie_preview.find('img[data-id="' + imageId + '"]').parent('.image-container').remove();
    }

    // Načtení existujících obrázků při editaci
    var existing_images = ds_galerie_input.val();
    if (existing_images) {
        var image_ids = existing_images.split(',');
        $.each(image_ids, function(index, id) {
            wp.media.attachment(id).fetch().then(function() {
                addImageToPreview(this.attributes);
            });
        });
    }
});
</script>

<style>
.image-container {
    position: relative;
    display: inline-block;
    margin: 5px;
}
.remove-image {
    position: absolute;
    top: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    cursor: pointer;
    padding: 2px 5px;
    font-size: 16px;
}
#ds_galerie_preview img {
    width: 100px;
    height: 100px;
    object-fit: cover;
}
</style>

<div class="ds-form-row">
    <label for="ds_oteviraci_doba">Otevírací doba:</label>
    <input type="text" id="ds_oteviraci_doba" name="ds_oteviraci_doba" value="<?php echo esc_attr($oteviraci_doba); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_provoz_celocelocni">Provoz celoroční:</label>
    <input type="text" id="ds_provoz_celocelocni" name="ds_provoz_celocelocni" value="<?php echo esc_attr($provoz_celocelocni); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_provoz_od">Provoz od:</label>
    <input type="text" id="ds_provoz_od" name="ds_provoz_od" value="<?php echo esc_attr($provoz_od); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_verejna_pro_zamestnance">Veřejná/pro zaměstnance:</label>
    <input type="text" id="ds_verejna_pro_zamestnance" name="ds_verejna_pro_zamestnance" value="<?php echo esc_attr($verejna_pro_zamestnance); ?>" />
</div>

<div class="ds-form-row">
    <label for="ds_email">Email:</label>
    <input type="email" id="ds_email" name="ds_email" value="<?php echo esc_attr($email); ?>" />
</div>
