<?php
/**
 * Template for Ordinace Details Meta Box
 * 
 * @package SPD10_Ordinace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<style>
.spd10-ordinace-form-row {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.spd10-ordinace-form-row label {
    width: 150px;
    font-weight: bold;
    margin-right: 10px;
}

.spd10-ordinace-form-row input[type="text"],
.spd10-ordinace-form-row input[type="email"],
.spd10-ordinace-form-row input[type="url"],
.spd10-ordinace-form-row textarea {
    flex: 1;
    padding: 5px;
}

.spd10-ordinace-form-row textarea {
    height: 80px;
    resize: vertical;
}

.spd10-ordinace-geocode-section {
    background: #f9f9f9;
    padding: 15px;
    border: 1px solid #ddd;
    margin: 15px 0;
}

.spd10-ordinace-geocode-status {
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.spd10-ordinace-geocode-status.status-ok {
    background: #d4edda;
    color: #155724;
}

.spd10-ordinace-geocode-status.status-pending {
    background: #fff3cd;
    color: #856404;
}

.spd10-ordinace-geocode-status.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.spd10-ordinace-geocode-status.status-manual {
    background: #cce5ff;
    color: #004085;
}

.spd10-ordinace-geocode-status.status-none {
    background: #e9ecef;
    color: #6c757d;
}

.spd10-ordinace-hidden-fields {
    background: #f0f0f1;
    padding: 10px;
    margin-top: 15px;
    border-left: 4px solid #72aee6;
}

.spd10-ordinace-hidden-fields h4 {
    margin-top: 0;
    color: #1d2327;
}
</style>

<div class="spd10-ordinace-form-row">
    <label for="spd10_ordinace_doctors_names"><?php _e('Jména lékařů:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
    <textarea 
        id="spd10_ordinace_doctors_names" 
        name="spd10_ordinace_doctors_names" 
        placeholder="<?php _e('Každé jméno na nový řádek', SPD10_ORDINACE_TEXT_DOMAIN); ?>"
    ><?php echo esc_textarea($doctors_names); ?></textarea>
</div>

<div class="spd10-ordinace-form-row">
    <label for="spd10_ordinace_organization"><?php _e('Organizace:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
    <input 
        type="text" 
        id="spd10_ordinace_organization" 
        name="spd10_ordinace_organization" 
        value="<?php echo esc_attr($organization); ?>" 
        placeholder="<?php _e('Název organizace/ordinace', SPD10_ORDINACE_TEXT_DOMAIN); ?>"
    />
</div>

<div class="spd10-ordinace-form-row">
    <label for="spd10_ordinace_quarter"><?php _e('Čtvrť:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
    <input 
        type="text" 
        id="spd10_ordinace_quarter" 
        name="spd10_ordinace_quarter" 
        value="<?php echo esc_attr($quarter); ?>" 
        placeholder="<?php _e('Např. Vinohrady, Vršovice', SPD10_ORDINACE_TEXT_DOMAIN); ?>"
    />
</div>

<div class="spd10-ordinace-form-row">
    <label for="spd10_ordinace_address"><?php _e('Adresa:', SPD10_ORDINACE_TEXT_DOMAIN); ?> *</label>
    <input 
        type="text" 
        id="spd10_ordinace_address" 
        name="spd10_ordinace_address" 
        value="<?php echo esc_attr($address); ?>" 
        placeholder="<?php _e('Úplná adresa ordinace', SPD10_ORDINACE_TEXT_DOMAIN); ?>"
        required
    />
</div>

<div class="spd10-ordinace-form-row">
    <label for="spd10_ordinace_email"><?php _e('E-mail:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
    <input 
        type="email" 
        id="spd10_ordinace_email" 
        name="spd10_ordinace_email" 
        value="<?php echo esc_attr($email); ?>" 
        placeholder="<?php _e('<EMAIL>', SPD10_ORDINACE_TEXT_DOMAIN); ?>"
    />
</div>

<div class="spd10-ordinace-form-row">
    <label for="spd10_ordinace_phone"><?php _e('Telefon:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
    <input 
        type="text" 
        id="spd10_ordinace_phone" 
        name="spd10_ordinace_phone" 
        value="<?php echo esc_attr($phone); ?>" 
        placeholder="<?php _e('+420 123 456 789', SPD10_ORDINACE_TEXT_DOMAIN); ?>"
    />
</div>

<div class="spd10-ordinace-form-row">
    <label for="spd10_ordinace_website"><?php _e('Webové stránky:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
    <input 
        type="url" 
        id="spd10_ordinace_website" 
        name="spd10_ordinace_website" 
        value="<?php echo esc_attr($website); ?>" 
        placeholder="<?php _e('https://www.ordinace.cz', SPD10_ORDINACE_TEXT_DOMAIN); ?>"
    />
</div>

<div class="spd10-ordinace-geocode-section">
    <h4><?php _e('Geokódování', SPD10_ORDINACE_TEXT_DOMAIN); ?></h4>
    
    <div class="spd10-ordinace-form-row">
        <label><?php _e('Stav:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
        <span class="geocode-status-indicator spd10-ordinace-geocode-status status-<?php echo esc_attr(strtolower($geocode_status ?: 'none')); ?>">
            <?php
            switch ($geocode_status) {
                case 'OK':
                case 'ok':
                    _e('Geokódováno', SPD10_ORDINACE_TEXT_DOMAIN);
                    break;
                case 'PENDING':
                case 'pending':
                    _e('Čeká na geokódování', SPD10_ORDINACE_TEXT_DOMAIN);
                    break;
                case 'FAILED':
                case 'failed':
                    _e('Geokódování selhalo', SPD10_ORDINACE_TEXT_DOMAIN);
                    break;
                case 'manual':
                    _e('Ručně nastaveno', SPD10_ORDINACE_TEXT_DOMAIN);
                    break;
                default:
                    _e('Negeokódováno', SPD10_ORDINACE_TEXT_DOMAIN);
            }
            ?>
        </span>
        <button type="button" class="geocode-button button" style="margin-left: 10px;">
            <?php _e('Geokódovat', SPD10_ORDINACE_TEXT_DOMAIN); ?>
        </button>
    </div>
    
    <div class="spd10-ordinace-form-row">
        <label for="spd10_ordinace_lat"><?php _e('Zeměpisná šířka:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
        <input 
            type="text" 
            id="spd10_ordinace_lat" 
            name="spd10_ordinace_lat" 
            value="<?php echo esc_attr($lat); ?>" 
            readonly
        />
    </div>
    
    <div class="spd10-ordinace-form-row">
        <label for="spd10_ordinace_lng"><?php _e('Zeměpisná délka:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
        <input 
            type="text" 
            id="spd10_ordinace_lng" 
            name="spd10_ordinace_lng" 
            value="<?php echo esc_attr($lng); ?>" 
            readonly
        />
    </div>
    
    <div class="spd10-ordinace-form-row">
        <label><?php _e('Náhled mapy:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
        <div id="spd10-ordinace-admin-map" class="spd10-ordinace-admin-map" style="height: 250px; width: 100%; border: 1px solid #ddd; border-radius: 4px;"></div>
        <?php if ($lat && $lng): ?>
        <p class="description">
            <?php printf(__('Souřadnice: %s, %s | Stav: %s', SPD10_ORDINACE_TEXT_DOMAIN),
                esc_html($lat), esc_html($lng), esc_html($geocode_status)); ?>
            <br><?php _e('Klikněte na mapu pro ruční nastavení pozice nebo přetáhněte značku.', SPD10_ORDINACE_TEXT_DOMAIN); ?>
        </p>
        <?php else: ?>
        <p class="description">
            <?php _e('Mapa se zobrazí po geokódování adresy. Klikněte na mapu pro ruční nastavení pozice.', SPD10_ORDINACE_TEXT_DOMAIN); ?>
        </p>
        <?php endif; ?>
    </div>
</div>

<div class="spd10-ordinace-hidden-fields">
    <h4><?php _e('Systémová pole (pouze pro čtení)', SPD10_ORDINACE_TEXT_DOMAIN); ?></h4>
    
    <div class="spd10-ordinace-form-row">
        <label for="spd10_ordinace_source_row_id"><?php _e('ID řádku ze Sheets:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
        <input 
            type="text" 
            id="spd10_ordinace_source_row_id" 
            name="spd10_ordinace_source_row_id" 
            value="<?php echo esc_attr($source_row_id); ?>" 
            readonly
        />
    </div>
    
    <div class="spd10-ordinace-form-row">
        <label for="spd10_ordinace_updated_from_source_at"><?php _e('Poslední import:', SPD10_ORDINACE_TEXT_DOMAIN); ?></label>
        <input 
            type="text" 
            id="spd10_ordinace_updated_from_source_at" 
            name="spd10_ordinace_updated_from_source_at" 
            value="<?php echo esc_attr($updated_from_source_at); ?>" 
            readonly
        />
    </div>
    
    <input type="hidden" name="spd10_ordinace_geocode_status" value="<?php echo esc_attr($geocode_status); ?>" />
</div>

<!-- Map assets are now loaded via the map provider system -->
