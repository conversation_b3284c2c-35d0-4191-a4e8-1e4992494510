<?php
$adresa = get_post_meta($post->ID, '_ds_adresa', true);
$ico = get_post_meta($post->ID, '_ds_ico', true);
$datova_schranka = get_post_meta($post->ID, '_ds_datova_schranka', true);
$kontaktni_osoba = get_post_meta($post->ID, '_ds_kontaktni_osoba', true);
$telefon = get_post_meta($post->ID, '_ds_telefon', true);
$mail = get_post_meta($post->ID, '_ds_mail', true);

// Přidání nonce pole pro zabezpečení formuláře
wp_nonce_field('ds_save_provozovatel', 'ds_provozovatel_nonce');
?>
<style>
    .ds-form-row {
        display: flex;
        margin-bottom: 10px;
    }
    .ds-form-row label {
        flex: 0 0 30%;
        text-align: right;
        padding-right: 10px;
        line-height: 30px;
    }
    .ds-form-row input {
        flex: 0 0 70%;
        width: 100%;
    }
</style>
<div class="ds-form-row">
    <label for="ds_adresa">Adresa:</label>
    <input type="text" id="ds_adresa" name="ds_adresa" value="<?php echo esc_attr($adresa); ?>" />
</div>
<div class="ds-form-row">
    <label for="ds_ico">IČO:</label>
    <input type="text" id="ds_ico" name="ds_ico" value="<?php echo esc_attr($ico); ?>" />
</div>
<div class="ds-form-row">
    <label for="ds_datova_schranka">Datová schránka:</label>
    <input type="text" id="ds_datova_schranka" name="ds_datova_schranka" value="<?php echo esc_attr($datova_schranka); ?>" />
</div>
<div class="ds-form-row">
    <label for="ds_kontaktni_osoba">Kontaktní osoba:</label>
    <input type="text" id="ds_kontaktni_osoba" name="ds_kontaktni_osoba" value="<?php echo esc_attr($kontaktni_osoba); ?>" />
</div>
<div class="ds-form-row">
    <label for="ds_telefon">Telefon:</label>
    <input type="text" id="ds_telefon" name="ds_telefon" value="<?php echo esc_attr($telefon); ?>" />
</div>
<div class="ds-form-row">
    <label for="ds_mail">E-mail:</label>
    <input type="email" id="ds_mail" name="ds_mail" value="<?php echo esc_attr($mail); ?>" />
</div>
