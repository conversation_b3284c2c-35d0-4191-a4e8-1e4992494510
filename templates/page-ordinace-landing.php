<?php
/**
 * Page Template: Ordinace Landing - Professional Design
 * Template Name: Ordinace: <PERSON><PERSON><PERSON><PERSON> stránka
 * Theme override path: yourtheme/ordinace/page-ordinace-landing.php
 *
 * @package SPD10_Ordinace
 */

if (!defined('ABSPATH')) { exit; }
get_header();

// Get statistics
$total_ordinace = wp_count_posts('ordinace')->publish;
$pediatr_count = get_terms(array(
    'taxonomy' => 'ordinace_typ',
    'slug' => 'pediatr',
    'fields' => 'count'
));
$praktik_count = get_terms(array(
    'taxonomy' => 'ordinace_typ',
    'slug' => 'prakticky-lekar',
    'fields' => 'count'
));
?>

<div class="spd10-ordinace-landing-container">
    <div class="spd10-ordinace-landing-hero">
        <div class="spd10-ordinace-landing-hero-content">
            <h1 class="spd10-ordinace-landing-title">
                <?php _e('<PERSON>ékaři na Praze 10', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </h1>
            <p class="spd10-ordinace-landing-subtitle">
                <?php _e('Najděte svého lékaře rychle a jednoduše. Pediatři a praktičtí lékaři pro dospělé v jedné přehledné databázi.', SPD10_ORDINACE_TEXT_DOMAIN); ?>
            </p>
            <div class="spd10-ordinace-landing-stats">
                <div class="spd10-ordinace-landing-stat">
                    <span class="spd10-ordinace-landing-stat-number"><?php echo $total_ordinace; ?></span>
                    <span class="spd10-ordinace-landing-stat-label"><?php _e('Ordinací', SPD10_ORDINACE_TEXT_DOMAIN); ?></span>
                </div>
                <div class="spd10-ordinace-landing-stat">
                    <span class="spd10-ordinace-landing-stat-number">10</span>
                    <span class="spd10-ordinace-landing-stat-label"><?php _e('Čtvrtí', SPD10_ORDINACE_TEXT_DOMAIN); ?></span>
                </div>
                <div class="spd10-ordinace-landing-stat">
                    <span class="spd10-ordinace-landing-stat-number">2</span>
                    <span class="spd10-ordinace-landing-stat-label"><?php _e('Typy lékařů', SPD10_ORDINACE_TEXT_DOMAIN); ?></span>
                </div>
            </div>
        </div>
    </div>

    <div class="spd10-ordinace-landing-cards">
        <div class="spd10-ordinace-landing-card spd10-ordinace-landing-card-pediatr">
            <div class="spd10-ordinace-landing-card-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z"/>
                </svg>
            </div>
            <div class="spd10-ordinace-landing-card-content">
                <h2><?php _e('Pediatři', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
                <p><?php _e('Specializovaná péče pro děti a mladistvé. Najděte pediatra ve své čtvrti s dostupnými termíny.', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                <div class="spd10-ordinace-landing-card-features">
                    <span class="spd10-ordinace-landing-card-feature">👶 Péče pro děti</span>
                    <span class="spd10-ordinace-landing-card-feature">💉 Očkování</span>
                    <span class="spd10-ordinace-landing-card-feature">📋 Preventivní prohlídky</span>
                </div>
            </div>
            <div class="spd10-ordinace-landing-card-footer">
                <a href="<?php echo esc_url(add_query_arg('typ', 'pediatr', get_post_type_archive_link('ordinace'))); ?>" class="spd10-ordinace-landing-card-button">
                    <?php _e('Najít pediatra', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    <svg class="spd10-ordinace-landing-card-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                    </svg>
                </a>
            </div>
        </div>

        <div class="spd10-ordinace-landing-card spd10-ordinace-landing-card-praktik">
            <div class="spd10-ordinace-landing-card-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                </svg>
            </div>
            <div class="spd10-ordinace-landing-card-content">
                <h2><?php _e('Praktičtí lékaři', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
                <p><?php _e('Komplexní zdravotní péče pro dospělé. Praktičtí lékaři s dlouholetou praxí ve vaší oblasti.', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                <div class="spd10-ordinace-landing-card-features">
                    <span class="spd10-ordinace-landing-card-feature">🩺 Všeobecná péče</span>
                    <span class="spd10-ordinace-landing-card-feature">📊 Preventivní prohlídky</span>
                    <span class="spd10-ordinace-landing-card-feature">💊 Chronické nemoci</span>
                </div>
            </div>
            <div class="spd10-ordinace-landing-card-footer">
                <a href="<?php echo esc_url(add_query_arg('typ', 'prakticky-lekar', get_post_type_archive_link('ordinace'))); ?>" class="spd10-ordinace-landing-card-button">
                    <?php _e('Najít praktika', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    <svg class="spd10-ordinace-landing-card-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                    </svg>
                </a>
            </div>
        </div>

        <div class="spd10-ordinace-landing-card spd10-ordinace-landing-card-mapa">
            <div class="spd10-ordinace-landing-card-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"/>
                </svg>
            </div>
            <div class="spd10-ordinace-landing-card-content">
                <h2><?php _e('Interaktivní mapa', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
                <p><?php _e('Prohlédněte si všechny ordinace na interaktivní mapě Prahy 10. Najděte nejbližší ordinaci k vašemu bydlišti.', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
                <div class="spd10-ordinace-landing-card-features">
                    <span class="spd10-ordinace-landing-card-feature">🗺️ Interaktivní mapa</span>
                    <span class="spd10-ordinace-landing-card-feature">📍 Přesné lokace</span>
                    <span class="spd10-ordinace-landing-card-feature">🚶 Navigace</span>
                </div>
            </div>
            <div class="spd10-ordinace-landing-card-footer">
                <div class="spd10-ordinace-landing-card-button spd10-ordinace-landing-card-button-disabled">
                    <?php _e('Brzy dostupné', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                    <svg class="spd10-ordinace-landing-card-clock" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.7L16.2,16.2Z"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <div class="spd10-ordinace-landing-cta">
        <div class="spd10-ordinace-landing-cta-content">
            <h2><?php _e('Potřebujete rychle najít lékaře?', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
            <p><?php _e('Prohlédněte si kompletní seznam všech ordinací na Praze 10 s kontaktními údaji a informacemi o lékařích.', SPD10_ORDINACE_TEXT_DOMAIN); ?></p>
            <a href="<?php echo get_post_type_archive_link('ordinace'); ?>" class="spd10-ordinace-landing-cta-button">
                <?php _e('Zobrazit všechny ordinace', SPD10_ORDINACE_TEXT_DOMAIN); ?>
                <svg class="spd10-ordinace-landing-cta-arrow" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                </svg>
            </a>
        </div>
    </div>
</div>

<style>
/* Professional Landing Page Styles */
.spd10-ordinace-landing-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.spd10-ordinace-landing-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24px;
    padding: 80px 40px;
    margin-bottom: 60px;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.spd10-ordinace-landing-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
}

.spd10-ordinace-landing-hero-content {
    position: relative;
    z-index: 1;
}

.spd10-ordinace-landing-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin: 0 0 20px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    animation: slideDown 0.8s ease-out;
}

.spd10-ordinace-landing-subtitle {
    font-size: 1.3rem;
    margin: 0 0 40px 0;
    opacity: 0.95;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
    animation: slideUp 0.8s ease-out 0.2s both;
}

.spd10-ordinace-landing-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    animation: fadeIn 0.8s ease-out 0.4s both;
}

.spd10-ordinace-landing-stat {
    text-align: center;
}

.spd10-ordinace-landing-stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 5px;
    background: rgba(255,255,255,0.2);
    padding: 10px 20px;
    border-radius: 16px;
    backdrop-filter: blur(10px);
}

.spd10-ordinace-landing-stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

.spd10-ordinace-landing-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 80px;
}

.spd10-ordinace-landing-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

.spd10-ordinace-landing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0,0,0,0.15);
}

.spd10-ordinace-landing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.spd10-ordinace-landing-card:hover::before {
    transform: scaleX(1);
}

.spd10-ordinace-landing-card-pediatr .spd10-ordinace-landing-card-icon {
    color: #4fc3f7;
}

.spd10-ordinace-landing-card-praktik .spd10-ordinace-landing-card-icon {
    color: #9c27b0;
}

.spd10-ordinace-landing-card-mapa .spd10-ordinace-landing-card-icon {
    color: #4caf50;
}

.spd10-ordinace-landing-card-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 25px;
    animation: bounce 2s ease-in-out infinite;
}

.spd10-ordinace-landing-card-icon svg {
    width: 100%;
    height: 100%;
}

.spd10-ordinace-landing-card h2 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 15px 0;
    color: #2d3748;
}

.spd10-ordinace-landing-card p {
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 25px;
    font-size: 1rem;
}

.spd10-ordinace-landing-card-features {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 30px;
}

.spd10-ordinace-landing-card-feature {
    font-size: 0.9rem;
    color: #4a5568;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.spd10-ordinace-landing-card-feature:last-child {
    border-bottom: none;
}

.spd10-ordinace-landing-card-footer {
    margin-top: auto;
}

.spd10-ordinace-landing-card-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    padding: 15px 25px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.spd10-ordinace-landing-card-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

.spd10-ordinace-landing-card-button-disabled {
    background: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
}

.spd10-ordinace-landing-card-button-disabled:hover {
    transform: none;
    box-shadow: none;
}

.spd10-ordinace-landing-card-arrow,
.spd10-ordinace-landing-card-clock {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.spd10-ordinace-landing-card-button:hover .spd10-ordinace-landing-card-arrow {
    transform: translateX(4px);
}

.spd10-ordinace-landing-cta {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 20px;
    padding: 60px 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.spd10-ordinace-landing-cta::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    animation: float 10s ease-in-out infinite reverse;
}

.spd10-ordinace-landing-cta-content {
    position: relative;
    z-index: 1;
}

.spd10-ordinace-landing-cta h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0 0 15px 0;
    color: #2d3748;
}

.spd10-ordinace-landing-cta p {
    font-size: 1.1rem;
    color: #4a5568;
    margin: 0 0 30px 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.spd10-ordinace-landing-cta-button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 18px 35px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-decoration: none;
    border-radius: 16px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.spd10-ordinace-landing-cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

.spd10-ordinace-landing-cta-arrow {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.spd10-ordinace-landing-cta-button:hover .spd10-ordinace-landing-cta-arrow {
    transform: translateX(4px);
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .spd10-ordinace-landing-container {
        padding: 15px;
    }

    .spd10-ordinace-landing-hero {
        padding: 50px 25px;
        margin-bottom: 40px;
    }

    .spd10-ordinace-landing-title {
        font-size: 2.5rem;
    }

    .spd10-ordinace-landing-subtitle {
        font-size: 1.1rem;
    }

    .spd10-ordinace-landing-stats {
        gap: 20px;
        flex-wrap: wrap;
    }

    .spd10-ordinace-landing-cards {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 50px;
    }

    .spd10-ordinace-landing-card {
        padding: 30px 25px;
    }

    .spd10-ordinace-landing-cta {
        padding: 40px 25px;
    }

    .spd10-ordinace-landing-cta h2 {
        font-size: 1.8rem;
    }
}

/* Staggered animations */
.spd10-ordinace-landing-card:nth-child(1) { animation-delay: 0.1s; }
.spd10-ordinace-landing-card:nth-child(2) { animation-delay: 0.2s; }
.spd10-ordinace-landing-card:nth-child(3) { animation-delay: 0.3s; }
</style>

<?php get_footer(); ?>

