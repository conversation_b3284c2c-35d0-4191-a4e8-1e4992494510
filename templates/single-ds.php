<?php get_header(); ?>

<div class="ds-single-container">
    <div class="ds-single-header">
        <div class="ds-title-container">
            <h1 class="ds-title"><?php the_title(); ?></h1>
            <div class="ds-title-decoration"></div>
        </div>
        <!-- Tlačítko pro tisk odstraněno -->
    </div>

    <div class="ds-content-wrapper">
        <div class="ds-main-content">
            <div class="ds-media-container">
                <div class="ds-image-wrapper">
                    <?php if (has_post_thumbnail()): ?>
                        <img src="<?php the_post_thumbnail_url('large'); ?>" class="ds-main-image" alt="<?php the_title_attribute(); ?>">
                    <?php else: ?>
                        <?php
                        $default_image_id = get_option('ds_default_image_id');
                        $default_image_url = $default_image_id ? wp_get_attachment_url($default_image_id) : esc_url(plugin_dir_url(dirname(__FILE__)) . 'assets/images/default-ds-image.jpg');
                        ?>
                        <img src="<?php echo esc_url($default_image_url); ?>" class="ds-main-image" alt="Default Image">
                    <?php endif; ?>
                    <div class="ds-image-overlay">
                        <div class="ds-image-caption"><?php the_title(); ?></div>
                    </div>
                </div>

                <?php if (!empty(get_the_content())): ?>
                <div class="ds-content-text">
                    <?php the_content(); ?>
                </div>
                <?php endif; ?>
            </div>

            <!-- Detaily dětské skupiny -->
            <div class="ds-details-section">
                <h2 class="ds-section-title">Detaily dětské skupiny</h2>
                <div class="ds-details-decoration"></div>

                <?php
                // Definice detailů dětské skupiny
                $details = array(
                    'Kontaktní osoba' => '_ds_kontaktni_osoba',
                    'Kontakt na KO' => '_ds_kontakt_na_ko',
                    'Čtvrť' => '_ds_ctvrt',
                    'Adresa' => '_ds_adresa',
                    'Kapacita' => '_ds_kapacita',
                    'Věk dětí' => '_ds_vek_deti',
                    'Zápis' => '_ds_zapis',
                    'Registrace MPSV' => '_ds_registrace_mpsv',
                    'Webové stránky' => '_ds_webove_stranky',
                    'Specifické informace' => '_ds_specificke_informace',
                    'Cena za celotýdenní docházku' => '_ds_cena',
                    'Otevírací doba' => '_ds_oteviraci_doba',
                    'Provoz celoroční' => '_ds_provoz_celocelocni',
                    'Provoz od' => '_ds_provoz_od',
                    'Veřejná/pro zaměstnance' => '_ds_verejna_pro_zamestnance',
                    'Email' => '_ds_email',
                    'Provozovatel' => '_ds_provozovatel_id' // Přesunuto na konec seznamu
                );

                // Rozdělení detailů do dvou skupin pro lepší zobrazení
                $details_left = array();
                $details_right = array();
                $count = 0;
                $total = 0;

                // Počítáme pouze nepprázdné hodnoty
                foreach ($details as $label => $meta_key) {
                    if ($meta_key === '_ds_provozovatel_id') {
                        $value = get_post_meta(get_the_ID(), $meta_key, true);
                        if (!empty($value)) $total++;
                    } else {
                        $value = get_post_meta(get_the_ID(), $meta_key, true);
                        if (!empty($value)) $total++;
                    }
                }

                $half = ceil($total / 2);
                $has_details = false;

                foreach ($details as $label => $meta_key) {
                    if ($meta_key === '_ds_provozovatel_id') {
                        $provozovatel_id = get_post_meta(get_the_ID(), $meta_key, true);
                        if ($provozovatel_id) {
                            $has_details = true;
                            $detail_item = '<div class="ds-detail-item"><div class="ds-detail-label">' . esc_html($label) . '</div><div class="ds-detail-value"><a href="' . esc_url(get_permalink($provozovatel_id)) . '">' . esc_html(get_the_title($provozovatel_id)) . '</a></div></div>';

                            if ($count < $half) {
                                $details_left[] = $detail_item;
                            } else {
                                $details_right[] = $detail_item;
                            }
                            $count++;
                        }
                    } else {
                        $value = get_post_meta(get_the_ID(), $meta_key, true);
                        // Zobrazit pouze vyplněné atributy (neprázdné hodnoty)
                        if (!empty($value)) {
                            $has_details = true;

                            if ($meta_key === '_ds_webove_stranky') {
                                $detail_item = '<div class="ds-detail-item"><div class="ds-detail-label">' . esc_html($label) . '</div><div class="ds-detail-value"><a href="' . esc_url($value) . '" target="_blank">' . esc_html($value) . '</a></div></div>';
                            } elseif ($meta_key === '_ds_specificke_informace') {
                                $detail_item = '<div class="ds-detail-item"><div class="ds-detail-label">' . esc_html($label) . '</div><div class="ds-detail-value">' . nl2br(esc_html($value)) . '</div></div>';
                            } else {
                                $detail_item = '<div class="ds-detail-item"><div class="ds-detail-label">' . esc_html($label) . '</div><div class="ds-detail-value">' . esc_html($value) . '</div></div>';
                            }

                            if ($count < $half) {
                                $details_left[] = $detail_item;
                            } else {
                                $details_right[] = $detail_item;
                            }
                            $count++;
                        }
                    }
                }

                if ($has_details) :
                ?>
                <div class="ds-details-grid">
                    <div class="ds-details-column">
                        <?php echo implode('', $details_left); ?>
                    </div>
                    <div class="ds-details-column">
                        <?php echo implode('', $details_right); ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>


            <?php
            $gallery_ids = get_post_meta(get_the_ID(), '_ds_galerie', true);
            if ($gallery_ids) :
                $gallery_array = explode(',', $gallery_ids);
            ?>
            <div class="ds-gallery-section print-hide">
                <h2 class="ds-section-title">Fotogalerie</h2>
                <div class="ds-details-decoration"></div>
                <div class="ds-gallery-grid">
                    <?php
                    foreach ($gallery_array as $image_id) {
                        $image_url = wp_get_attachment_image_url($image_id, 'large');
                        $image_thumb = wp_get_attachment_image_url($image_id, 'thumbnail');
                        $image_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);
                        $image_caption = wp_get_attachment_caption($image_id);
                    ?>
                    <div class="ds-gallery-item">
                        <a href="<?php echo esc_url($image_url); ?>" data-lightbox="ds-gallery" data-title="<?php echo esc_attr($image_caption); ?>" class="ds-gallery-link">
                            <div class="ds-gallery-image-container">
                                <img src="<?php echo esc_url($image_thumb); ?>" alt="<?php echo esc_attr($image_alt); ?>" class="ds-gallery-image">
                                <div class="ds-gallery-overlay">
                                    <div class="ds-gallery-zoom"><i class="fas fa-search-plus"></i></div>
                                </div>
                            </div>
                            <?php if ($image_caption) : ?>
                            <div class="ds-gallery-caption"><?php echo esc_html($image_caption); ?></div>
                            <?php endif; ?>
                        </a>
                    </div>
                    <?php } ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <div class="ds-sidebar">
            <div class="ds-map-section">
                <h2 class="ds-section-title">Lokalita</h2>
                <div class="ds-details-decoration"></div>
                <div class="ds-map-container">
                    <div id="ds-map-sidebar"></div>
                    <div class="ds-map-caption">
                        <p>Mapa zobrazuje lokalitu dětské skupiny. Pro přesné informace o umístění kontaktujte provozovatele.</p>
                    </div>
                </div>
            </div>

            <div class="ds-info-section">
                <div class="ds-info-content">
                    <p>Za aktuálnost údajů odpovídá vždy daný provozovatel. Pro konkrétnější informace kontaktujte prosím provozovatele dětských skupin.</p>
                    <p>Tento rozcestník vznikl v rámci aktivit naplňování <a href="https://strategieprodesitku.cz/rodinna-politika-v-akci/">Koncepce rodinné politiky v Praze 10</a>.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>

<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

<style>
/* Moderní styly pro dětské skupiny */
:root {
    --primary-color: #4e73df;
    --primary-gradient: linear-gradient(45deg, #4e73df, #36b9cc);
    --secondary-color: #1cc88a;
    --secondary-gradient: linear-gradient(45deg, #1cc88a, #36b9cc);
    --accent-color: #f6c23e;
    --text-color: #5a5c69;
    --light-bg: #f8f9fc;
    --card-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    --hover-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.25);
    --border-radius: 0.35rem;
    --transition-speed: 0.3s;
}

/* Základní styly */
body {
    color: var(--text-color);
    background-color: var(--light-bg);
}

.ds-single-container {
    max-width: 1200px;
    margin: 3rem auto;
    padding: 0 1rem;
}

/* Hlavička */
.ds-single-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.ds-title-container {
    position: relative;
}

.ds-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.ds-title-decoration {
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* Hlavní obsah a postranní panel */
.ds-content-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.ds-main-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.ds-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Obrázek a media */
.ds-media-container {
    padding: 1.5rem;
}

.ds-image-wrapper {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 1.5rem;
    box-shadow: var(--card-shadow);
}

.ds-main-image {
    width: 100%;
    height: auto;
    display: block;
    transition: transform var(--transition-speed) ease;
}

.ds-image-wrapper:hover .ds-main-image {
    transform: scale(1.03);
}

.ds-image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
    padding: 1.5rem;
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
}

.ds-image-wrapper:hover .ds-image-overlay {
    opacity: 1;
}

.ds-image-caption {
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
}

.ds-content-text {
    line-height: 1.6;
}

/* Sekce detailů */
.ds-details-section, .ds-gallery-section, .ds-map-section, .ds-info-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
}

.ds-section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.ds-details-decoration {
    width: 50px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
    margin-bottom: 1.5rem;
}

.ds-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.ds-detail-item {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px dashed rgba(0,0,0,0.1);
    transition: all var(--transition-speed) ease;
}

.ds-detail-item:hover {
    transform: translateX(5px);
    border-bottom-color: var(--primary-color);
}

.ds-detail-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.ds-detail-value {
    color: var(--text-color);
}

.ds-detail-value a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color var(--transition-speed) ease;
}

.ds-detail-value a:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

/* Galerie */
.ds-gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.ds-gallery-item {
    transition: all var(--transition-speed) ease;
}

.ds-gallery-item:hover {
    transform: translateY(-5px);
}

.ds-gallery-image-container {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    aspect-ratio: 1 / 1;
}

.ds-gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-speed) ease;
}

.ds-gallery-item:hover .ds-gallery-image {
    transform: scale(1.1);
}

.ds-gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
}

.ds-gallery-item:hover .ds-gallery-overlay {
    opacity: 1;
}

.ds-gallery-zoom {
    color: white;
    font-size: 2rem;
}

.ds-gallery-caption {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-color);
    text-align: center;
}

/* Mapa */
.ds-map-container {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

#ds-map-sidebar {
    height: 300px;
    width: 100%;
}

.ds-map-caption {
    padding: 0.75rem;
    font-size: 0.9rem;
    color: var(--text-color);
    background: var(--light-bg);
    border-top: 1px solid rgba(0,0,0,0.05);
}

/* Info sekce */
.ds-info-section {
    background: var(--light-bg);
    border-left: 4px solid var(--accent-color);
}

.ds-info-content {
    font-size: 0.9rem;
    line-height: 1.6;
}

.ds-info-content a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-speed) ease;
}

.ds-info-content a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

/* Skryté prvky pro tisk */
.ds-print-map-container, .ds-print-footer {
    display: none;
}

/* Animace při scrollování */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Postupné animace */
.ds-details-section.animated {
    transition-delay: 0.1s;
}

.ds-gallery-section.animated {
    transition-delay: 0.3s;
}

.ds-map-section.animated {
    transition-delay: 0.1s;
}

.ds-info-section.animated {
    transition-delay: 0.3s;
}

/* Responzivní design */
@media (max-width: 992px) {
    .ds-content-wrapper {
        grid-template-columns: 1fr;
    }

    .ds-single-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .ds-details-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var lat = <?php echo esc_js(get_post_meta(get_the_ID(), '_ds_lat', true)); ?>;
    var lon = <?php echo esc_js(get_post_meta(get_the_ID(), '_ds_lon', true)); ?>;

    // Funkce pro vytvoření mapy s animací
    function createMap(elementId, lat, lon, zoom, withAnimation) {
        var map = L.map(elementId).setView([lat, lon], zoom);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        var marker = L.marker([lat, lon], {
            opacity: withAnimation ? 0 : 1
        }).addTo(map);

        if (withAnimation) {
            // Animace markeru
            setTimeout(function() {
                marker.setOpacity(1);
                marker.bindPopup('<?php echo esc_js(get_the_title()); ?>');
                setTimeout(function() {
                    marker.openPopup();
                }, 500);
            }, 500);
        } else {
            marker.bindPopup('<?php echo esc_js(get_the_title()); ?>');
            marker.openPopup();
        }

        return map;
    }

    // Mapa v postranním panelu (pro zobrazení na webu)
    var sidebarMap = createMap('ds-map-sidebar', lat, lon, 15, true);

    // Animace při scrollování
    function animateOnScroll() {
        var elements = document.querySelectorAll('.ds-details-section, .ds-gallery-section, .ds-map-section, .ds-info-section');

        elements.forEach(function(element) {
            var position = element.getBoundingClientRect();

            // Pokud je prvek viditelný
            if(position.top < window.innerHeight && position.bottom >= 0) {
                element.classList.add('animated');
            }
        });
    }

    // Přidání třídy pro animaci
    document.querySelectorAll('.ds-details-section, .ds-gallery-section, .ds-map-section, .ds-info-section').forEach(function(element) {
        element.classList.add('animate-on-scroll');
    });

    // Spouštění animace při scrollování
    window.addEventListener('scroll', animateOnScroll);
    animateOnScroll(); // Spustit při načtení stránky
});
</script>

<?php get_footer(); ?>
