<?php
/**
 * Single Template: Ordinace - Professional Design
 * Theme override path: yourtheme/ordinace/single-ordinace.php
 *
 * @package SPD10_Ordinace
 */

if (!defined('ABSPATH')) { exit; }

get_header();

if (have_posts()) {
    the_post();
} else {
    echo '<p>Post not found</p>';
    get_footer();
    return;
}

// Get meta data
$meta = array(
    'doctors_names' => get_post_meta(get_the_ID(), '_spd10_ordinace_doctors_names', true),
    'organization' => get_post_meta(get_the_ID(), '_spd10_ordinace_organization', true),
    'quarter' => get_post_meta(get_the_ID(), '_spd10_ordinace_quarter', true),
    'address' => get_post_meta(get_the_ID(), '_spd10_ordinace_address', true),
    'email' => get_post_meta(get_the_ID(), '_spd10_ordinace_email', true),
    'phone' => get_post_meta(get_the_ID(), '_spd10_ordinace_phone', true),
    'web' => get_post_meta(get_the_ID(), '_spd10_ordinace_website', true),
    'lat' => get_post_meta(get_the_ID(), '_spd10_ordinace_lat', true),
    'lng' => get_post_meta(get_the_ID(), '_spd10_ordinace_lng', true),
);

// Get ordinace type for illustration
$terms = get_the_terms(get_the_ID(), 'ordinace_typ');
$ordinace_type = $terms && !is_wp_error($terms) ? $terms[0]->slug : 'general';

// Function to get illustration based on type
function get_ordinace_illustration($type) {
    $illustrations = array(
        'pediatr' => 'data:image/svg+xml;base64,' . base64_encode('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#e0f2fe"/><stop offset="100%" style="stop-color:#b3e5fc"/></linearGradient></defs><rect width="200" height="200" fill="url(#bg)"/><circle cx="100" cy="80" r="25" fill="#ffb74d"/><rect x="85" y="105" width="30" height="40" rx="15" fill="#4fc3f7"/><rect x="75" y="125" width="15" height="30" rx="7" fill="#ffb74d"/><rect x="110" y="125" width="15" height="30" rx="7" fill="#ffb74d"/><circle cx="92" cy="75" r="3" fill="#333"/><circle cx="108" cy="75" r="3" fill="#333"/><path d="M95 85 Q100 90 105 85" stroke="#333" stroke-width="2" fill="none"/><rect x="40" y="160" width="120" height="8" rx="4" fill="#4caf50"/><text x="100" y="185" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Pediatrie</text></svg>'),
        'prakticky-lekar' => 'data:image/svg+xml;base64,' . base64_encode('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#f3e5f5"/><stop offset="100%" style="stop-color:#e1bee7"/></linearGradient></defs><rect width="200" height="200" fill="url(#bg)"/><circle cx="100" cy="70" r="25" fill="#ffb74d"/><rect x="85" y="95" width="30" height="50" rx="15" fill="#2196f3"/><rect x="75" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><rect x="110" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><circle cx="92" cy="65" r="3" fill="#333"/><circle cx="108" cy="65" r="3" fill="#333"/><path d="M95 75 Q100 80 105 75" stroke="#333" stroke-width="2" fill="none"/><rect x="90" y="50" width="20" height="4" rx="2" fill="#fff"/><rect x="40" y="170" width="120" height="8" rx="4" fill="#9c27b0"/><text x="100" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Praktický lékař</text></svg>'),
        'general' => 'data:image/svg+xml;base64,' . base64_encode('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#e8f5e8"/><stop offset="100%" style="stop-color:#c8e6c9"/></linearGradient></defs><rect width="200" height="200" fill="url(#bg)"/><circle cx="100" cy="70" r="25" fill="#ffb74d"/><rect x="85" y="95" width="30" height="50" rx="15" fill="#4caf50"/><rect x="75" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><rect x="110" y="125" width="15" height="35" rx="7" fill="#ffb74d"/><circle cx="92" cy="65" r="3" fill="#333"/><circle cx="108" cy="65" r="3" fill="#333"/><path d="M95 75 Q100 80 105 75" stroke="#333" stroke-width="2" fill="none"/><rect x="95" y="105" width="10" height="2" fill="#fff"/><rect x="99" y="101" width="2" height="10" fill="#fff"/><rect x="40" y="170" width="120" height="8" rx="4" fill="#4caf50"/><text x="100" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Ordinace</text></svg>')
    );

    return isset($illustrations[$type]) ? $illustrations[$type] : $illustrations['general'];
}
?>

<div class="spd10-ordinace-single-container">
    <div class="spd10-ordinace-hero">
        <div class="spd10-ordinace-hero-content">
            <div class="spd10-ordinace-hero-illustration">
                <img src="<?php echo get_ordinace_illustration($ordinace_type); ?>" alt="<?php echo esc_attr($ordinace_type); ?>" class="spd10-ordinace-illustration">
            </div>
            <div class="spd10-ordinace-hero-info">
                <h1 class="spd10-ordinace-title"><?php the_title(); ?></h1>
                <?php if (!empty($meta['quarter'])) : ?>
                    <div class="spd10-ordinace-location">
                        <svg class="spd10-ordinace-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                        </svg>
                        <?php echo esc_html($meta['quarter']); ?>
                    </div>
                <?php endif; ?>
                <?php if ($terms && !is_wp_error($terms)) : ?>
                    <div class="spd10-ordinace-type-badge">
                        <?php echo esc_html($terms[0]->name); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="spd10-ordinace-content">
        <?php if (!empty($meta['doctors_names'])) : ?>
            <div class="spd10-ordinace-card spd10-ordinace-doctors-card">
                <div class="spd10-ordinace-card-header">
                    <svg class="spd10-ordinace-card-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z"/>
                    </svg>
                    <h2><?php _e('Lékaři', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
                </div>
                <div class="spd10-ordinace-doctors-list">
                    <?php foreach (array_filter(array_map('trim', explode("\n", $meta['doctors_names']))) as $index => $name) : ?>
                        <div class="spd10-ordinace-doctor-item" style="animation-delay: <?php echo $index * 0.1; ?>s">
                            <div class="spd10-ordinace-doctor-avatar">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                </svg>
                            </div>
                            <span class="spd10-ordinace-doctor-name"><?php echo esc_html($name); ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <div class="spd10-ordinace-card spd10-ordinace-contact-card">
            <div class="spd10-ordinace-card-header">
                <svg class="spd10-ordinace-card-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
                <h2><?php _e('Kontaktní informace', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
            </div>
            <div class="spd10-ordinace-contact-grid">
                <?php if (!empty($meta['organization'])) : ?>
                    <div class="spd10-ordinace-contact-item">
                        <svg class="spd10-ordinace-contact-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"/>
                        </svg>
                        <div>
                            <strong><?php _e('Organizace', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong>
                            <span><?php echo esc_html($meta['organization']); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($meta['address'])) : ?>
                    <div class="spd10-ordinace-contact-item">
                        <svg class="spd10-ordinace-contact-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                        </svg>
                        <div>
                            <strong><?php _e('Adresa', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong>
                            <span><?php echo esc_html($meta['address']); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($meta['phone'])) : ?>
                    <div class="spd10-ordinace-contact-item">
                        <svg class="spd10-ordinace-contact-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"/>
                        </svg>
                        <div>
                            <strong><?php _e('Telefon', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong>
                            <a href="tel:<?php echo esc_attr(preg_replace('/\s+/', '', $meta['phone'])); ?>" class="spd10-ordinace-contact-link"><?php echo esc_html($meta['phone']); ?></a>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($meta['email'])) : ?>
                    <div class="spd10-ordinace-contact-item">
                        <svg class="spd10-ordinace-contact-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                        <div>
                            <strong><?php _e('E-mail', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong>
                            <a href="mailto:<?php echo esc_attr($meta['email']); ?>" class="spd10-ordinace-contact-link"><?php echo esc_html($meta['email']); ?></a>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($meta['web'])) : ?>
                    <div class="spd10-ordinace-contact-item">
                        <svg class="spd10-ordinace-contact-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                        </svg>
                        <div>
                            <strong><?php _e('Webové stránky', SPD10_ORDINACE_TEXT_DOMAIN); ?></strong>
                            <a href="<?php echo esc_url($meta['web']); ?>" target="_blank" rel="noopener noreferrer" class="spd10-ordinace-contact-link"><?php echo esc_html($meta['web']); ?></a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <?php if (!empty($meta['lat']) && !empty($meta['lng'])) : ?>
            <div class="spd10-ordinace-card spd10-ordinace-map-card">
                <div class="spd10-ordinace-card-header">
                    <svg class="spd10-ordinace-card-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .*********.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"/>
                    </svg>
                    <h2><?php _e('Poloha', SPD10_ORDINACE_TEXT_DOMAIN); ?></h2>
                </div>
                <div id="spd10-ordinace-detail-map" class="spd10-ordinace-map-container"></div>
            </div>

            <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
            <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
            <script>
                document.addEventListener('DOMContentLoaded', function () {
                    if (typeof L !== 'undefined') {
                        var map = L.map('spd10-ordinace-detail-map').setView([<?php echo esc_js($meta['lat']); ?>, <?php echo esc_js($meta['lng']); ?>], 16);
                        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            attribution: '&copy; OpenStreetMap contributors'
                        }).addTo(map);
                        L.marker([<?php echo esc_js($meta['lat']); ?>, <?php echo esc_js($meta['lng']); ?>]).addTo(map).bindPopup('<?php echo esc_js(get_the_title()); ?>');
                    }
                });
            </script>
        <?php endif; ?>
    </div>
</div>

<style>
/* Professional Ordinace Single Page Styles */
.spd10-ordinace-single-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.spd10-ordinace-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 30px;
    color: white;
    position: relative;
    overflow: hidden;
}

.spd10-ordinace-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.spd10-ordinace-hero-content {
    display: flex;
    align-items: center;
    gap: 30px;
    position: relative;
    z-index: 1;
}

.spd10-ordinace-hero-illustration {
    flex-shrink: 0;
}

.spd10-ordinace-illustration {
    width: 120px;
    height: 120px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.spd10-ordinace-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 15px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.spd10-ordinace-location {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.spd10-ordinace-icon {
    width: 20px;
    height: 20px;
}

.spd10-ordinace-type-badge {
    display: inline-block;
    background: rgba(255,255,255,0.2);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
    text-transform: none;
}

.spd10-ordinace-content {
    display: grid;
    gap: 25px;
}

.spd10-ordinace-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.spd10-ordinace-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.spd10-ordinace-card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.spd10-ordinace-card-icon {
    width: 28px;
    height: 28px;
    color: #667eea;
}

.spd10-ordinace-card h2 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    color: #2d3748;
}

.spd10-ordinace-doctors-list {
    display: grid;
    gap: 15px;
}

.spd10-ordinace-doctor-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 12px;
    transition: all 0.3s ease;
    animation: fadeInLeft 0.6s ease-out;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.spd10-ordinace-doctor-item:hover {
    background: #e2e8f0;
    transform: translateX(5px);
}

.spd10-ordinace-doctor-avatar {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.spd10-ordinace-doctor-avatar svg {
    width: 24px;
    height: 24px;
}

.spd10-ordinace-doctor-name {
    font-weight: 500;
    color: #2d3748;
    font-size: 1.1rem;
}

.spd10-ordinace-contact-grid {
    display: grid;
    gap: 20px;
}

.spd10-ordinace-contact-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.spd10-ordinace-contact-item:hover {
    background: #e2e8f0;
    transform: translateX(5px);
}

.spd10-ordinace-contact-icon {
    width: 24px;
    height: 24px;
    color: #667eea;
    flex-shrink: 0;
    margin-top: 2px;
}

.spd10-ordinace-contact-item div {
    flex: 1;
}

.spd10-ordinace-contact-item strong {
    display: block;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
    font-size: 0.9rem;
}

.spd10-ordinace-contact-item span {
    color: #4a5568;
    font-size: 1rem;
}

.spd10-ordinace-contact-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.spd10-ordinace-contact-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

.spd10-ordinace-map-container {
    height: 350px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .spd10-ordinace-single-container {
        padding: 15px;
    }

    .spd10-ordinace-hero {
        padding: 25px;
    }

    .spd10-ordinace-hero-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .spd10-ordinace-title {
        font-size: 2rem;
    }

    .spd10-ordinace-card {
        padding: 20px;
    }

    .spd10-ordinace-illustration {
        width: 100px;
        height: 100px;
    }
}

/* Loading animation for cards */
.spd10-ordinace-card:nth-child(1) { animation-delay: 0.1s; }
.spd10-ordinace-card:nth-child(2) { animation-delay: 0.2s; }
.spd10-ordinace-card:nth-child(3) { animation-delay: 0.3s; }
</style>

<?php get_footer(); ?>

