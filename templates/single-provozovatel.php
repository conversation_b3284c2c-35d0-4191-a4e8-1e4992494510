<?php get_header(); ?>

<div class="container my-5">
    <div class="row">
        <div class="col-md-8">
            <article id="post-<?php the_ID(); ?>" <?php post_class('bg-white p-4 shadow-sm rounded'); ?>>
                <h1 class="entry-title mb-4"><?php the_title(); ?></h1>

                <?php if (has_post_thumbnail()): ?>
                    <img src="<?php the_post_thumbnail_url('large'); ?>" class="img-fluid mb-4" alt="<?php the_title_attribute(); ?>">
                <?php else: ?>
                    <?php
                    $default_image_id = get_option('provozovatel_default_image_id');
                    $default_image_url = $default_image_id ? wp_get_attachment_url($default_image_id) : esc_url(plugin_dir_url(dirname(__FILE__)) . 'assets/images/default-provozovatel-image.jpg');
                    ?>
                    <img src="<?php echo esc_url($default_image_url); ?>" class="img-fluid mb-4" alt="Default Image">
                <?php endif; ?>

                <?php if (!empty(get_the_content())): ?>
                <div class="entry-content mb-4">
                    <?php the_content(); ?>
                </div>
                <?php endif; ?>

                <?php
                $details = array(
                    'IČO' => '_ds_ico',
                    'Adresa' => '_ds_adresa',
                    'Datová schránka' => '_ds_datova_schranka',
                    'Kontaktní osoba' => '_ds_kontaktni_osoba',
                    'Telefon' => '_ds_telefon',
                    'E-mail' => '_ds_mail',
                    'Web' => '_ds_web'
                );

                $has_details = false;
                $details_output = '';

                foreach ($details as $label => $meta_key) {
                    $value = get_post_meta(get_the_ID(), $meta_key, true);
                    if (!empty($value)) {
                        $has_details = true;
                        if ($meta_key === '_ds_mail') {
                            $details_output .= '<li class="list-group-item"><strong>' . esc_html($label) . ':</strong> <a href="mailto:' . esc_attr($value) . '">' . esc_html($value) . '</a></li>';
                        } elseif ($meta_key === '_ds_web') {
                            $details_output .= '<li class="list-group-item"><strong>' . esc_html($label) . ':</strong> <a href="' . esc_url($value) . '" target="_blank">' . esc_html($value) . '</a></li>';
                        } else {
                            $details_output .= '<li class="list-group-item"><strong>' . esc_html($label) . ':</strong> ' . esc_html($value) . '</li>';
                        }
                    }
                }

                if ($has_details) :
                ?>
                <div class="provozovatel-details mt-4">
                    <h2 class="h4 mb-3">Detaily provozovatele</h2>
                    <ul class="list-group">
                        <?php echo $details_output; ?>
                    </ul>
                </div>
                <?php endif; ?>
            </article>
        </div>
        <div class="col-md-4">
            <div class="bg-white p-4 shadow-sm rounded">
                <h2 class="h4 mb-3">Dětské skupiny provozovatele</h2>
                <?php
                $args = array(
                    'post_type' => 'ds',
                    'posts_per_page' => -1,
                    'meta_query' => array(
                        array(
                            'key' => '_ds_provozovatel_id',
                            'value' => get_the_ID(),
                        ),
                    ),
                );
                $related_groups = new WP_Query($args);
                if ($related_groups->have_posts()) :
                ?>
                    <ul class="list-group">
                        <?php while ($related_groups->have_posts()) : $related_groups->the_post(); ?>
                            <li class="list-group-item">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </li>
                        <?php endwhile; ?>
                    </ul>
                <?php else : ?>
                    <p>Tento provozovatel nemá žádné dětské skupiny.</p>
                <?php
                endif;
                wp_reset_postdata();
                ?>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>
