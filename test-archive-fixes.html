<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Oprav Archivní <PERSON></title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #2196f3;
        }
        .fix-list {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        .fix-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .fix-list li {
            margin: 5px 0;
        }
        
        /* Demo filtry */
        .demo-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
        }
        .demo-filter-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .demo-filter-item label {
            font-weight: 600;
            color: #2d3748;
            font-size: 0.9rem;
        }
        .demo-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }
        .demo-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        /* Demo stránkování */
        .demo-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            gap: 8px;
            flex-wrap: wrap;
        }
        .demo-page-number {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 10px 14px;
            min-width: 44px;
            background: white;
            color: #4a5568;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            border: 2px solid #e2e8f0;
            box-sizing: border-box;
            cursor: pointer;
        }
        .demo-page-number:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .demo-page-number.current {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            cursor: default;
        }
        .demo-page-number.current:hover {
            transform: none;
        }
        .demo-page-number.dots {
            border: none;
            background: transparent;
            color: #a0aec0;
            cursor: default;
            padding: 10px 8px;
        }
        .demo-page-number.dots:hover {
            background: transparent;
            color: #a0aec0;
            transform: none;
            box-shadow: none;
        }
        .demo-page-number.prev,
        .demo-page-number.next {
            padding: 10px 16px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }
        .before {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        .after {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test Oprav Archivní Stránky</h1>
        
        <div class="test-info">
            <strong>Opravené problémy v archivní stránce ordinací:</strong>
        </div>
        
        <div class="fix-list">
            <h3>✅ Opravené problémy:</h3>
            <ul>
                <li><strong>Filtr podle typu:</strong> Opraven slug z "prakticky-lekar" na "praktik-dospeli"</li>
                <li><strong>Filtr podle lokality:</strong> Změněn z input na select box s dynamickými hodnotami</li>
                <li><strong>Stránkování:</strong> Opravena strategie číslování a odstraněny tečky jako seznam</li>
                <li><strong>Parametry filtrů:</strong> Přidáno zachování filtrů při stránkování</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>Test 1: Opravené Filtry</h2>
            
            <div class="comparison">
                <div class="before">
                    <h4>❌ PŘED opravou</h4>
                    <ul>
                        <li>Typ: "prakticky-lekar" (nefungoval)</li>
                        <li>Čtvrť: text input (nepohodlné)</li>
                        <li>Filtry se ztratily při stránkování</li>
                    </ul>
                </div>
                <div class="after">
                    <h4>✅ PO opravě</h4>
                    <ul>
                        <li>Typ: "praktik-dospeli" (funguje)</li>
                        <li>Čtvrť: select box (pohodlné)</li>
                        <li>Filtry se zachovávají při stránkování</li>
                    </ul>
                </div>
            </div>
            
            <h3>Demo opravených filtrů:</h3>
            <form class="demo-filters">
                <div class="demo-filter-item">
                    <label>Typ ordinace</label>
                    <select class="demo-select">
                        <option value="">Všechny typy</option>
                        <option value="pediatr">Pediatr</option>
                        <option value="praktik-dospeli">Praktický lékař</option>
                    </select>
                </div>
                
                <div class="demo-filter-item">
                    <label>Čtvrť</label>
                    <select class="demo-select">
                        <option value="">Všechny čtvrti</option>
                        <option value="Vinohrady">Vinohrady</option>
                        <option value="Vršovice">Vršovice</option>
                        <option value="Strašnice">Strašnice</option>
                        <option value="Záběhlice">Záběhlice</option>
                        <option value="Malešice">Malešice</option>
                        <option value="Hostivař">Hostivař</option>
                    </select>
                </div>
            </form>
        </div>
        
        <div class="test-section">
            <h2>Test 2: Opravené Stránkování</h2>
            
            <div class="comparison">
                <div class="before">
                    <h4>❌ PŘED opravou</h4>
                    <ul>
                        <li>Zobrazovaly se tečky jako seznam</li>
                        <li>Špatná strategie: 1,2,3 ... 7, další</li>
                        <li>Neprofesionální vzhled</li>
                    </ul>
                </div>
                <div class="after">
                    <h4>✅ PO opravě</h4>
                    <ul>
                        <li>Čisté tlačítka bez seznamu</li>
                        <li>Profesionální strategie číslování</li>
                        <li>Moderní design s hover efekty</li>
                    </ul>
                </div>
            </div>
            
            <h3>Demo profesionálního stránkování:</h3>
            
            <h4>Scénář 1: Málo stránek (1-5)</h4>
            <div class="demo-pagination">
                <span class="demo-page-number prev">← Předchozí</span>
                <span class="demo-page-number">1</span>
                <span class="demo-page-number current">2</span>
                <span class="demo-page-number">3</span>
                <span class="demo-page-number">4</span>
                <span class="demo-page-number">5</span>
                <span class="demo-page-number next">Další →</span>
            </div>
            
            <h4>Scénář 2: Hodně stránek - začátek (stránka 3 z 20)</h4>
            <div class="demo-pagination">
                <span class="demo-page-number prev">← Předchozí</span>
                <span class="demo-page-number">1</span>
                <span class="demo-page-number">2</span>
                <span class="demo-page-number current">3</span>
                <span class="demo-page-number">4</span>
                <span class="demo-page-number">5</span>
                <span class="demo-page-number dots">...</span>
                <span class="demo-page-number">20</span>
                <span class="demo-page-number next">Další →</span>
            </div>
            
            <h4>Scénář 3: Hodně stránek - střed (stránka 10 z 20)</h4>
            <div class="demo-pagination">
                <span class="demo-page-number prev">← Předchozí</span>
                <span class="demo-page-number">1</span>
                <span class="demo-page-number dots">...</span>
                <span class="demo-page-number">8</span>
                <span class="demo-page-number">9</span>
                <span class="demo-page-number current">10</span>
                <span class="demo-page-number">11</span>
                <span class="demo-page-number">12</span>
                <span class="demo-page-number dots">...</span>
                <span class="demo-page-number">20</span>
                <span class="demo-page-number next">Další →</span>
            </div>
            
            <h4>Scénář 4: Hodně stránek - konec (stránka 18 z 20)</h4>
            <div class="demo-pagination">
                <span class="demo-page-number prev">← Předchozí</span>
                <span class="demo-page-number">1</span>
                <span class="demo-page-number dots">...</span>
                <span class="demo-page-number">16</span>
                <span class="demo-page-number">17</span>
                <span class="demo-page-number current">18</span>
                <span class="demo-page-number">19</span>
                <span class="demo-page-number">20</span>
                <span class="demo-page-number next">Další →</span>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Test 3: Technické detaily</h2>
            
            <h3>Opravené hodnoty filtrů:</h3>
            <ul>
                <li><strong>Pediatr:</strong> <code>pediatr</code> ✅</li>
                <li><strong>Praktický lékař:</strong> <code>praktik-dospeli</code> ✅ (dříve: prakticky-lekar ❌)</li>
            </ul>
            
            <h3>Nové funkce:</h3>
            <ul>
                <li><strong>Dynamické čtvrti:</strong> Select box se naplňuje z databáze</li>
                <li><strong>Zachování filtrů:</strong> Parametry se předávají při stránkování</li>
                <li><strong>Profesionální stránkování:</strong> mid_size=2, end_size=1</li>
                <li><strong>Responzivní design:</strong> Stránkování se přizpůsobuje šířce</li>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ Test archivních oprav načten');
            console.log('🔍 Zkontroluj:');
            console.log('1. Filtry mají správné hodnoty (praktik-dospeli)');
            console.log('2. Čtvrť je select box místo input');
            console.log('3. Stránkování má profesionální vzhled');
            console.log('4. Hover efekty fungují správně');
        });
    </script>
</body>
</html>
