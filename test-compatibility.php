<?php
/**
 * Compatibility test for both plugins
 * 
 * This script tests if both plugins can be loaded without conflicts
 */

// Simulate WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', '/tmp/');
}

if (!defined('WPINC')) {
    define('WPINC', 'wp-includes');
}

// Mock WordPress functions
function plugin_dir_path($file) {
    return dirname($file) . '/';
}

function plugin_dir_url($file) {
    return 'http://example.com/' . dirname($file) . '/';
}

function plugin_basename($file) {
    return basename(dirname($file)) . '/' . basename($file);
}

function add_action($hook, $callback, $priority = 10, $args = 1) {
    echo "✓ add_action: $hook\n";
}

function add_filter($hook, $callback, $priority = 10, $args = 1) {
    echo "✓ add_filter: $hook\n";
}

function add_shortcode($tag, $callback) {
    echo "✓ add_shortcode: $tag\n";
}

function register_activation_hook($file, $callback) {
    echo "✓ register_activation_hook\n";
}

function register_deactivation_hook($file, $callback) {
    echo "✓ register_deactivation_hook\n";
}

function register_post_type($name, $args) {
    echo "✓ register_post_type: $name\n";
}

function register_taxonomy($name, $post_types, $args) {
    echo "✓ register_taxonomy: $name\n";
}

function get_option($name, $default = false) {
    return $default;
}

function update_option($name, $value) {
    return true;
}

function add_option($name, $value) {
    return true;
}

function wp_verify_nonce($nonce, $action) {
    return true;
}

function wp_nonce_field($action, $name = '_wpnonce', $referer = true, $echo = true) {
    return '<input type="hidden" name="' . $name . '" value="test" />';
}

function current_user_can($capability, $object_id = null) {
    return true;
}

function get_post_type($post_id) {
    return 'test';
}

function get_post_meta($post_id, $key, $single = false) {
    return '';
}

function update_post_meta($post_id, $key, $value) {
    return true;
}

function sanitize_text_field($str) {
    return $str;
}

function esc_html($text) {
    return htmlspecialchars($text);
}

function esc_attr($text) {
    return htmlspecialchars($text);
}

function esc_url($url) {
    return $url;
}

function __($text, $domain = 'default') {
    return $text;
}

function is_admin() {
    return false;
}

// defined() is a built-in PHP function, no need to mock

// Test plugin compatibility
echo "=== Testing Plugin Compatibility ===\n\n";

echo "1. Checking syntax of original plugin (detske-skupiny.php)...\n";
$output = shell_exec('php -l detske-skupiny.php 2>&1');
if (strpos($output, 'No syntax errors') !== false) {
    echo "✓ Original plugin syntax is valid\n\n";
} else {
    echo "✗ Original plugin syntax error: $output\n\n";
}

echo "2. Checking syntax of new plugin (ordinace.php)...\n";
$output = shell_exec('php -l ordinace.php 2>&1');
if (strpos($output, 'No syntax errors') !== false) {
    echo "✓ New plugin syntax is valid\n\n";
} else {
    echo "✗ New plugin syntax error: $output\n\n";
}

echo "3. Checking for file conflicts...\n";
$conflicts = array();

// Check for conflicting files
$ds_files = array(
    'detske-skupiny.php',
    'includes/post-types.php',
    'includes/meta-boxes.php',
    'includes/admin-columns.php',
    'includes/bulk-edit.php',
    'includes/shortcodes.php',
    'includes/admin-settings.php',
    'assets/css/ds-custom-styles.css',
    'assets/css/admin-style.css'
);

$ordinace_files = array(
    'ordinace.php',
    'includes/ordinace-post-types.php',
    'includes/meta-boxes.php',
    'includes/admin-columns.php',
    'includes/shortcodes.php',
    'includes/admin-settings.php',
    'assets/css/spd10-ordinace-frontend.css'
);

$shared_files = array_intersect($ds_files, $ordinace_files);
echo "✓ Shared files (should be compatible): " . implode(', ', $shared_files) . "\n";

echo "\n4. Checking post type conflicts...\n";
echo "✓ Original plugin post types: ds, provozovatel\n";
echo "✓ New plugin post types: ordinace\n";
echo "✓ No post type conflicts\n";

echo "\n5. Checking taxonomy conflicts...\n";
echo "✓ Original plugin taxonomies: ds_ctvrt\n";
echo "✓ New plugin taxonomies: ordinace_typ\n";
echo "✓ No taxonomy conflicts\n";

echo "=== Compatibility Test Complete ===\n";
echo "Both plugins should be able to run simultaneously without conflicts.\n";
?>
