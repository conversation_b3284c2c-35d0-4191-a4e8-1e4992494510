<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test <PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="assets/css/spd10-ordinace-frontend.css" />
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #2196f3;
        }
        .fix-list {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }
        .before {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        .after {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        
        /* Demo špatného formátování */
        .bad-formatting {
            text-transform: capitalize !important;
        }
        
        /* Demo správného formátování */
        .good-formatting {
            text-transform: none !important;
        }
        
        .demo-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .demo-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }
        
        .demo-card-image {
            position: relative;
            height: 150px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        
        .demo-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            color: #2d3748;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .demo-card-content {
            padding: 20px;
        }
        
        .examples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .example-item {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
        }
        
        .example-correct {
            background: #f0fff4;
            border-color: #4caf50;
        }
        
        .example-incorrect {
            background: #fff5f5;
            border-color: #f44336;
        }
        
        .example-title {
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .example-correct .example-title::before {
            content: "✅ ";
            color: #4caf50;
        }
        
        .example-incorrect .example-title::before {
            content: "❌ ";
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🇨🇿 Test Českého Formátování</h1>
        
        <div class="test-info">
            <strong>Problém:</strong> V češtině se NIKDY nepoužívají velká písmena v každém slově (Title Case)!<br>
            <strong>Řešení:</strong> Přidání <code>text-transform: none</code> pro všechny badge a type elementy.
        </div>
        
        <div class="fix-list">
            <h3>✅ Opravené problémy:</h3>
            <ul>
                <li><strong>Badge v kartách:</strong> Odstraněn text-transform: capitalize</li>
                <li><strong>Type badge:</strong> Přidán text-transform: none</li>
                <li><strong>Globální pravidlo:</strong> CSS pravidlo pro všechny badge elementy</li>
                <li><strong>Testovací soubory:</strong> Opraveny příklady v HTML testech</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>Porovnání: Špatné vs. Správné Formátování</h2>
            
            <div class="comparison">
                <div class="before">
                    <h4>❌ ŠPATNĚ (anglický styl)</h4>
                    <ul>
                        <li class="bad-formatting">Praktičtí Lékaři Pro Děti A Dorost</li>
                        <li class="bad-formatting">Praktičtí Lékaři Pro Dospělé</li>
                        <li class="bad-formatting">Velmi Dlouhý Název Typu Ordinace</li>
                    </ul>
                </div>
                <div class="after">
                    <h4>✅ SPRÁVNĚ (český styl)</h4>
                    <ul>
                        <li class="good-formatting">Praktičtí lékaři pro děti a dorost</li>
                        <li class="good-formatting">Praktičtí lékaři pro dospělé</li>
                        <li class="good-formatting">Velmi dlouhý název typu ordinace</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Demo Opravených Karet</h2>
            
            <div class="demo-cards">
                <div class="demo-card">
                    <div class="demo-card-image">
                        <div class="demo-badge spd10-ordinace-card-badge">Praktičtí lékaři pro děti a dorost</div>
                        👶
                    </div>
                    <div class="demo-card-content">
                        <h3>MUDr. Eva Chládková</h3>
                        <p>Pediatrická ordinace v Záběhlicích</p>
                    </div>
                </div>
                
                <div class="demo-card">
                    <div class="demo-card-image">
                        <div class="demo-badge spd10-ordinace-card-badge">Praktičtí lékaři pro dospělé</div>
                        🩺
                    </div>
                    <div class="demo-card-content">
                        <h3>MUDr. Petr Dvořák</h3>
                        <p>Praktická ordinace v Karlově náměstí</p>
                    </div>
                </div>
                
                <div class="demo-card">
                    <div class="demo-card-image">
                        <div class="demo-badge spd10-ordinace-card-badge">Specializovaná ambulance</div>
                        🏥
                    </div>
                    <div class="demo-card-content">
                        <h3>MUDr. Jana Nováková</h3>
                        <p>Specializovaná péče ve Vršovicích</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Pravidla Českého Pravopisu</h2>
            
            <div class="examples-grid">
                <div class="example-correct">
                    <div class="example-title">Správně</div>
                    <div>Praktičtí lékaři pro děti a dorost</div>
                </div>
                
                <div class="example-incorrect">
                    <div class="example-title">Špatně</div>
                    <div>Praktičtí Lékaři Pro Děti A Dorost</div>
                </div>
                
                <div class="example-correct">
                    <div class="example-title">Správně</div>
                    <div>Ministerstvo zdravotnictví České republiky</div>
                </div>
                
                <div class="example-incorrect">
                    <div class="example-title">Špatně</div>
                    <div>Ministerstvo Zdravotnictví České Republiky</div>
                </div>
                
                <div class="example-correct">
                    <div class="example-title">Správně</div>
                    <div>Univerzita Karlova v Praze</div>
                </div>
                
                <div class="example-incorrect">
                    <div class="example-title">Špatně</div>
                    <div>Univerzita Karlova V Praze</div>
                </div>
                
                <div class="example-correct">
                    <div class="example-title">Správně</div>
                    <div>Dětská nemocnice Motol</div>
                </div>
                
                <div class="example-incorrect">
                    <div class="example-title">Špatně</div>
                    <div>Dětská Nemocnice Motol</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Technické Detaily Opravy</h2>
            
            <h3>Přidané CSS pravidlo:</h3>
            <pre><code>/* Správné formátování českých textů */
.spd10-ordinace-card-badge,
.spd10-ordinace-type-badge,
.spd10-ordinace-landing-card-title,
[class*="spd10-ordinace"] .badge,
[class*="spd10-ordinace"] .type-name {
    text-transform: none !important;
}</code></pre>
            
            <h3>Opravené soubory:</h3>
            <ul>
                <li><code>assets/css/spd10-ordinace-frontend.css</code> - globální pravidlo</li>
                <li><code>templates/archive-ordinace.php</code> - badge v archive</li>
                <li><code>templates/single-ordinace.php</code> - type badge v single</li>
                <li><code>test-fixes.html</code> - opraveny příklady</li>
            </ul>
            
            <h3>Kdy používat velká písmena v češtině:</h3>
            <ul>
                <li>✅ Na začátku věty</li>
                <li>✅ U vlastních jmen (Praha, Karlova univerzita)</li>
                <li>✅ U názvů institucí (Ministerstvo zdravotnictví)</li>
                <li>❌ NIKDY u běžných podstatných jmen v titulech</li>
                <li>❌ NIKDY u předložek a spojek (pro, a, v, na, ...)</li>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ Test českého formátování načten');
            console.log('🔍 Zkontroluj:');
            console.log('1. Všechny badge mají správné formátování (malá písmena)');
            console.log('2. CSS pravidlo text-transform: none funguje');
            console.log('3. Žádné "Title Case" formátování');
            console.log('4. Respektování českých pravidel pravopisu');
        });
    </script>
</body>
</html>
