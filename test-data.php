<?php
/**
 * Test script for creating sample ordinace data
 * 
 * Run this from WordPress admin or via WP-CLI to create test data
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Create sample ordinace data for testing
 */
function spd10_ordinace_create_test_data() {
    // Sample ordinace data
    $test_ordinaces = array(
        array(
            'title' => 'MUDr. <PERSON> - <PERSON>ý lé<PERSON>ř',
            'address' => 'Wenceslas Square 1, Prague 1, Czech Republic',
            'organization' => 'Ordinace MUDr. Novák',
            'doctors_names' => 'MUDr. <PERSON>',
            'phone' => '+420 123 456 789',
            'email' => '<EMAIL>',
            'website' => 'https://ordinace-novak.cz',
            'type' => 'praktik-dospeli',
            'lat' => 50.0813,
            'lng' => 14.4267
        ),
        array(
            'title' => 'MUDr. <PERSON>ediatr',
            'address' => 'Náměstí Míru 5, Prague 2, Czech Republic',
            'organization' => 'Dětská ordinace Svobodová',
            'doctors_names' => 'MUDr. <PERSON>dová',
            'phone' => '+420 987 654 321',
            'email' => '<EMAIL>',
            'website' => 'https://pediatr-svobodova.cz',
            'type' => 'pediatr',
            'lat' => 50.0755,
            'lng' => 14.4378
        ),
        array(
            'title' => 'MUDr. Petr Dvořák - Praktický lékař',
            'address' => 'Karlovo náměstí 10, Prague 2, Czech Republic',
            'organization' => 'Ordinace Dvořák',
            'doctors_names' => 'MUDr. Petr Dvořák',
            'phone' => '+420 555 123 456',
            'email' => '<EMAIL>',
            'website' => 'https://dvorak-ordinace.cz',
            'type' => 'praktik-dospeli',
            'lat' => 50.0755,
            'lng' => 14.4150
        )
    );

    $created = 0;
    $errors = array();

    foreach ($test_ordinaces as $ordinace_data) {
        // Check if ordinace already exists
        $existing = get_posts(array(
            'post_type' => 'ordinace',
            'meta_query' => array(
                array(
                    'key' => '_spd10_ordinace_address',
                    'value' => $ordinace_data['address'],
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1
        ));

        if (!empty($existing)) {
            continue; // Skip if already exists
        }

        // Create post
        $post_data = array(
            'post_title' => $ordinace_data['title'],
            'post_type' => 'ordinace',
            'post_status' => 'publish',
            'post_content' => 'Testovací ordinace vytvořená pro účely testování mapy.'
        );

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            $errors[] = 'Failed to create post: ' . $post_id->get_error_message();
            continue;
        }

        // Set meta fields
        $meta_fields = array(
            '_spd10_ordinace_address' => $ordinace_data['address'],
            '_spd10_ordinace_organization' => $ordinace_data['organization'],
            '_spd10_ordinace_doctors_names' => $ordinace_data['doctors_names'],
            '_spd10_ordinace_phone' => $ordinace_data['phone'],
            '_spd10_ordinace_email' => $ordinace_data['email'],
            '_spd10_ordinace_website' => $ordinace_data['website'],
            '_spd10_ordinace_lat' => $ordinace_data['lat'],
            '_spd10_ordinace_lng' => $ordinace_data['lng'],
            '_spd10_ordinace_geocode_status' => 'OK'
        );

        foreach ($meta_fields as $key => $value) {
            update_post_meta($post_id, $key, $value);
        }

        // Set taxonomy term
        wp_set_object_terms($post_id, $ordinace_data['type'], 'ordinace_typ');

        $created++;
    }

    return array(
        'created' => $created,
        'errors' => $errors
    );
}

/**
 * Delete all test ordinace data
 */
function spd10_ordinace_delete_test_data() {
    $ordinaces = get_posts(array(
        'post_type' => 'ordinace',
        'posts_per_page' => -1,
        'post_status' => 'any'
    ));

    $deleted = 0;
    foreach ($ordinaces as $ordinace) {
        if (wp_delete_post($ordinace->ID, true)) {
            $deleted++;
        }
    }

    return $deleted;
}

// If running from admin, show interface
if (is_admin() && isset($_GET['page']) && $_GET['page'] === 'spd10-ordinace-test-data') {
    add_action('admin_menu', function() {
        add_submenu_page(
            'edit.php?post_type=ordinace',
            'Test Data',
            'Test Data',
            'manage_options',
            'spd10-ordinace-test-data',
            'spd10_ordinace_test_data_page'
        );
    });
}

function spd10_ordinace_test_data_page() {
    if (isset($_POST['create_test_data'])) {
        $result = spd10_ordinace_create_test_data();
        echo '<div class="notice notice-success"><p>Created ' . $result['created'] . ' test ordinaces.</p></div>';
        if (!empty($result['errors'])) {
            echo '<div class="notice notice-error"><p>Errors: ' . implode(', ', $result['errors']) . '</p></div>';
        }
    }

    if (isset($_POST['delete_test_data'])) {
        $deleted = spd10_ordinace_delete_test_data();
        echo '<div class="notice notice-success"><p>Deleted ' . $deleted . ' ordinaces.</p></div>';
    }

    ?>
    <div class="wrap">
        <h1>Ordinace Test Data</h1>
        
        <div class="card">
            <h2>Create Test Data</h2>
            <p>This will create 3 sample ordinaces in Prague for testing the map functionality.</p>
            <form method="post">
                <input type="submit" name="create_test_data" class="button button-primary" value="Create Test Data">
            </form>
        </div>

        <div class="card">
            <h2>Delete All Data</h2>
            <p><strong>Warning:</strong> This will delete ALL ordinace posts!</p>
            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL ordinace data?')">
                <input type="submit" name="delete_test_data" class="button button-secondary" value="Delete All Data">
            </form>
        </div>

        <div class="card">
            <h2>Current Data</h2>
            <?php
            $ordinaces = get_posts(array(
                'post_type' => 'ordinace',
                'posts_per_page' => -1
            ));
            echo '<p>Current ordinaces in database: ' . count($ordinaces) . '</p>';
            
            if (!empty($ordinaces)) {
                echo '<ul>';
                foreach ($ordinaces as $ordinace) {
                    $address = get_post_meta($ordinace->ID, '_spd10_ordinace_address', true);
                    $lat = get_post_meta($ordinace->ID, '_spd10_ordinace_lat', true);
                    $lng = get_post_meta($ordinace->ID, '_spd10_ordinace_lng', true);
                    $terms = wp_get_post_terms($ordinace->ID, 'ordinace_typ');
                    $type = !empty($terms) ? $terms[0]->slug : 'none';
                    
                    echo '<li><strong>' . esc_html($ordinace->post_title) . '</strong><br>';
                    echo 'Address: ' . esc_html($address) . '<br>';
                    echo 'Coordinates: ' . esc_html($lat) . ', ' . esc_html($lng) . '<br>';
                    echo 'Type: ' . esc_html($type) . '</li>';
                }
                echo '</ul>';
            }
            ?>
        </div>
    </div>
    <?php
}
?>
