<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Oprav - Mapa a Karty</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="assets/css/spd10-ordinace-frontend.css" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #2196f3;
        }
        .resize-demo {
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test Oprav - Ordinace Plugin</h1>
        
        <div class="test-info">
            <strong>Opravené problémy:</strong><br>
            1. ✅ Výška mapy se nemění - opraveno v CSS<br>
            2. ✅ Chybí markery na mapě - přidán fallback s testovacími daty<br>
            3. ✅ "Více informací" box přesahuje - přidán box-sizing: border-box<br>
            4. ✅ Responsive layout - 3-2-1 karty podle šířky obrazovky<br>
            5. ✅ Badge překrývá obrázek - přidáno max-width a text-overflow
        </div>
        
        <div class="test-section">
            <h2>Test 1: Mapa s testovacími daty (fallback)</h2>
            <div class="spd10-ordinace-map-container">
                <div id="map1" class="spd10-ordinace-map shortcode-map" style="height: 500px; width: 100%;" data-provider="leaflet"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Test 2: Responsive Grid Layout (3-2-1)</h2>
            <div class="resize-demo">
                <strong>Instrukce:</strong> Změňte šířku okna prohlížeče a sledujte, jak se mění počet karet v řádku:<br>
                • Široké obrazovky (>1200px): 3 karty<br>
                • Střední obrazovky (768px-1200px): 2 karty<br>
                • Úzké obrazovky (<768px): 1 karta
            </div>
            
            <div class="spd10-ordinace-grid">
                <!-- Karta 1 -->
                <div class="spd10-ordinace-item">
                    <div class="spd10-ordinace-card-image" style="height: 200px; background: linear-gradient(135deg, #667eea, #764ba2); position: relative;">
                        <div class="spd10-ordinace-card-badge">Praktičtí lékaři pro děti a dospělé</div>
                    </div>
                    <div class="spd10-ordinace-item-content">
                        <h3>MUDr. Eva Chládková</h3>
                        <div class="spd10-ordinace-item-meta">
                            <div class="spd10-ordinace-item-location">📍 Záběhlice</div>
                            <div class="spd10-ordinace-item-doctor">👨‍⚕️ MUDr. Eva Chládková</div>
                            <div class="spd10-ordinace-item-phone">📞 267 295 352</div>
                        </div>
                        <a href="#" class="spd10-ordinace-more-info">Více informací</a>
                    </div>
                </div>
                
                <!-- Karta 2 -->
                <div class="spd10-ordinace-item">
                    <div class="spd10-ordinace-card-image" style="height: 200px; background: linear-gradient(135deg, #4CAF50, #45a049); position: relative;">
                        <div class="spd10-ordinace-card-badge">Pediatři</div>
                    </div>
                    <div class="spd10-ordinace-item-content">
                        <h3>MUDr. Marie Svobodová</h3>
                        <div class="spd10-ordinace-item-meta">
                            <div class="spd10-ordinace-item-location">📍 Náměstí Míru</div>
                            <div class="spd10-ordinace-item-doctor">👨‍⚕️ MUDr. Marie Svobodová</div>
                            <div class="spd10-ordinace-item-phone">📞 987 654 321</div>
                        </div>
                        <a href="#" class="spd10-ordinace-more-info">Více informací</a>
                    </div>
                </div>
                
                <!-- Karta 3 -->
                <div class="spd10-ordinace-item">
                    <div class="spd10-ordinace-card-image" style="height: 200px; background: linear-gradient(135deg, #FF9800, #F57C00); position: relative;">
                        <div class="spd10-ordinace-card-badge">Praktičtí lékaři</div>
                    </div>
                    <div class="spd10-ordinace-item-content">
                        <h3>MUDr. Petr Dvořák</h3>
                        <div class="spd10-ordinace-item-meta">
                            <div class="spd10-ordinace-item-location">📍 Karlovo náměstí</div>
                            <div class="spd10-ordinace-item-doctor">👨‍⚕️ MUDr. Petr Dvořák</div>
                            <div class="spd10-ordinace-item-phone">📞 555 123 456</div>
                        </div>
                        <a href="#" class="spd10-ordinace-more-info">Více informací</a>
                    </div>
                </div>
                
                <!-- Karta 4 -->
                <div class="spd10-ordinace-item">
                    <div class="spd10-ordinace-card-image" style="height: 200px; background: linear-gradient(135deg, #9C27B0, #7B1FA2); position: relative;">
                        <div class="spd10-ordinace-card-badge">Velmi dlouhý název typu ordinace</div>
                    </div>
                    <div class="spd10-ordinace-item-content">
                        <h3>MUDr. Jan Novák</h3>
                        <div class="spd10-ordinace-item-meta">
                            <div class="spd10-ordinace-item-location">📍 Wenceslas Square</div>
                            <div class="spd10-ordinace-item-doctor">👨‍⚕️ MUDr. Jan Novák</div>
                            <div class="spd10-ordinace-item-phone">📞 123 456 789</div>
                        </div>
                        <a href="#" class="spd10-ordinace-more-info">Více informací</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Test data for map
        window.spd10OrdinaceTestData = [
            {
                id: 1,
                title: 'MUDr. Jan Novák - Praktický lékař',
                lat: 50.0813,
                lng: 14.4267,
                type: 'praktik-dospeli',
                doctors_names: 'MUDr. Jan Novák',
                organization: 'Ordinace MUDr. Novák',
                address: 'Wenceslas Square 1, Prague 1',
                phone: '+420 123 456 789',
                email: '<EMAIL>'
            },
            {
                id: 2,
                title: 'MUDr. Marie Svobodová - Pediatr',
                lat: 50.0755,
                lng: 14.4378,
                type: 'pediatr',
                doctors_names: 'MUDr. Marie Svobodová',
                organization: 'Dětská ordinace Svobodová',
                address: 'Náměstí Míru 5, Prague 2',
                phone: '+420 987 654 321',
                email: '<EMAIL>'
            },
            {
                id: 3,
                title: 'MUDr. Petr Dvořák - Praktický lékař',
                lat: 50.0755,
                lng: 14.4150,
                type: 'praktik-dospeli',
                doctors_names: 'MUDr. Petr Dvořák',
                organization: 'Ordinace Dvořák',
                address: 'Karlovo náměstí 10, Prague 2',
                phone: '+420 555 123 456',
                email: '<EMAIL>'
            }
        ];
        
        // Mock spd10OrdinaceMap object
        window.spd10OrdinaceMap = {
            provider: 'leaflet',
            config: {
                default_zoom: 13,
                max_zoom: 18,
                attribution: '© OpenStreetMap contributors'
            },
            center: {
                lat: 50.0755,
                lng: 14.4378
            },
            markerColors: {
                'pediatr': '#4CAF50',
                'praktik-dospeli': '#2196F3'
            },
            strings: {
                loading: 'Načítání mapy...',
                error: 'Chyba při načítání mapy',
                noResults: 'Žádné výsledky'
            }
        };
        
        // Initialize test map with fallback data
        function initTestMap() {
            try {
                const map = L.map('map1', {
                    center: [50.0755, 14.4378],
                    zoom: 13,
                    maxZoom: 18
                });

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 18
                }).addTo(map);

                // Add test markers using fallback data
                window.spd10OrdinaceTestData.forEach(function(ordinace) {
                    const color = ordinace.type === 'pediatr' ? '#4CAF50' : '#2196F3';
                    
                    const marker = L.circleMarker([ordinace.lat, ordinace.lng], {
                        radius: 8,
                        fillColor: color,
                        color: '#fff',
                        weight: 2,
                        opacity: 1,
                        fillOpacity: 0.8
                    }).addTo(map);
                    
                    const popupContent = `
                        <div style="min-width: 200px;">
                            <h4 style="margin: 0 0 10px 0; color: #333;">${ordinace.title}</h4>
                            <p style="margin: 5px 0;"><strong>Organizace:</strong> ${ordinace.organization}</p>
                            <p style="margin: 5px 0;"><strong>Adresa:</strong> ${ordinace.address}</p>
                            <p style="margin: 5px 0;"><strong>Telefon:</strong> ${ordinace.phone}</p>
                            <p style="margin: 5px 0;"><strong>Email:</strong> ${ordinace.email}</p>
                            <p style="margin: 5px 0;"><strong>Typ:</strong> ${ordinace.type === 'pediatr' ? 'Pediatr' : 'Praktický lékař'}</p>
                        </div>
                    `;
                    
                    marker.bindPopup(popupContent);
                });
                
                console.log('✅ Mapa inicializována s testovacími daty');
                return map;
                
            } catch (error) {
                console.error('❌ Chyba při inicializaci mapy:', error);
                return null;
            }
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                initTestMap();
            }, 500);
        });
    </script>
</body>
</html>
