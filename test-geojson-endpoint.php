<?php
/**
 * Test script for GeoJSON endpoint
 * 
 * This script tests if the GeoJSON endpoint is working correctly
 * and helps debug any issues with the map data loading.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Try to load WordPress
    $wp_load_paths = array(
        __DIR__ . '/wp-load.php',
        __DIR__ . '/../wp-load.php',
        __DIR__ . '/../../wp-load.php',
        __DIR__ . '/../../../wp-load.php',
        '/var/www/html/wp-load.php',
        '/home/<USER>/vyvoj/ordinace-plugin/wp-load.php'
    );
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this script from WordPress root or adjust paths.');
    }
}

echo "<h1>SPD10 Ordinace GeoJSON Endpoint Test</h1>\n";

// Test 1: Check if GeoJSON cache class exists
echo "<h2>Test 1: Class Availability</h2>\n";
if (class_exists('SPD10_Ordinace_GeoJSON_Cache')) {
    echo "✓ SPD10_Ordinace_GeoJSON_Cache class is available<br>\n";
} else {
    echo "✗ SPD10_Ordinace_GeoJSON_Cache class not found<br>\n";
}

// Test 2: Check rewrite rules
echo "<h2>Test 2: Rewrite Rules</h2>\n";
global $wp_rewrite;
$rules = $wp_rewrite->wp_rewrite_rules();
$geojson_rule_found = false;

foreach ($rules as $pattern => $replacement) {
    if (strpos($pattern, 'ordinace-geojson') !== false) {
        echo "✓ Found rewrite rule: $pattern → $replacement<br>\n";
        $geojson_rule_found = true;
    }
}

if (!$geojson_rule_found) {
    echo "✗ No GeoJSON rewrite rules found<br>\n";
    echo "Available rules:<br>\n";
    foreach ($rules as $pattern => $replacement) {
        if (strpos($pattern, 'ordinace') !== false) {
            echo "- $pattern → $replacement<br>\n";
        }
    }
}

// Test 3: Check query vars
echo "<h2>Test 3: Query Variables</h2>\n";
global $wp;
if (in_array('spd10_ordinace_geojson', $wp->public_query_vars)) {
    echo "✓ spd10_ordinace_geojson query var is registered<br>\n";
} else {
    echo "✗ spd10_ordinace_geojson query var not found<br>\n";
}

if (in_array('ordinace_type', $wp->public_query_vars)) {
    echo "✓ ordinace_type query var is registered<br>\n";
} else {
    echo "✗ ordinace_type query var not found<br>\n";
}

// Test 4: Check ordinace posts
echo "<h2>Test 4: Ordinace Posts</h2>\n";
$ordinace_count = wp_count_posts('ordinace');
if ($ordinace_count && $ordinace_count->publish > 0) {
    echo "✓ Found {$ordinace_count->publish} published ordinace posts<br>\n";
} else {
    echo "✗ No published ordinace posts found<br>\n";
}

// Test 5: Check posts with coordinates
echo "<h2>Test 5: Posts with Coordinates</h2>\n";
$posts_with_coords = get_posts(array(
    'post_type' => 'ordinace',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'meta_query' => array(
        'relation' => 'AND',
        array(
            'key' => '_spd10_ordinace_lat',
            'value' => '',
            'compare' => '!=',
        ),
        array(
            'key' => '_spd10_ordinace_lng',
            'value' => '',
            'compare' => '!=',
        ),
    ),
));

echo "✓ Found " . count($posts_with_coords) . " ordinace posts with coordinates<br>\n";

if (count($posts_with_coords) > 0) {
    echo "<strong>Sample coordinates:</strong><br>\n";
    foreach (array_slice($posts_with_coords, 0, 3) as $post) {
        $lat = get_post_meta($post->ID, '_spd10_ordinace_lat', true);
        $lng = get_post_meta($post->ID, '_spd10_ordinace_lng', true);
        echo "- {$post->post_title}: {$lat}, {$lng}<br>\n";
    }
}

// Test 6: Test GeoJSON generation
echo "<h2>Test 6: GeoJSON Generation</h2>\n";
if (class_exists('SPD10_Ordinace_GeoJSON_Cache')) {
    $cache = new SPD10_Ordinace_GeoJSON_Cache();
    
    // Use reflection to access private method
    $reflection = new ReflectionClass($cache);
    $method = $reflection->getMethod('generate_geojson_data');
    $method->setAccessible(true);
    
    try {
        $geojson = $method->invoke($cache, '', '');
        echo "✓ GeoJSON generation successful<br>\n";
        echo "- Features count: " . count($geojson['features']) . "<br>\n";
        echo "- Generated at: " . $geojson['metadata']['generated_at'] . "<br>\n";
        
        if (count($geojson['features']) > 0) {
            echo "<strong>Sample feature:</strong><br>\n";
            $feature = $geojson['features'][0];
            echo "- Title: " . $feature['properties']['title'] . "<br>\n";
            echo "- Coordinates: " . implode(', ', $feature['geometry']['coordinates']) . "<br>\n";
        }
    } catch (Exception $e) {
        echo "✗ GeoJSON generation failed: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "✗ Cannot test GeoJSON generation - class not available<br>\n";
}

// Test 7: Test endpoint URL
echo "<h2>Test 7: Endpoint URL Test</h2>\n";
$geojson_url = home_url('ordinace-geojson');
echo "GeoJSON URL: <a href=\"{$geojson_url}\" target=\"_blank\">{$geojson_url}</a><br>\n";

// Test 8: Manual flush rewrite rules
echo "<h2>Test 8: Manual Flush</h2>\n";
echo "<a href=\"?flush_rules=1\">Click here to flush rewrite rules</a><br>\n";

if (isset($_GET['flush_rules'])) {
    flush_rewrite_rules();
    echo "✓ Rewrite rules flushed!<br>\n";
}

echo "<h2>Recommendations</h2>\n";
echo "<ul>\n";
echo "<li>If rewrite rules are missing, try flushing them using the link above</li>\n";
echo "<li>If no ordinace posts found, import some test data first</li>\n";
echo "<li>If posts have no coordinates, run geocoding on them</li>\n";
echo "<li>Check browser console for JavaScript errors when testing the map</li>\n";
echo "</ul>\n";
?>
