<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GeoJSON Endpoint</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #1565c0;
        }
    </style>
</head>
<body>
    <h1>Test GeoJSON Endpoint pro Ordinace Plugin</h1>
    
    <div class="test-section">
        <h2>Test 1: <PERSON>áklad<PERSON><PERSON> GeoJSON endpoint</h2>
        <button onclick="testBasicEndpoint()">Test /ordinace-geojson</button>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: GeoJSON s filtrem typu</h2>
        <button onclick="testTypeFilter()">Test /ordinace-geojson/pediatr</button>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Rewrite rules test</h2>
        <button onclick="testRewriteRules()">Test rewrite rules</button>
        <div id="result3" class="result"></div>
    </div>

    <script>
        const baseUrl = window.location.origin + window.location.pathname.replace('/test-geojson.html', '');
        
        async function testBasicEndpoint() {
            const resultDiv = document.getElementById('result1');
            resultDiv.textContent = 'Testování...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(baseUrl + '/ordinace-geojson');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = `✓ Úspěch!\nStatus: ${response.status}\nData: ${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `✗ Chyba HTTP ${response.status}\nData: ${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `✗ Chyba: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testTypeFilter() {
            const resultDiv = document.getElementById('result2');
            resultDiv.textContent = 'Testování...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(baseUrl + '/ordinace-geojson/pediatr');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = `✓ Úspěch!\nStatus: ${response.status}\nData: ${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `✗ Chyba HTTP ${response.status}\nData: ${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `✗ Chyba: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testRewriteRules() {
            const resultDiv = document.getElementById('result3');
            resultDiv.textContent = 'Testování rewrite rules...';
            resultDiv.className = 'result';
            
            const tests = [
                { url: baseUrl + '/ordinace-geojson', name: 'Základní endpoint' },
                { url: baseUrl + '/ordinace-geojson/', name: 'Endpoint s lomítkem' },
                { url: baseUrl + '/ordinace-geojson/praktik-dospeli', name: 'Endpoint s typem' },
                { url: baseUrl + '/ordinace-geojson?quarter=Praha+1', name: 'Endpoint s query parametrem' }
            ];
            
            let results = '';
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url);
                    const status = response.status;
                    
                    if (status === 200) {
                        results += `✓ ${test.name}: OK (${status})\n`;
                    } else if (status === 404) {
                        results += `✗ ${test.name}: 404 - Rewrite rule nefunguje\n`;
                    } else {
                        results += `? ${test.name}: ${status}\n`;
                    }
                } catch (error) {
                    results += `✗ ${test.name}: Chyba - ${error.message}\n`;
                }
            }
            
            resultDiv.textContent = results;
            resultDiv.className = 'result';
        }
    </script>
</body>
</html>
