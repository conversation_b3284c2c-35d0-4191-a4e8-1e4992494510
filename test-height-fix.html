<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Opravy Výšky Mapy</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="assets/css/spd10-ordinace-frontend.css" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #2196f3;
        }
        .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🗺️ Test Opravy Výšky Mapy - Ordinace Plugin</h1>
        
        <div class="test-info">
            <strong>Testované problémy:</strong><br>
            1. ✅ Výška mapy se nemění (opraveno v CSS - odstraněna pevná height: 400px)<br>
            2. 🔍 Chybí markery na mapě (testujeme mock data)<br>
            3. 🔍 GeoJSON endpoint nefunguje (potřebuje WordPress server)
        </div>
        
        <div class="test-section">
            <h2>Test 1: Mapa s výchozí výškou (400px)</h2>
            <div class="spd10-ordinace-map-container">
                <div id="map1" class="spd10-ordinace-map shortcode-map" style="height: 400px; width: 100%;" data-provider="leaflet"></div>
            </div>
            <div id="result1" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 2: Mapa s vlastní výškou (600px) - Test parametru height</h2>
            <div class="spd10-ordinace-map-container">
                <div id="map2" class="spd10-ordinace-map shortcode-map" style="height: 600px; width: 100%;" data-provider="leaflet"></div>
            </div>
            <div id="result2" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 3: Mapa s malou výškou (250px)</h2>
            <div class="spd10-ordinace-map-container">
                <div id="map3" class="spd10-ordinace-map shortcode-map" style="height: 250px; width: 100%;" data-provider="leaflet"></div>
            </div>
            <div id="result3" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 4: Mapa s velmi vysokou výškou (800px)</h2>
            <div class="spd10-ordinace-map-container">
                <div id="map4" class="spd10-ordinace-map shortcode-map" style="height: 800px; width: 100%;" data-provider="leaflet"></div>
            </div>
            <div id="result4" class="test-result"></div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Test data - same as in test-data.php
        const testOrdinaces = [
            {
                lat: 50.0813,
                lng: 14.4267,
                title: 'MUDr. Jan Novák - Praktický lékař',
                organization: 'Ordinace MUDr. Novák',
                address: 'Wenceslas Square 1, Prague 1',
                type: 'praktik-dospeli',
                phone: '+420 123 456 789',
                email: '<EMAIL>'
            },
            {
                lat: 50.0755,
                lng: 14.4378,
                title: 'MUDr. Marie Svobodová - Pediatr',
                organization: 'Dětská ordinace Svobodová',
                address: 'Náměstí Míru 5, Prague 2',
                type: 'pediatr',
                phone: '+420 987 654 321',
                email: '<EMAIL>'
            },
            {
                lat: 50.0755,
                lng: 14.4150,
                title: 'MUDr. Petr Dvořák - Praktický lékař',
                organization: 'Ordinace Dvořák',
                address: 'Karlovo náměstí 10, Prague 2',
                type: 'praktik-dospeli',
                phone: '+420 555 123 456',
                email: '<EMAIL>'
            }
        ];
        
        // Initialize test map
        function initTestMap(mapId) {
            const mapElement = document.getElementById(mapId);
            const expectedHeight = parseInt(mapElement.style.height.replace('px', ''));
            
            try {
                const map = L.map(mapId, {
                    center: [50.0755, 14.4378],
                    zoom: 13,
                    maxZoom: 18
                });

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 18
                }).addTo(map);

                // Add test markers
                let markerCount = 0;
                testOrdinaces.forEach(function(ordinace) {
                    const color = ordinace.type === 'pediatr' ? '#2196F3' : '#4CAF50';
                    
                    const marker = L.circleMarker([ordinace.lat, ordinace.lng], {
                        radius: 8,
                        fillColor: color,
                        color: '#fff',
                        weight: 2,
                        opacity: 1,
                        fillOpacity: 0.8
                    }).addTo(map);
                    
                    const popupContent = `
                        <div style="min-width: 200px;">
                            <h4 style="margin: 0 0 10px 0; color: #333;">${ordinace.title}</h4>
                            <p style="margin: 5px 0;"><strong>Organizace:</strong> ${ordinace.organization}</p>
                            <p style="margin: 5px 0;"><strong>Adresa:</strong> ${ordinace.address}</p>
                            <p style="margin: 5px 0;"><strong>Telefon:</strong> ${ordinace.phone}</p>
                            <p style="margin: 5px 0;"><strong>Email:</strong> ${ordinace.email}</p>
                            <p style="margin: 5px 0;"><strong>Typ:</strong> ${ordinace.type === 'pediatr' ? 'Pediatr' : 'Praktický lékař'}</p>
                        </div>
                    `;
                    
                    marker.bindPopup(popupContent);
                    markerCount++;
                });
                
                // Check actual height after map is fully loaded
                setTimeout(() => {
                    const actualHeight = mapElement.offsetHeight;
                    const resultDiv = document.getElementById('result' + mapId.slice(-1));
                    
                    if (actualHeight === expectedHeight) {
                        resultDiv.innerHTML = `✅ <strong>Úspěch!</strong><br>
                            Očekávaná výška: ${expectedHeight}px<br>
                            Skutečná výška: ${actualHeight}px<br>
                            Počet markerů: ${markerCount}<br>
                            Status: Výška se správně aplikuje z inline style`;
                        resultDiv.className = 'test-result success';
                    } else {
                        resultDiv.innerHTML = `❌ <strong>Problém s výškou!</strong><br>
                            Očekávaná výška: ${expectedHeight}px<br>
                            Skutečná výška: ${actualHeight}px<br>
                            Počet markerů: ${markerCount}<br>
                            Status: CSS stále přepisuje inline style`;
                        resultDiv.className = 'test-result error';
                    }
                }, 1500);
                
                return map;
                
            } catch (error) {
                const resultDiv = document.getElementById('result' + mapId.slice(-1));
                resultDiv.innerHTML = `❌ <strong>Chyba při inicializaci:</strong><br>${error.message}`;
                resultDiv.className = 'test-result error';
                return null;
            }
        }
        
        // Initialize all test maps
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Inicializuji testovací mapy...');
            
            setTimeout(() => {
                const map1 = initTestMap('map1');
                const map2 = initTestMap('map2');
                const map3 = initTestMap('map3');
                const map4 = initTestMap('map4');
                
                console.log('Test maps initialized:', {
                    map1: map1 ? 'OK' : 'FAILED',
                    map2: map2 ? 'OK' : 'FAILED',
                    map3: map3 ? 'OK' : 'FAILED',
                    map4: map4 ? 'OK' : 'FAILED'
                });
            }, 500);
        });
    </script>
</body>
</html>
