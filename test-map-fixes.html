<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test oprav mapy ordinací</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        .shortcode-example {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .fix-list {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .fix-list h3 {
            color: #2d5a2d;
            margin-top: 0;
        }
        .fix-list ul {
            margin: 0;
        }
        .fix-list li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>Test oprav mapy ordinací</h1>
    
    <div class="fix-list">
        <h3>Opravené problémy:</h3>
        <ul>
            <li>✅ Odstraněna mock data - mapa nyní načítá skutečná data z databáze</li>
            <li>✅ Opraveno nastavení výšky mapy ze shortcode atributu</li>
            <li>✅ Přidána funkce fullscreen s tlačítkem "Celá obrazovka"</li>
            <li>✅ Opraveno překrývání filtrů v fullscreen režimu</li>
            <li>✅ Vylepšeno centrování mapy pro zobrazení všech ordinací</li>
            <li>✅ Přidány debug informace pro lepší diagnostiku</li>
            <li>✅ Uvolněny filtry pro geocode_status (zobrazí více záznamů)</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Test 1: Základní mapa s výchozí výškou</h2>
        <div class="shortcode-example">[ordinace_map]</div>
        <p>Očekávaný výsledek: Mapa s výškou 400px, všechny ordinace z databáze, tlačítko fullscreen</p>
    </div>

    <div class="test-section">
        <h2>Test 2: Mapa s vlastní výškou</h2>
        <div class="shortcode-example">[ordinace_map height="600px"]</div>
        <p>Očekávaný výsledek: Mapa s výškou 600px</p>
    </div>

    <div class="test-section">
        <h2>Test 3: Mapa s filtrem typu</h2>
        <div class="shortcode-example">[ordinace_map typ="pediatr"]</div>
        <p>Očekávaný výsledek: Pouze pediatři</p>
    </div>

    <div class="test-section">
        <h2>Test 4: Mapa bez filtrů a legendy</h2>
        <div class="shortcode-example">[ordinace_map show_filters="false" show_legend="false"]</div>
        <p>Očekávaný výsledek: Pouze mapa bez ovládacích prvků</p>
    </div>

    <div class="test-section">
        <h2>Technické změny:</h2>
        <ul>
            <li><strong>includes/shortcodes.php</strong>: Přidána fullscreen funkcionalita, odstraněna mock data</li>
            <li><strong>assets/js/spd10-ordinace-map.js</strong>: Vylepšeno načítání dat a centrování mapy</li>
            <li><strong>assets/css/spd10-ordinace-frontend.css</strong>: Přidány fullscreen styly</li>
            <li><strong>includes/class-geojson-cache.php</strong>: Uvolněny filtry pro geocode_status</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Jak testovat fullscreen:</h2>
        <ol>
            <li>Klikněte na tlačítko "Celá obrazovka" v pravém horním rohu mapy</li>
            <li>Mapa by se měla rozšířit na celou obrazovku</li>
            <li>Filtry by měly být skryté</li>
            <li>Klikněte na "Ukončit celou obrazovku" pro návrat</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Debug informace:</h2>
        <p>Pro kontrolu načítání dat otevřete Developer Tools (F12) a sledujte Console tab. Měli byste vidět:</p>
        <ul>
            <li>URL pro načítání GeoJSON dat</li>
            <li>Počet načtených ordinací</li>
            <li>Informace o centrování mapy</li>
        </ul>
    </div>
</body>
</html>
