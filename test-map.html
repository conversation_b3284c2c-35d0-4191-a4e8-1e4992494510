<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Ordinace Map - Výška a Markery</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="assets/css/spd10-ordinace-frontend.css" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #2196f3;
        }
        .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Test Ordinace Map Functionality</h1>
    
    <div class="test-section">
        <h2>1. Test JavaScript Loading</h2>
        <div id="js-test">Checking...</div>
    </div>
    
    <div class="test-section">
        <h2>2. Test GeoJSON Endpoint</h2>
        <div id="geojson-test">Checking...</div>
        <button onclick="testGeoJSON()">Test GeoJSON</button>
    </div>
    
    <div class="test-section">
        <h2>3. Test Map Libraries</h2>
        <div id="map-libs-test">Checking...</div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <script>
        // Test 1: JavaScript Loading
        function testJavaScript() {
            const jsTest = document.getElementById('js-test');
            
            if (typeof jQuery !== 'undefined') {
                jsTest.innerHTML += '<div class="success">✓ jQuery loaded</div>';
            } else {
                jsTest.innerHTML += '<div class="error">✗ jQuery not loaded</div>';
            }
            
            if (typeof L !== 'undefined') {
                jsTest.innerHTML += '<div class="success">✓ Leaflet loaded</div>';
            } else {
                jsTest.innerHTML += '<div class="error">✗ Leaflet not loaded</div>';
            }
        }
        
        // Test 2: GeoJSON Endpoint
        function testGeoJSON() {
            const geojsonTest = document.getElementById('geojson-test');
            geojsonTest.innerHTML = '<div class="info">Testing GeoJSON endpoint...</div>';
            
            // Try different possible URLs
            const urls = [
                '/ordinace-geojson',
                '/ordinace-geojson/',
                window.location.origin + '/ordinace-geojson',
                window.location.origin + '/ordinace-geojson/'
            ];
            
            urls.forEach((url, index) => {
                fetch(url)
                    .then(response => {
                        if (response.ok) {
                            return response.json();
                        }
                        throw new Error(`HTTP ${response.status}`);
                    })
                    .then(data => {
                        geojsonTest.innerHTML += `<div class="success">✓ URL ${index + 1} (${url}): Success - ${data.features ? data.features.length : 0} features</div>`;
                    })
                    .catch(error => {
                        geojsonTest.innerHTML += `<div class="error">✗ URL ${index + 1} (${url}): ${error.message}</div>`;
                    });
            });
        }
        
        // Test 3: Map Libraries
        function testMapLibraries() {
            const mapLibsTest = document.getElementById('map-libs-test');
            
            if (typeof L !== 'undefined') {
                try {
                    // Try to create a simple map
                    const testDiv = document.createElement('div');
                    testDiv.style.height = '100px';
                    testDiv.style.width = '100px';
                    testDiv.style.display = 'none';
                    document.body.appendChild(testDiv);
                    
                    const testMap = L.map(testDiv).setView([50.0755, 14.4378], 13);
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(testMap);
                    
                    mapLibsTest.innerHTML += '<div class="success">✓ Leaflet map creation works</div>';
                    
                    // Clean up
                    testMap.remove();
                    document.body.removeChild(testDiv);
                } catch (error) {
                    mapLibsTest.innerHTML += `<div class="error">✗ Leaflet map creation failed: ${error.message}</div>`;
                }
            } else {
                mapLibsTest.innerHTML += '<div class="error">✗ Leaflet not available</div>';
            }
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            testJavaScript();
            testMapLibraries();
        });
    </script>
</body>
</html>
