<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Vyhledávací Funkcionality</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #2196f3;
        }
        .fix-list {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        
        /* Demo filtry s vyhled<PERSON>váním */
        .demo-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
        }
        .demo-filter-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .demo-filter-item label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #2d3748;
            font-size: 0.9rem;
        }
        .demo-filter-icon {
            width: 18px;
            height: 18px;
            color: #667eea;
        }
        .demo-input, .demo-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
            box-sizing: border-box;
        }
        .demo-input:focus, .demo-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .demo-filter-actions {
            display: flex;
            gap: 12px;
            grid-column: 1 / -1;
            justify-content: center;
            margin-top: 10px;
        }
        .demo-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .demo-button-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        .demo-button-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
        }
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .search-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .search-example {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            background: #f8fafc;
        }
        .search-example h4 {
            margin: 0 0 10px 0;
            color: #2d3748;
        }
        .search-example ul {
            margin: 0;
            padding-left: 20px;
        }
        .search-example li {
            margin: 5px 0;
            color: #4a5568;
        }
        
        .technical-details {
            background: #f7fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin: 20px 0;
        }
        .technical-details h3 {
            margin-top: 0;
            color: #2d3748;
        }
        .technical-details pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Test Vyhledávací Funkcionality</h1>
        
        <div class="test-info">
            <strong>Nová funkcionalita:</strong> Přidáno textové vyhledávání do archivní stránky ordinací.<br>
            <strong>Vyhledává v:</strong> Názvu ordinace, jménech lékařů, adrese, organizaci a čtvrti.
        </div>
        
        <div class="fix-list">
            <h3>✅ Implementované funkce:</h3>
            <ul>
                <li><strong>Textové vyhledávání:</strong> Nové pole pro vyhledávání podle textu</li>
                <li><strong>Rozšířené hledání:</strong> Prohledává title, custom fields a meta data</li>
                <li><strong>Zachování filtrů:</strong> Search parametr se zachovává při stránkování</li>
                <li><strong>Kombinace filtrů:</strong> Vyhledávání funguje společně s filtry typu a čtvrti</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>Demo Rozšířených Filtrů</h2>
            
            <form class="demo-filters">
                <div class="demo-filter-item">
                    <label>
                        <svg class="demo-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                        Vyhledávání
                    </label>
                    <input type="text" class="demo-input" placeholder="Hledat podle názvu, lékaře, adresy..." />
                </div>
                
                <div class="demo-filter-item">
                    <label>
                        <svg class="demo-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z"/>
                        </svg>
                        Typ ordinace
                    </label>
                    <select class="demo-select">
                        <option value="">Všechny typy</option>
                        <option value="pediatr">Pediatr</option>
                        <option value="praktik-dospeli">Praktický lékař</option>
                    </select>
                </div>
                
                <div class="demo-filter-item">
                    <label>
                        <svg class="demo-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                        </svg>
                        Čtvrť
                    </label>
                    <select class="demo-select">
                        <option value="">Všechny čtvrti</option>
                        <option value="Vinohrady">Vinohrady</option>
                        <option value="Vršovice">Vršovice</option>
                        <option value="Strašnice">Strašnice</option>
                        <option value="Záběhlice">Záběhlice</option>
                    </select>
                </div>
                
                <div class="demo-filter-actions">
                    <button type="submit" class="demo-button demo-button-primary">
                        <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                        Vyhledat
                    </button>
                    <button type="reset" class="demo-button demo-button-secondary">
                        <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                        Vymazat
                    </button>
                </div>
            </form>
        </div>
        
        <div class="test-section">
            <h2>Příklady Vyhledávání</h2>
            
            <div class="search-examples">
                <div class="search-example">
                    <h4>🏥 Podle názvu ordinace</h4>
                    <ul>
                        <li>"Dětská ordinace"</li>
                        <li>"Praktická ordinace"</li>
                        <li>"Ambulance"</li>
                    </ul>
                </div>
                
                <div class="search-example">
                    <h4>👨‍⚕️ Podle jména lékaře</h4>
                    <ul>
                        <li>"MUDr. Novák"</li>
                        <li>"Eva Chládková"</li>
                        <li>"Petr"</li>
                    </ul>
                </div>
                
                <div class="search-example">
                    <h4>📍 Podle adresy</h4>
                    <ul>
                        <li>"Wenceslas Square"</li>
                        <li>"Náměstí Míru"</li>
                        <li>"Karlovo náměstí"</li>
                    </ul>
                </div>
                
                <div class="search-example">
                    <h4>🏢 Podle organizace</h4>
                    <ul>
                        <li>"Ordinace MUDr. Novák"</li>
                        <li>"Dětská ordinace"</li>
                        <li>"Poliklinika"</li>
                    </ul>
                </div>
                
                <div class="search-example">
                    <h4>🗺️ Podle čtvrti</h4>
                    <ul>
                        <li>"Vinohrady"</li>
                        <li>"Vršovice"</li>
                        <li>"Záběhlice"</li>
                    </ul>
                </div>
                
                <div class="search-example">
                    <h4>🔗 Kombinace filtrů</h4>
                    <ul>
                        <li>Vyhledávání: "Novák"</li>
                        <li>Typ: "Praktický lékař"</li>
                        <li>Čtvrť: "Vinohrady"</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Technické Detaily</h2>
            
            <div class="technical-details">
                <h3>Implementace vyhledávání</h3>
                <pre><code>// Přidání textového vyhledávání s custom fields
if (!empty($search)) {
    $args['s'] = $search;
    $args['meta_query'] = array(
        'relation' => 'OR',
        array(
            'key' => '_spd10_ordinace_doctors_names',
            'value' => $search,
            'compare' => 'LIKE'
        ),
        array(
            'key' => '_spd10_ordinace_address',
            'value' => $search,
            'compare' => 'LIKE'
        ),
        array(
            'key' => '_spd10_ordinace_organization',
            'value' => $search,
            'compare' => 'LIKE'
        ),
        array(
            'key' => '_spd10_ordinace_quarter',
            'value' => $search,
            'compare' => 'LIKE'
        )
    );
}</code></pre>
            </div>
            
            <h3>Prohledávaná pole:</h3>
            <ul>
                <li><strong>post_title:</strong> Název ordinace (standardní WordPress vyhledávání)</li>
                <li><strong>_spd10_ordinace_doctors_names:</strong> Jména lékařů</li>
                <li><strong>_spd10_ordinace_address:</strong> Adresa ordinace</li>
                <li><strong>_spd10_ordinace_organization:</strong> Název organizace</li>
                <li><strong>_spd10_ordinace_quarter:</strong> Čtvrť</li>
            </ul>
            
            <h3>Zachování parametrů při stránkování:</h3>
            <ul>
                <li>✅ Typ ordinace (typ)</li>
                <li>✅ Čtvrť (ctvrt)</li>
                <li>✅ Vyhledávací text (search)</li>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ Test vyhledávací funkcionality načten');
            console.log('🔍 Nové funkce:');
            console.log('1. Textové vyhledávání v archivní stránce');
            console.log('2. Prohledávání custom fields (lékaři, adresa, organizace)');
            console.log('3. Kombinace s existujícími filtry');
            console.log('4. Zachování parametrů při stránkování');
            
            // Demo funkcionality
            const searchInput = document.querySelector('.demo-input');
            const typeSelect = document.querySelectorAll('.demo-select')[0];
            const quarterSelect = document.querySelectorAll('.demo-select')[1];
            
            function updateDemo() {
                const search = searchInput.value;
                const type = typeSelect.value;
                const quarter = quarterSelect.value;
                
                console.log('Demo filtry:', {
                    search: search || 'prázdné',
                    type: type || 'všechny',
                    quarter: quarter || 'všechny'
                });
            }
            
            searchInput.addEventListener('input', updateDemo);
            typeSelect.addEventListener('change', updateDemo);
            quarterSelect.addEventListener('change', updateDemo);
        });
    </script>
</body>
</html>
