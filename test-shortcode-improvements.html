<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test vylepšení shortcode ordinace_list</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
        }
        .test-section.new {
            border-color: #4caf50;
            background: #f8fff8;
        }
        h1 {
            color: #2d3748;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #4a5568;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        .demo-grid {
            display: grid;
            gap: 20px;
            margin: 20px 0;
        }
        .demo-grid.desktop {
            grid-template-columns: repeat(3, 1fr);
        }
        .demo-grid.tablet {
            grid-template-columns: repeat(2, 1fr);
        }
        .demo-grid.mobile {
            grid-template-columns: 1fr;
        }
        .demo-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-weight: 600;
        }
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        .demo-filters {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            margin: 20px 0;
        }
        .demo-filter-group {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr auto;
            gap: 20px;
            align-items: end;
        }
        .demo-filter-item label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        .demo-filter-icon {
            width: 18px;
            height: 18px;
            color: #667eea;
        }
        .demo-select,
        .demo-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }
        .demo-select:focus,
        .demo-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .demo-filter-actions {
            display: flex;
            gap: 10px;
            flex-direction: column;
        }
        .demo-filter-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.9rem;
        }
        .demo-filter-button-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .demo-filter-button-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .demo-filter-button-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
        }
        .demo-filter-button-secondary:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }
        .demo-button-icon {
            width: 16px;
            height: 16px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .feature-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin: 8px 0;
        }
        .responsive-demo {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
        }
        .responsive-demo h4 {
            margin-top: 0;
            color: #666;
            text-align: center;
        }
        @media (max-width: 1024px) {
            .demo-filter-group {
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
            .demo-filter-actions {
                grid-column: 1 / -1;
                flex-direction: row;
                justify-content: center;
                margin-top: 15px;
            }
        }
        @media (max-width: 767px) {
            .demo-filter-group {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .demo-filter-actions {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Vylepšení shortcode [ordinace_list]</h1>
        
        <div class="test-section new">
            <h2>✨ Nová funkce: Responzivní layout 3,2,1</h2>
            <p><strong>Automatické přizpůsobení počtu sloupců podle šířky obrazovky</strong></p>
            
            <div class="responsive-demo">
                <h4>Desktop (1200px+) - 3 karty</h4>
                <div class="demo-grid desktop">
                    <div class="demo-card">Ordinace 1</div>
                    <div class="demo-card">Ordinace 2</div>
                    <div class="demo-card">Ordinace 3</div>
                </div>
            </div>
            
            <div class="responsive-demo">
                <h4>Tablet (768-1199px) - 2 karty</h4>
                <div class="demo-grid tablet">
                    <div class="demo-card">Ordinace 1</div>
                    <div class="demo-card">Ordinace 2</div>
                </div>
            </div>
            
            <div class="responsive-demo">
                <h4>Mobil (do 767px) - 1 karta</h4>
                <div class="demo-grid mobile">
                    <div class="demo-card">Ordinace 1</div>
                </div>
            </div>
            
            <div class="code-block">
<strong>CSS implementace:</strong>
/* Základní grid s auto-fit */
.spd10-ordinace-list,
.spd10-ordinace-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

/* Responzivní breakpointy */
@media (min-width: 1200px) {
    grid-template-columns: repeat(3, 1fr); /* 3 karty */
}

@media (min-width: 768px) and (max-width: 1199px) {
    grid-template-columns: repeat(2, 1fr); /* 2 karty */
}

@media (max-width: 767px) {
    grid-template-columns: 1fr; /* 1 karta */
}
            </div>
            
            <div class="success">
                ✅ Layout se automaticky přizpůsobuje bez nutnosti JavaScript
            </div>
        </div>
        
        <div class="test-section new">
            <h2>🔍 Nová funkce: Interaktivní filtry v shortcode</h2>
            <p><strong>Parametr show_filters="true" přidá filtry přímo do shortcode</strong></p>
            
            <div class="demo-filters">
                <div class="demo-filter-group">
                    <div class="demo-filter-item">
                        <label>
                            <svg class="demo-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                            </svg>
                            Vyhledávání
                        </label>
                        <input type="text" class="demo-input" placeholder="Hledat podle názvu, lékaře, adresy..." />
                    </div>
                    
                    <div class="demo-filter-item">
                        <label>
                            <svg class="demo-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z"/>
                            </svg>
                            Typ ordinace
                        </label>
                        <select class="demo-select">
                            <option value="">Všechny typy</option>
                            <option value="pediatr">Pediatr</option>
                            <option value="praktik-dospeli">Praktický lékař</option>
                        </select>
                    </div>
                    
                    <div class="demo-filter-item">
                        <label>
                            <svg class="demo-filter-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            Čtvrť
                        </label>
                        <select class="demo-select">
                            <option value="">Všechny čtvrti</option>
                            <option value="Vinohrady">Vinohrady</option>
                            <option value="Vršovice">Vršovice</option>
                            <option value="Strašnice">Strašnice</option>
                        </select>
                    </div>
                    
                    <div class="demo-filter-actions">
                        <button class="demo-filter-button demo-filter-button-primary">
                            <svg class="demo-button-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                            </svg>
                            Filtrovat
                        </button>
                        <a href="#" class="demo-filter-button demo-filter-button-secondary">
                            <svg class="demo-button-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                            Zrušit filtry
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="code-block">
<strong>Použití:</strong>
[ordinace_list show_filters="true"]
[ordinace_list show_filters="true" search="Urbanová"]
[ordinace_list show_filters="true" typ="pediatr" per_page="6"]
            </div>
            
            <div class="feature-list">
                <h4>Funkce filtrů:</h4>
                <ul>
                    <li>✅ Textové vyhledávání (název, lékaři, adresa, organizace, čtvrť)</li>
                    <li>✅ Filtr podle typu ordinace</li>
                    <li>✅ Filtr podle čtvrti</li>
                    <li>✅ Tlačítko "Zrušit filtry"</li>
                    <li>✅ Responzivní layout filtrů</li>
                    <li>✅ Kombinace s parametry shortcode</li>
                </ul>
            </div>
            
            <div class="success">
                ✅ Filtry fungují stejně jako v archive stránce
            </div>
        </div>

        <div class="test-section new">
            <h2>📚 Aktualizovaná dokumentace</h2>
            <p><strong>Kompletní přehled všech parametrů shortcode [ordinace_list]</strong></p>

            <div class="code-block">
<strong>Základní použití:</strong>
[ordinace_list]

<strong>S filtry:</strong>
[ordinace_list typ="pediatr" per_page="10" ctvrt="Vinohrady"]

<strong>S interaktivními filtry:</strong>
[ordinace_list show_filters="true"]

<strong>S předvyplněným vyhledáváním:</strong>
[ordinace_list search="Urbanová" show_filters="true"]

<strong>Kombinace všech parametrů:</strong>
[ordinace_list typ="praktik-dospeli" per_page="6" ctvrt="Vinohrady" style="cards" show_filters="true" search=""]
            </div>

            <div class="feature-list">
                <h4>Všechny parametry:</h4>
                <ul>
                    <li><strong>typ</strong> - Filtr podle typu ordinace (<code>pediatr</code>, <code>praktik-dospeli</code>)</li>
                    <li><strong>per_page</strong> - Počet zobrazených ordinací (výchozí: 10)</li>
                    <li><strong>ctvrt</strong> - Filtr podle čtvrti (např. "Vinohrady")</li>
                    <li><strong>style</strong> - Styl zobrazení (<code>cards</code> nebo <code>simple</code>, výchozí: <code>cards</code>)</li>
                    <li><strong>show_filters</strong> - Zobrazit interaktivní filtry (<code>true</code>/<code>false</code>, výchozí: <code>false</code>) <span class="highlight">NOVÉ</span></li>
                    <li><strong>search</strong> - Předvyplněné textové vyhledávání <span class="highlight">NOVÉ</span></li>
                </ul>
            </div>

            <div class="feature-list">
                <h4>Responzivní layout:</h4>
                <ul>
                    <li><strong>Desktop (1200px+)</strong>: 3 karty vedle sebe</li>
                    <li><strong>Tablet (768-1199px)</strong>: 2 karty vedle sebe</li>
                    <li><strong>Mobil (do 767px)</strong>: 1 karta</li>
                </ul>
            </div>

            <div class="success">
                ✅ Dokumentace v README.md byla aktualizována
            </div>
        </div>

        <div class="test-section">
            <h2>📋 Shrnutí vylepšení</h2>
            <div class="feature-list">
                <h4>Co bylo přidáno:</h4>
                <ul>
                    <li>✅ <strong>Responzivní layout 3,2,1</strong> - automatické přizpůsobení počtu sloupců</li>
                    <li>✅ <strong>Parametr show_filters</strong> - interaktivní filtry v shortcode</li>
                    <li>✅ <strong>Parametr search</strong> - předvyplněné textové vyhledávání</li>
                    <li>✅ <strong>Textové vyhledávání</strong> - funguje stejně jako v archive stránce</li>
                    <li>✅ <strong>Kombinace filtrů</strong> - URL parametry + shortcode parametry</li>
                    <li>✅ <strong>Tlačítko reset filtrů</strong> - pro snadné zrušení filtrů</li>
                    <li>✅ <strong>Responzivní filtry</strong> - přizpůsobení na mobilních zařízeních</li>
                    <li>✅ <strong>Aktualizovaná dokumentace</strong> - kompletní přehled parametrů</li>
                </ul>
            </div>

            <div class="feature-list">
                <h4>Technické detaily:</h4>
                <ul>
                    <li>🔧 <strong>CSS Grid</strong> - moderní responzivní layout</li>
                    <li>🔧 <strong>WordPress hooks</strong> - posts_where filtr pro vyhledávání</li>
                    <li>🔧 <strong>Meta query kombinace</strong> - správné spojování filtrů</li>
                    <li>🔧 <strong>URL parametry</strong> - integrace s GET parametry</li>
                    <li>🔧 <strong>Sanitizace</strong> - bezpečné zpracování vstupů</li>
                    <li>🔧 <strong>Cleanup</strong> - odstranění filtrů po použití</li>
                </ul>
            </div>

            <div class="success">
                🎉 Shortcode [ordinace_list] je nyní plně funkční s moderním responzivním designem a pokročilými filtry!
            </div>
        </div>
    </div>
</body>
</html>
