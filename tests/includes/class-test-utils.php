<?php
/**
 * Test utilities for SPD10 Ordinace Plugin
 */

class SPD10_Ordinace_Test_Utils {
    
    /**
     * Create test ordinace post
     */
    public static function create_test_ordinace($args = array()) {
        $defaults = array(
            'post_title' => 'Test Ordinace',
            'post_type' => 'ordinace',
            'post_status' => 'publish',
            'meta_input' => array(
                'doctors_names' => 'Dr. Test Lékař',
                'organization' => 'Test Ordinace s.r.o.',
                'quarter' => 'Vinohrady',
                'address' => 'Testovací 123, Praha 10',
                'email' => '<EMAIL>',
                'phone' => '+420123456789',
                'website' => 'https://test-ordinace.cz',
                'lat' => '50.0755',
                'lng' => '14.4378',
                'geocode_status' => 'OK',
                'source_row_id' => 'test_' . uniqid(),
                'updated_from_source_at' => current_time('mysql'),
            ),
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $post_id = wp_insert_post($args);
        
        if (!is_wp_error($post_id)) {
            // Set taxonomy term
            wp_set_object_terms($post_id, 'praktik-dospeli', 'ordinace_typ');
        }
        
        return $post_id;
    }
    
    /**
     * Create test CSV data
     */
    public static function get_test_csv_data() {
        return array(
            array(
                'kategorie_ordinace' => 'pediatr',
                'jmena_lekaru' => 'Dr. Jana Nováková',
                'organizace' => 'Dětská ordinace',
                'ctvrt' => 'Vršovice',
                'adresa' => 'Korunní 1234, Praha 10',
                'email' => '<EMAIL>',
                'telefon' => '+420123456789',
                'web' => 'www.ordinace.cz',
            ),
            array(
                'kategorie_ordinace' => 'praktik-dospeli',
                'jmena_lekaru' => 'Dr. Petr Svoboda',
                'organizace' => 'Praktická ordinace',
                'ctvrt' => 'Strašnice',
                'adresa' => 'Průmyslová 567, Praha 10',
                'email' => '<EMAIL>',
                'telefon' => '+420987654321',
                'web' => 'www.prakticka.cz',
            ),
        );
    }
    
    /**
     * Create test paste data
     */
    public static function get_test_paste_data() {
        return "kategorie_ordinace\tjmena_lekaru\torganizace\tctvrt\tadresa\temail\ttelefon\tweb\n" .
               "pediatr\tDr. Jana Nováková\tDětská ordinace\tVršovice\tKorunní 1234, Praha 10\<EMAIL>\t+420123456789\twww.ordinace.cz\n" .
               "praktik-dospeli\tDr. Petr Svoboda\tPraktická ordinace\tStrašnice\tPrůmyslová 567, Praha 10\<EMAIL>\t+420987654321\twww.prakticka.cz";
    }
    
    /**
     * Clean up test data
     */
    public static function cleanup_test_data() {
        // Delete test posts
        $posts = get_posts(array(
            'post_type' => 'ordinace',
            'post_status' => 'any',
            'numberposts' => -1,
            'meta_query' => array(
                array(
                    'key' => 'source_row_id',
                    'value' => 'test_',
                    'compare' => 'LIKE',
                ),
            ),
        ));
        
        foreach ($posts as $post) {
            wp_delete_post($post->ID, true);
        }
        
        // Clean up transients
        delete_transient('spd10_ordinace_test_data');
        
        // Clean up options
        delete_option('spd10_ordinace_test_option');
    }
    
    /**
     * Mock geocoding response
     */
    public static function mock_geocoding_response($success = true) {
        if ($success) {
            return array(
                'lat' => 50.0755,
                'lng' => 14.4378,
                'formatted_address' => 'Testovací 123, Praha 10, Czech Republic',
                'status' => 'OK',
            );
        } else {
            return array(
                'status' => 'ZERO_RESULTS',
                'error' => 'Address not found',
            );
        }
    }
    
    /**
     * Create temporary CSV file
     */
    public static function create_temp_csv_file($data = null) {
        if ($data === null) {
            $data = self::get_test_csv_data();
        }
        
        $temp_file = tempnam(sys_get_temp_dir(), 'spd10_test_');
        $handle = fopen($temp_file, 'w');
        
        // Write header
        if (!empty($data)) {
            fputcsv($handle, array_keys($data[0]));
            
            // Write data rows
            foreach ($data as $row) {
                fputcsv($handle, $row);
            }
        }
        
        fclose($handle);
        return $temp_file;
    }
    
    /**
     * Assert ordinace post has correct meta
     */
    public static function assert_ordinace_meta($post_id, $expected_meta) {
        foreach ($expected_meta as $key => $expected_value) {
            $actual_value = get_post_meta($post_id, $key, true);
            
            if ($actual_value !== $expected_value) {
                throw new Exception(
                    sprintf(
                        'Meta key %s: expected "%s", got "%s"',
                        $key,
                        $expected_value,
                        $actual_value
                    )
                );
            }
        }
    }
}
