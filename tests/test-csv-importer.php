<?php
/**
 * Tests for SPD10_Ordinace_CSV_Importer class
 */

class Test_SPD10_Ordinace_CSV_Importer extends WP_UnitTestCase {
    
    private $csv_importer;
    
    public function setUp(): void {
        parent::setUp();
        
        if (!class_exists('SPD10_Ordinace_CSV_Importer')) {
            $this->markTestSkipped('SPD10_Ordinace_CSV_Importer class not found');
        }
        
        $this->csv_importer = new SPD10_Ordinace_CSV_Importer();
        SPD10_Ordinace_Test_Utils::cleanup_test_data();
    }
    
    public function tearDown(): void {
        SPD10_Ordinace_Test_Utils::cleanup_test_data();
        parent::tearDown();
    }
    
    /**
     * Test CSV file validation
     */
    public function test_csv_validation() {
        $temp_file = SPD10_Ordinace_Test_Utils::create_temp_csv_file();
        
        // Use reflection to access private method
        $reflection = new ReflectionClass($this->csv_importer);
        $method = $reflection->getMethod('validate_csv_file');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->csv_importer, $temp_file);
        
        $this->assertFalse(is_wp_error($result));
        $this->assertArrayHasKey('total_rows', $result);
        $this->assertArrayHasKey('valid_rows', $result);
        $this->assertArrayHasKey('headers', $result);
        $this->assertEquals(2, $result['total_rows']);
        
        unlink($temp_file);
    }
    
    /**
     * Test paste data processing
     */
    public function test_paste_data_processing() {
        $paste_data = SPD10_Ordinace_Test_Utils::get_test_paste_data();
        
        // Use reflection to access private method
        $reflection = new ReflectionClass($this->csv_importer);
        $method = $reflection->getMethod('process_paste_data');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->csv_importer, $paste_data);
        
        $this->assertFalse(is_wp_error($result));
        $this->assertArrayHasKey('detected_format', $result);
        $this->assertArrayHasKey('delimiter', $result);
        $this->assertArrayHasKey('headers', $result);
        $this->assertArrayHasKey('data', $result);
        
        $this->assertEquals("\t", $result['delimiter']);
        $this->assertEquals('Tabulátor (Excel)', $result['detected_format']);
        $this->assertEquals(2, $result['total_rows']);
        $this->assertEquals(2, $result['valid_rows']);
    }
    
    /**
     * Test delimiter detection
     */
    public function test_delimiter_detection() {
        $reflection = new ReflectionClass($this->csv_importer);
        $method = $reflection->getMethod('detect_delimiter');
        $method->setAccessible(true);
        
        // Test tab delimiter
        $tab_data = "col1\tcol2\tcol3\nval1\tval2\tval3";
        $result = $method->invoke($this->csv_importer, $tab_data);
        $this->assertEquals("\t", $result);
        
        // Test comma delimiter
        $comma_data = "col1,col2,col3\nval1,val2,val3";
        $result = $method->invoke($this->csv_importer, $comma_data);
        $this->assertEquals(",", $result);
        
        // Test semicolon delimiter
        $semicolon_data = "col1;col2;col3\nval1;val2;val3";
        $result = $method->invoke($this->csv_importer, $semicolon_data);
        $this->assertEquals(";", $result);
    }
    
    /**
     * Test column mapping auto-detection
     */
    public function test_column_mapping_detection() {
        $headers = array('kategorie_ordinace', 'jmena_lekaru', 'organizace', 'adresa', 'email');
        
        $reflection = new ReflectionClass($this->csv_importer);
        $method = $reflection->getMethod('auto_detect_column_mapping');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->csv_importer, $headers);
        
        $this->assertArrayHasKey('kategorie_ordinace', $result);
        $this->assertArrayHasKey('jmena_lekaru', $result);
        $this->assertArrayHasKey('organizace', $result);
        $this->assertArrayHasKey('adresa', $result);
        $this->assertArrayHasKey('email', $result);
        
        $this->assertEquals('type', $result['kategorie_ordinace']);
        $this->assertEquals('doctors_names', $result['jmena_lekaru']);
        $this->assertEquals('organization', $result['organizace']);
        $this->assertEquals('address', $result['adresa']);
        $this->assertEquals('email', $result['email']);
    }
    
    /**
     * Test paste data import
     */
    public function test_paste_data_import() {
        $paste_data = SPD10_Ordinace_Test_Utils::get_test_paste_data();
        
        // First process the paste data
        $reflection = new ReflectionClass($this->csv_importer);
        $process_method = $reflection->getMethod('process_paste_data');
        $process_method->setAccessible(true);
        
        $processed_data = $process_method->invoke($this->csv_importer, $paste_data);
        $this->assertFalse(is_wp_error($processed_data));
        
        // Then import it
        $import_method = $reflection->getMethod('import_paste_data');
        $import_method->setAccessible(true);
        
        $result = $import_method->invoke($this->csv_importer, $processed_data, true); // dry run
        
        $this->assertFalse(is_wp_error($result));
        $this->assertArrayHasKey('dry_run', $result);
        $this->assertTrue($result['dry_run']);
        $this->assertEquals(2, $result['stats']['processed']);
    }
    
    /**
     * Test invalid paste data
     */
    public function test_invalid_paste_data() {
        $invalid_data = "invalid\tdata\nwithout\theaders";
        
        $reflection = new ReflectionClass($this->csv_importer);
        $method = $reflection->getMethod('process_paste_data');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->csv_importer, $invalid_data);
        
        $this->assertFalse(is_wp_error($result)); // Should still process
        $this->assertNotEmpty($result['errors']); // But should have validation errors
    }
    
    /**
     * Test CSV file size validation
     */
    public function test_file_size_validation() {
        // Create a large test file (simulate)
        $large_data = str_repeat("test,data,row\n", 10000);
        $temp_file = tempnam(sys_get_temp_dir(), 'spd10_large_test_');
        file_put_contents($temp_file, $large_data);
        
        // File should be valid if under limit
        $this->assertLessThan(SPD10_Ordinace_CSV_Importer::MAX_FILE_SIZE, filesize($temp_file));
        
        unlink($temp_file);
    }
    
    /**
     * Test required columns validation
     */
    public function test_required_columns_validation() {
        $data_missing_required = "optional_col1,optional_col2\nval1,val2";
        
        $reflection = new ReflectionClass($this->csv_importer);
        $method = $reflection->getMethod('process_paste_data');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->csv_importer, $data_missing_required);
        
        $this->assertFalse(is_wp_error($result));
        $this->assertNotEmpty($result['errors']);
        
        // Should contain errors about missing required columns
        $error_text = implode(' ', $result['errors']);
        $this->assertStringContainsString('kategorie_ordinace', $error_text);
        $this->assertStringContainsString('jmena_lekaru', $error_text);
        $this->assertStringContainsString('adresa', $error_text);
    }
}
