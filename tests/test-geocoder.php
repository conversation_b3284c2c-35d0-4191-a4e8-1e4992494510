<?php
/**
 * Tests for SPD10_Ordinace_Geocoder class
 */

class Test_SPD10_Ordinace_Geocoder extends WP_UnitTestCase {
    
    private $geocoder;
    
    public function setUp(): void {
        parent::setUp();
        
        if (!class_exists('SPD10_Ordinace_Geocoder')) {
            $this->markTestSkipped('SPD10_Ordinace_Geocoder class not found');
        }
        
        $this->geocoder = new SPD10_Ordinace_Geocoder();
        SPD10_Ordinace_Test_Utils::cleanup_test_data();
    }
    
    public function tearDown(): void {
        SPD10_Ordinace_Test_Utils::cleanup_test_data();
        parent::tearDown();
    }
    
    /**
     * Test geocoder initialization
     */
    public function test_geocoder_initialization() {
        $this->assertInstanceOf('SPD10_Ordinace_Geocoder', $this->geocoder);
    }
    
    /**
     * Test mock geocoding success
     */
    public function test_mock_geocoding_success() {
        // Mock successful geocoding
        add_filter('pre_http_request', function($preempt, $args, $url) {
            if (strpos($url, 'nominatim') !== false) {
                return array(
                    'response' => array('code' => 200),
                    'body' => json_encode(array(
                        array(
                            'lat' => '50.0755',
                            'lon' => '14.4378',
                            'display_name' => 'Testovací 123, Praha 10, Czech Republic',
                        )
                    ))
                );
            }
            return $preempt;
        }, 10, 3);
        
        $result = $this->geocoder->geocode_address('Testovací 123, Praha 10');
        
        $this->assertFalse(is_wp_error($result));
        $this->assertArrayHasKey('lat', $result);
        $this->assertArrayHasKey('lng', $result);
        $this->assertEquals('50.0755', $result['lat']);
        $this->assertEquals('14.4378', $result['lng']);
        
        remove_all_filters('pre_http_request');
    }
    
    /**
     * Test geocoding failure
     */
    public function test_geocoding_failure() {
        // Mock failed geocoding
        add_filter('pre_http_request', function($preempt, $args, $url) {
            if (strpos($url, 'nominatim') !== false) {
                return array(
                    'response' => array('code' => 200),
                    'body' => json_encode(array()) // Empty results
                );
            }
            return $preempt;
        }, 10, 3);
        
        $result = $this->geocoder->geocode_address('Neexistující adresa');
        
        $this->assertTrue(is_wp_error($result));
        $this->assertEquals('no_results', $result->get_error_code());
        
        remove_all_filters('pre_http_request');
    }
    
    /**
     * Test geocoding cache
     */
    public function test_geocoding_cache() {
        if (!class_exists('SPD10_Ordinace_Geocoding_Cache')) {
            $this->markTestSkipped('SPD10_Ordinace_Geocoding_Cache class not found');
        }
        
        $cache = new SPD10_Ordinace_Geocoding_Cache();
        $address = 'Test Address, Praha 10';
        $result = array('lat' => 50.0755, 'lng' => 14.4378);
        
        // Store in cache
        $cache->store($address, $result);
        
        // Retrieve from cache
        $cached_result = $cache->get($address);
        
        $this->assertNotFalse($cached_result);
        $this->assertEquals($result['lat'], $cached_result['lat']);
        $this->assertEquals($result['lng'], $cached_result['lng']);
    }
    
    /**
     * Test rate limiting
     */
    public function test_rate_limiting() {
        // Set a very low rate limit for testing
        update_option('spd10_ordinace_geocoding_rate_limit', 1);
        
        // Mock successful geocoding
        add_filter('pre_http_request', function($preempt, $args, $url) {
            return array(
                'response' => array('code' => 200),
                'body' => json_encode(array(
                    array(
                        'lat' => '50.0755',
                        'lon' => '14.4378',
                        'display_name' => 'Test Address',
                    )
                ))
            );
        }, 10, 3);
        
        // First request should succeed
        $result1 = $this->geocoder->geocode_address('Test Address 1');
        $this->assertFalse(is_wp_error($result1));
        
        // Second request should be rate limited
        $result2 = $this->geocoder->geocode_address('Test Address 2');
        $this->assertTrue(is_wp_error($result2));
        $this->assertEquals('rate_limit_exceeded', $result2->get_error_code());
        
        remove_all_filters('pre_http_request');
        delete_option('spd10_ordinace_geocoding_rate_limit');
    }
    
    /**
     * Test coordinate validation
     */
    public function test_coordinate_validation() {
        $reflection = new ReflectionClass($this->geocoder);
        
        if ($reflection->hasMethod('validate_coordinates')) {
            $method = $reflection->getMethod('validate_coordinates');
            $method->setAccessible(true);
            
            // Valid Prague coordinates
            $this->assertTrue($method->invoke($this->geocoder, 50.0755, 14.4378));
            
            // Invalid coordinates (outside Prague area)
            $this->assertFalse($method->invoke($this->geocoder, 0, 0));
            $this->assertFalse($method->invoke($this->geocoder, 90, 180));
        } else {
            $this->markTestSkipped('validate_coordinates method not found');
        }
    }
    
    /**
     * Test bulk geocoding
     */
    public function test_bulk_geocoding() {
        $addresses = array(
            'Testovací 123, Praha 10',
            'Zkušební 456, Praha 10',
        );
        
        // Mock successful geocoding for both addresses
        add_filter('pre_http_request', function($preempt, $args, $url) {
            static $call_count = 0;
            $call_count++;
            
            if (strpos($url, 'nominatim') !== false) {
                return array(
                    'response' => array('code' => 200),
                    'body' => json_encode(array(
                        array(
                            'lat' => '50.075' . $call_count,
                            'lon' => '14.437' . $call_count,
                            'display_name' => 'Test Address ' . $call_count,
                        )
                    ))
                );
            }
            return $preempt;
        }, 10, 3);
        
        if (method_exists($this->geocoder, 'bulk_geocode')) {
            $results = $this->geocoder->bulk_geocode($addresses);
            
            $this->assertCount(2, $results);
            $this->assertArrayHasKey('Testovací 123, Praha 10', $results);
            $this->assertArrayHasKey('Zkušební 456, Praha 10', $results);
        } else {
            $this->markTestSkipped('bulk_geocode method not found');
        }
        
        remove_all_filters('pre_http_request');
    }
    
    /**
     * Test provider switching
     */
    public function test_provider_switching() {
        // Test with different providers
        $providers = array('nominatim', 'google', 'mapbox');
        
        foreach ($providers as $provider) {
            update_option('spd10_ordinace_geocoding_provider', $provider);
            
            // The geocoder should adapt to the selected provider
            $current_provider = get_option('spd10_ordinace_geocoding_provider');
            $this->assertEquals($provider, $current_provider);
        }
        
        delete_option('spd10_ordinace_geocoding_provider');
    }
}
