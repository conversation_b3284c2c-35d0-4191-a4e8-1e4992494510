<?php
/**
 * Tests for SPD10_Ordinace_Importer class
 */

class Test_SPD10_Ordinace_Importer extends WP_UnitTestCase {
    
    private $importer;
    
    public function setUp(): void {
        parent::setUp();
        
        // Ensure plugin is loaded
        if (!class_exists('SPD10_Ordinace_Importer')) {
            $this->markTestSkipped('SPD10_Ordinace_Importer class not found');
        }
        
        $this->importer = new SPD10_Ordinace_Importer();
        
        // Clean up any existing test data
        SPD10_Ordinace_Test_Utils::cleanup_test_data();
    }
    
    public function tearDown(): void {
        SPD10_Ordinace_Test_Utils::cleanup_test_data();
        parent::tearDown();
    }
    
    /**
     * Test dry run import
     */
    public function test_dry_run_import() {
        $test_data = SPD10_Ordinace_Test_Utils::get_test_csv_data();
        
        $result = $this->importer->import_data($test_data, 'test-sheet', true);
        
        $this->assertFalse(is_wp_error($result));
        $this->assertTrue($result['dry_run']);
        $this->assertEquals(2, $result['stats']['processed']);
        $this->assertEquals(2, $result['stats']['created']);
        $this->assertEquals(0, $result['stats']['updated']);
        
        // Verify no posts were actually created
        $posts = get_posts(array('post_type' => 'ordinace', 'numberposts' => -1));
        $this->assertEquals(0, count($posts));
    }
    
    /**
     * Test actual import
     */
    public function test_actual_import() {
        $test_data = SPD10_Ordinace_Test_Utils::get_test_csv_data();
        
        $result = $this->importer->import_data($test_data, 'test-sheet', false);
        
        $this->assertFalse(is_wp_error($result));
        $this->assertFalse($result['dry_run']);
        $this->assertEquals(2, $result['stats']['processed']);
        $this->assertEquals(2, $result['stats']['created']);
        
        // Verify posts were created
        $posts = get_posts(array('post_type' => 'ordinace', 'numberposts' => -1));
        $this->assertEquals(2, count($posts));
        
        // Check first post
        $post1 = $posts[0];
        $this->assertEquals('Dr. Jana Nováková', get_post_meta($post1->ID, 'doctors_names', true));
        $this->assertEquals('Dětská ordinace', get_post_meta($post1->ID, 'organization', true));
        
        // Check taxonomy
        $terms = wp_get_object_terms($post1->ID, 'ordinace_typ');
        $this->assertNotEmpty($terms);
        $this->assertEquals('pediatr', $terms[0]->slug);
    }
    
    /**
     * Test deduplication
     */
    public function test_deduplication() {
        $test_data = SPD10_Ordinace_Test_Utils::get_test_csv_data();
        
        // First import
        $result1 = $this->importer->import_data($test_data, 'test-sheet', false);
        $this->assertEquals(2, $result1['stats']['created']);
        
        // Second import with same data
        $result2 = $this->importer->import_data($test_data, 'test-sheet', false);
        $this->assertEquals(0, $result2['stats']['created']);
        $this->assertEquals(2, $result2['stats']['updated']);
        
        // Verify still only 2 posts
        $posts = get_posts(array('post_type' => 'ordinace', 'numberposts' => -1));
        $this->assertEquals(2, count($posts));
    }
    
    /**
     * Test column mapping
     */
    public function test_column_mapping() {
        $test_data = array(
            array(
                'typ_ordinace' => 'pediatr', // Different column name
                'doktor' => 'Dr. Test Lékař', // Different column name
                'adresa' => 'Test 123, Praha 10',
            ),
        );
        
        $column_mapping = array(
            'typ_ordinace' => 'type',
            'doktor' => 'doctors_names',
            'adresa' => 'address',
        );
        
        $result = $this->importer->import_data($test_data, 'test-sheet', false, $column_mapping);
        
        $this->assertFalse(is_wp_error($result));
        $this->assertEquals(1, $result['stats']['created']);
        
        $posts = get_posts(array('post_type' => 'ordinace', 'numberposts' => 1));
        $post = $posts[0];
        
        $this->assertEquals('Dr. Test Lékař', get_post_meta($post->ID, 'doctors_names', true));
        $this->assertEquals('Test 123, Praha 10', get_post_meta($post->ID, 'address', true));
    }
    
    /**
     * Test validation errors
     */
    public function test_validation_errors() {
        $invalid_data = array(
            array(
                'kategorie_ordinace' => 'invalid_type',
                'jmena_lekaru' => '',
                'adresa' => '',
                'email' => 'invalid-email',
            ),
        );
        
        $result = $this->importer->import_data($invalid_data, 'test-sheet', true);
        
        $this->assertFalse(is_wp_error($result));
        $this->assertEquals(0, $result['stats']['created']);
        $this->assertNotEmpty($result['stats']['errors']);
    }
    
    /**
     * Test import with geocoding
     */
    public function test_import_with_geocoding() {
        // Mock geocoding
        add_filter('spd10_ordinace_geocode_address', function($address) {
            return SPD10_Ordinace_Test_Utils::mock_geocoding_response(true);
        });
        
        $test_data = array(
            array(
                'kategorie_ordinace' => 'pediatr',
                'jmena_lekaru' => 'Dr. Test Lékař',
                'adresa' => 'Testovací 123, Praha 10',
            ),
        );
        
        $result = $this->importer->import_data($test_data, 'test-sheet', false);
        
        $this->assertFalse(is_wp_error($result));
        $this->assertEquals(1, $result['stats']['created']);
        
        $posts = get_posts(array('post_type' => 'ordinace', 'numberposts' => 1));
        $post = $posts[0];
        
        $this->assertEquals('50.0755', get_post_meta($post->ID, 'lat', true));
        $this->assertEquals('14.4378', get_post_meta($post->ID, 'lng', true));
        $this->assertEquals('OK', get_post_meta($post->ID, 'geocode_status', true));
        
        remove_all_filters('spd10_ordinace_geocode_address');
    }
}
