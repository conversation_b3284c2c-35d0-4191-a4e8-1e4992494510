<?php
/**
 * Integration tests for SPD10 Ordinace Plugin
 */

class Test_SPD10_Ordinace_Integration extends WP_UnitTestCase {
    
    public function setUp(): void {
        parent::setUp();
        SPD10_Ordinace_Test_Utils::cleanup_test_data();
    }
    
    public function tearDown(): void {
        SPD10_Ordinace_Test_Utils::cleanup_test_data();
        parent::tearDown();
    }
    
    /**
     * Test plugin activation
     */
    public function test_plugin_activation() {
        // Plugin should be loaded
        $this->assertTrue(class_exists('SPD10_Ordinace_Plugin'));
        
        // Constants should be defined
        $this->assertTrue(defined('SPD10_ORDINACE_VERSION'));
        $this->assertTrue(defined('SPD10_ORDINACE_PLUGIN_DIR'));
        $this->assertTrue(defined('SPD10_ORDINACE_TEXT_DOMAIN'));
    }
    
    /**
     * Test post type registration
     */
    public function test_post_type_registration() {
        $this->assertTrue(post_type_exists('ordinace'));
        
        $post_type = get_post_type_object('ordinace');
        $this->assertNotNull($post_type);
        $this->assertEquals('ordinace', $post_type->name);
        $this->assertTrue($post_type->public);
    }
    
    /**
     * Test taxonomy registration
     */
    public function test_taxonomy_registration() {
        $this->assertTrue(taxonomy_exists('ordinace_typ'));
        
        $taxonomy = get_taxonomy('ordinace_typ');
        $this->assertNotNull($taxonomy);
        $this->assertEquals('ordinace_typ', $taxonomy->name);
        $this->assertContains('ordinace', $taxonomy->object_type);
    }
    
    /**
     * Test default taxonomy terms
     */
    public function test_default_taxonomy_terms() {
        $terms = get_terms(array(
            'taxonomy' => 'ordinace_typ',
            'hide_empty' => false,
        ));
        
        $this->assertNotEmpty($terms);
        
        $term_slugs = wp_list_pluck($terms, 'slug');
        $this->assertContains('praktik-dospeli', $term_slugs);
        $this->assertContains('pediatr', $term_slugs);
    }
    
    /**
     * Test shortcode registration
     */
    public function test_shortcode_registration() {
        global $shortcode_tags;
        
        $this->assertArrayHasKey('ordinace_list', $shortcode_tags);
        $this->assertArrayHasKey('ordinace_map', $shortcode_tags);
    }
    
    /**
     * Test admin menu registration
     */
    public function test_admin_menu_registration() {
        if (!is_admin()) {
            $this->markTestSkipped('Admin menu test requires admin context');
        }
        
        global $menu, $submenu;
        
        // Check if admin menu items exist
        $found_menu = false;
        foreach ($menu as $menu_item) {
            if (strpos($menu_item[2], 'ordinace') !== false) {
                $found_menu = true;
                break;
            }
        }
        
        $this->assertTrue($found_menu, 'Admin menu for ordinace not found');
    }
    
    /**
     * Test localization loading
     */
    public function test_localization_loading() {
        $this->assertTrue(is_textdomain_loaded('spd10-ordinace'));
        
        // Test a translated string
        $translated = __('Ordinace', 'spd10-ordinace');
        $this->assertNotEmpty($translated);
    }
    
    /**
     * Test complete import workflow
     */
    public function test_complete_import_workflow() {
        if (!class_exists('SPD10_Ordinace_Importer')) {
            $this->markTestSkipped('Importer class not found');
        }
        
        $importer = new SPD10_Ordinace_Importer();
        $test_data = SPD10_Ordinace_Test_Utils::get_test_csv_data();
        
        // 1. Dry run import
        $dry_result = $importer->import_data($test_data, 'test-sheet', true);
        $this->assertFalse(is_wp_error($dry_result));
        $this->assertTrue($dry_result['dry_run']);
        
        // 2. Actual import
        $import_result = $importer->import_data($test_data, 'test-sheet', false);
        $this->assertFalse(is_wp_error($import_result));
        $this->assertFalse($import_result['dry_run']);
        $this->assertEquals(2, $import_result['stats']['created']);
        
        // 3. Verify posts were created
        $posts = get_posts(array('post_type' => 'ordinace', 'numberposts' => -1));
        $this->assertEquals(2, count($posts));
        
        // 4. Test shortcode with imported data
        $shortcode_output = do_shortcode('[ordinace_list]');
        $this->assertStringContainsString('Dr. Jana Nováková', $shortcode_output);
        $this->assertStringContainsString('Dr. Petr Svoboda', $shortcode_output);
    }
    
    /**
     * Test CSV import workflow
     */
    public function test_csv_import_workflow() {
        if (!class_exists('SPD10_Ordinace_CSV_Importer')) {
            $this->markTestSkipped('CSV Importer class not found');
        }
        
        $csv_importer = new SPD10_Ordinace_CSV_Importer();
        $temp_file = SPD10_Ordinace_Test_Utils::create_temp_csv_file();
        
        // Use reflection to test private methods
        $reflection = new ReflectionClass($csv_importer);
        
        // 1. Validate CSV
        $validate_method = $reflection->getMethod('validate_csv_file');
        $validate_method->setAccessible(true);
        $validation_result = $validate_method->invoke($csv_importer, $temp_file);
        
        $this->assertFalse(is_wp_error($validation_result));
        $this->assertEquals(2, $validation_result['total_rows']);
        
        // 2. Import CSV
        $import_method = $reflection->getMethod('import_csv_data');
        $import_method->setAccessible(true);
        $import_result = $import_method->invoke($csv_importer, $temp_file, false);
        
        $this->assertFalse(is_wp_error($import_result));
        $this->assertEquals(2, $import_result['stats']['created']);
        
        unlink($temp_file);
    }
    
    /**
     * Test paste import workflow
     */
    public function test_paste_import_workflow() {
        if (!class_exists('SPD10_Ordinace_CSV_Importer')) {
            $this->markTestSkipped('CSV Importer class not found');
        }
        
        $csv_importer = new SPD10_Ordinace_CSV_Importer();
        $paste_data = SPD10_Ordinace_Test_Utils::get_test_paste_data();
        
        $reflection = new ReflectionClass($csv_importer);
        
        // 1. Process paste data
        $process_method = $reflection->getMethod('process_paste_data');
        $process_method->setAccessible(true);
        $processed_data = $process_method->invoke($csv_importer, $paste_data);
        
        $this->assertFalse(is_wp_error($processed_data));
        $this->assertEquals(2, $processed_data['total_rows']);
        $this->assertEquals("\t", $processed_data['delimiter']);
        
        // 2. Import paste data
        $import_method = $reflection->getMethod('import_paste_data');
        $import_method->setAccessible(true);
        $import_result = $import_method->invoke($csv_importer, $processed_data, false);
        
        $this->assertFalse(is_wp_error($import_result));
        $this->assertEquals(2, $import_result['stats']['created']);
    }
    
    /**
     * Test geocoding integration
     */
    public function test_geocoding_integration() {
        if (!class_exists('SPD10_Ordinace_Geocoder')) {
            $this->markTestSkipped('Geocoder class not found');
        }
        
        // Create ordinace post
        $post_id = SPD10_Ordinace_Test_Utils::create_test_ordinace();
        
        // Mock geocoding
        add_filter('pre_http_request', function($preempt, $args, $url) {
            return array(
                'response' => array('code' => 200),
                'body' => json_encode(array(
                    array(
                        'lat' => '50.0755',
                        'lon' => '14.4378',
                        'display_name' => 'Test Address',
                    )
                ))
            );
        }, 10, 3);
        
        $geocoder = new SPD10_Ordinace_Geocoder();
        $result = $geocoder->geocode_address('Test Address');
        
        $this->assertFalse(is_wp_error($result));
        $this->assertArrayHasKey('lat', $result);
        $this->assertArrayHasKey('lng', $result);
        
        remove_all_filters('pre_http_request');
    }
    
    /**
     * Test compatibility with detske-skupiny plugin
     */
    public function test_detske_skupiny_compatibility() {
        // Check that our plugin doesn't interfere with detske-skupiny
        
        // 1. Check function name conflicts
        $our_functions = get_defined_functions()['user'];
        $conflicting_functions = array_filter($our_functions, function($func) {
            return strpos($func, 'spd10_ordinace_') === 0 && 
                   function_exists(str_replace('spd10_ordinace_', 'spd10_ds_', $func));
        });
        
        $this->assertEmpty($conflicting_functions, 'Function name conflicts detected');
        
        // 2. Check option key conflicts
        global $wpdb;
        $our_options = $wpdb->get_col(
            "SELECT option_name FROM {$wpdb->options} WHERE option_name LIKE 'spd10_ordinace_%'"
        );
        
        foreach ($our_options as $option) {
            $ds_option = str_replace('spd10_ordinace_', 'spd10_ds_', $option);
            $this->assertFalse(get_option($ds_option, false) !== false, 
                "Option conflict detected: {$option} vs {$ds_option}");
        }
        
        // 3. Check CSS class conflicts
        $shortcode_output = do_shortcode('[ordinace_list]');
        $this->assertStringNotContainsString('spd10-ds-', $shortcode_output);
        $this->assertStringContainsString('spd10-ordinace-', $shortcode_output);
    }
}
