<?php
/**
 * Tests for SPD10 Ordinace shortcodes
 */

class Test_SPD10_Ordinace_Shortcodes extends WP_UnitTestCase {
    
    public function setUp(): void {
        parent::setUp();
        SPD10_Ordinace_Test_Utils::cleanup_test_data();
        
        // Create test posts
        $this->create_test_ordinaces();
    }
    
    public function tearDown(): void {
        SPD10_Ordinace_Test_Utils::cleanup_test_data();
        parent::tearDown();
    }
    
    /**
     * Create test ordinace posts
     */
    private function create_test_ordinaces() {
        // Create pediatr ordinace
        $pediatr_id = SPD10_Ordinace_Test_Utils::create_test_ordinace(array(
            'post_title' => 'Pediatrická ordinace',
            'meta_input' => array(
                'doctors_names' => 'Dr. <PERSON>',
                'organization' => 'Dětská ordinace',
                'quarter' => 'Vinohrady',
                'address' => 'Korunní 123, Praha 10',
            ),
        ));
        wp_set_object_terms($pediatr_id, 'pediatr', 'ordinace_typ');
        
        // Create praktik ordinace
        $praktik_id = SPD10_Ordinace_Test_Utils::create_test_ordinace(array(
            'post_title' => 'Praktická ordinace',
            'meta_input' => array(
                'doctors_names' => 'Dr. Petr Svoboda',
                'organization' => 'Praktická ordinace',
                'quarter' => 'Strašnice',
                'address' => 'Průmyslová 456, Praha 10',
            ),
        ));
        wp_set_object_terms($praktik_id, 'praktik-dospeli', 'ordinace_typ');
    }
    
    /**
     * Test ordinace_list shortcode basic functionality
     */
    public function test_ordinace_list_shortcode_basic() {
        $output = do_shortcode('[ordinace_list]');
        
        $this->assertNotEmpty($output);
        $this->assertStringContainsString('spd10-ordinace-list', $output);
        $this->assertStringContainsString('Dr. Jana Nováková', $output);
        $this->assertStringContainsString('Dr. Petr Svoboda', $output);
    }
    
    /**
     * Test ordinace_list shortcode with type filter
     */
    public function test_ordinace_list_shortcode_type_filter() {
        // Test pediatr filter
        $output = do_shortcode('[ordinace_list typ="pediatr"]');
        
        $this->assertNotEmpty($output);
        $this->assertStringContainsString('Dr. Jana Nováková', $output);
        $this->assertStringNotContainsString('Dr. Petr Svoboda', $output);
        
        // Test praktik filter
        $output = do_shortcode('[ordinace_list typ="praktik-dospeli"]');
        
        $this->assertNotEmpty($output);
        $this->assertStringNotContainsString('Dr. Jana Nováková', $output);
        $this->assertStringContainsString('Dr. Petr Svoboda', $output);
    }
    
    /**
     * Test ordinace_list shortcode with quarter filter
     */
    public function test_ordinace_list_shortcode_quarter_filter() {
        $output = do_shortcode('[ordinace_list ctvrt="Vinohrady"]');
        
        $this->assertNotEmpty($output);
        $this->assertStringContainsString('Dr. Jana Nováková', $output);
        $this->assertStringNotContainsString('Dr. Petr Svoboda', $output);
    }
    
    /**
     * Test ordinace_list shortcode with per_page parameter
     */
    public function test_ordinace_list_shortcode_per_page() {
        $output = do_shortcode('[ordinace_list per_page="1"]');
        
        $this->assertNotEmpty($output);
        
        // Should contain only one ordinace
        $doctor_count = substr_count($output, 'spd10-ordinace-card');
        $this->assertEquals(1, $doctor_count);
    }
    
    /**
     * Test ordinace_list shortcode with style parameter
     */
    public function test_ordinace_list_shortcode_style() {
        // Test cards style
        $output = do_shortcode('[ordinace_list style="cards"]');
        $this->assertStringContainsString('spd10-ordinace-cards', $output);
        
        // Test list style
        $output = do_shortcode('[ordinace_list style="list"]');
        $this->assertStringContainsString('spd10-ordinace-simple-list', $output);
    }
    
    /**
     * Test ordinace_map shortcode
     */
    public function test_ordinace_map_shortcode() {
        $output = do_shortcode('[ordinace_map]');
        
        $this->assertNotEmpty($output);
        $this->assertStringContainsString('spd10-ordinace-map-container', $output);
        $this->assertStringContainsString('leaflet', $output);
    }
    
    /**
     * Test ordinace_map shortcode with parameters
     */
    public function test_ordinace_map_shortcode_with_params() {
        $output = do_shortcode('[ordinace_map height="500px" typ="pediatr"]');
        
        $this->assertNotEmpty($output);
        $this->assertStringContainsString('height: 500px', $output);
        $this->assertStringContainsString('spd10-ordinace-map-container', $output);
    }
    
    /**
     * Test shortcode with no results
     */
    public function test_shortcode_no_results() {
        $output = do_shortcode('[ordinace_list typ="neexistujici-typ"]');
        
        $this->assertNotEmpty($output);
        $this->assertStringContainsString('Žádné ordinace', $output);
    }
    
    /**
     * Test shortcode caching
     */
    public function test_shortcode_caching() {
        // First call
        $start_time = microtime(true);
        $output1 = do_shortcode('[ordinace_list]');
        $first_call_time = microtime(true) - $start_time;
        
        // Second call (should be cached)
        $start_time = microtime(true);
        $output2 = do_shortcode('[ordinace_list]');
        $second_call_time = microtime(true) - $start_time;
        
        $this->assertEquals($output1, $output2);
        // Second call should be faster (cached)
        $this->assertLessThan($first_call_time, $second_call_time);
    }
    
    /**
     * Test shortcode with invalid parameters
     */
    public function test_shortcode_invalid_parameters() {
        // Invalid per_page
        $output = do_shortcode('[ordinace_list per_page="invalid"]');
        $this->assertNotEmpty($output);

        // Invalid style
        $output = do_shortcode('[ordinace_list style="invalid"]');
        $this->assertNotEmpty($output);
        $this->assertStringContainsString('spd10-ordinace-cards', $output); // Should fallback to default
    }

    /**
     * Test ordinace_list shortcode with show_filters parameter
     */
    public function test_ordinace_list_shortcode_show_filters() {
        // Test with filters disabled (default)
        $output = do_shortcode('[ordinace_list]');
        $this->assertStringNotContainsString('spd10-ordinace-shortcode-filters', $output);

        // Test with filters enabled
        $output = do_shortcode('[ordinace_list show_filters="true"]');
        $this->assertStringContainsString('spd10-ordinace-shortcode-filters', $output);
        $this->assertStringContainsString('Vyhledávání', $output);
        $this->assertStringContainsString('Typ ordinace', $output);
        $this->assertStringContainsString('Čtvrť', $output);
    }

    /**
     * Test ordinace_list shortcode with search parameter
     */
    public function test_ordinace_list_shortcode_search() {
        $output = do_shortcode('[ordinace_list search="Jana"]');

        $this->assertNotEmpty($output);
        $this->assertStringContainsString('Dr. Jana Nováková', $output);
        $this->assertStringNotContainsString('Dr. Petr Svoboda', $output);
    }

    /**
     * Test ordinace_list shortcode with combined filters and search
     */
    public function test_ordinace_list_shortcode_combined_filters() {
        $output = do_shortcode('[ordinace_list typ="pediatr" search="Jana" show_filters="true"]');

        $this->assertNotEmpty($output);
        $this->assertStringContainsString('spd10-ordinace-shortcode-filters', $output);
        $this->assertStringContainsString('Dr. Jana Nováková', $output);
    }
    
    /**
     * Test shortcode HTML structure
     */
    public function test_shortcode_html_structure() {
        $output = do_shortcode('[ordinace_list]');
        
        // Check for proper HTML structure
        $this->assertStringContainsString('<div class="spd10-ordinace-list', $output);
        $this->assertStringContainsString('<div class="spd10-ordinace-card', $output);
        $this->assertStringContainsString('<h3 class="ordinace-title">', $output);
        $this->assertStringContainsString('<div class="ordinace-meta">', $output);
        
        // Check for proper escaping
        $this->assertStringNotContainsString('<script', $output);
        $this->assertStringNotContainsString('javascript:', $output);
    }
    
    /**
     * Test shortcode accessibility
     */
    public function test_shortcode_accessibility() {
        $output = do_shortcode('[ordinace_list]');
        
        // Check for accessibility attributes
        $this->assertStringContainsString('role=', $output);
        $this->assertStringContainsString('aria-', $output);
        
        // Check for proper heading structure
        $this->assertStringContainsString('<h3', $output);
    }
    
    /**
     * Test shortcode responsive design
     */
    public function test_shortcode_responsive_design() {
        $output = do_shortcode('[ordinace_list]');
        
        // Check for responsive CSS classes
        $this->assertStringContainsString('spd10-ordinace-responsive', $output);
        
        // Check for mobile-friendly structure
        $this->assertStringContainsString('viewport', $output);
    }
}
